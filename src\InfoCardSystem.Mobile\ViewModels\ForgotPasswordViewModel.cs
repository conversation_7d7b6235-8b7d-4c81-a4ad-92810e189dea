using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 忘记密码页面ViewModel
/// </summary>
public partial class ForgotPasswordViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;

    public ForgotPasswordViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        ILogger<ForgotPasswordViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        Title = "忘记密码";
    }

    [ObservableProperty]
    [Required(ErrorMessage = "请输入邮箱或手机号")]
    private string emailOrPhone = string.Empty;

    [ObservableProperty]
    private bool isNotBusy = true;

    [ObservableProperty]
    private bool isSuccess;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    /// <summary>
    /// 发送重置链接命令
    /// </summary>
    [RelayCommand]
    private async Task SendResetLinkAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            // 验证输入
            if (!ValidateInput())
                return;

            _logger.LogInformation("发送密码重置请求: {EmailOrPhone}", EmailOrPhone);

            var request = new ForgotPasswordRequest
            {
                EmailOrPhone = EmailOrPhone.Trim()
            };

            var response = await _authService.ForgotPasswordAsync(request);

            if (response.Success)
            {
                IsSuccess = true;
                ClearError();
                
                _logger.LogInformation("密码重置请求发送成功: {EmailOrPhone}", EmailOrPhone);
                
                // 3秒后自动返回登录页面
                _ = Task.Delay(3000).ContinueWith(async _ =>
                {
                    await NavigateToAsync("login");
                });
            }
            else
            {
                IsSuccess = false;
                await ShowErrorAsync("发送失败", response.Message);
                _logger.LogWarning("密码重置请求失败: {EmailOrPhone}, 错误: {Error}", EmailOrPhone, response.Message);
            }
        }, "发送重置链接");
    }

    /// <summary>
    /// 返回登录命令
    /// </summary>
    [RelayCommand]
    private async Task BackToLoginAsync()
    {
        await NavigateToAsync("login");
    }

    /// <summary>
    /// 联系客服命令
    /// </summary>
    [RelayCommand]
    private async Task ContactSupportAsync()
    {
        await ShowToastAsync("客服功能即将推出");
    }

    /// <summary>
    /// 验证输入
    /// </summary>
    private bool ValidateInput()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(EmailOrPhone))
        {
            errors.Add("请输入邮箱或手机号");
        }
        else if (!IsValidEmailOrPhone(EmailOrPhone))
        {
            errors.Add("请输入有效的邮箱地址或手机号");
        }

        if (errors.Any())
        {
            ErrorMessage = string.Join("\n", errors);
            HasError = true;
            IsSuccess = false;
            return false;
        }

        ClearError();
        return true;
    }

    /// <summary>
    /// 验证邮箱或手机号格式
    /// </summary>
    private static bool IsValidEmailOrPhone(string input)
    {
        // 验证邮箱格式
        var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
        if (Regex.IsMatch(input, emailPattern))
        {
            return true;
        }

        // 验证手机号格式
        var phonePattern = @"^1[3-9]\d{9}$";
        if (Regex.IsMatch(input, phonePattern))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }
}
