# InfoCard Web应用

## 📋 项目简介

InfoCard Web应用是一个基于Blazor Server的现代化资讯分享平台，提供用户认证、资讯卡管理、好友系统和群组功能。

## ✨ 主要特性

### 🔐 用户认证系统
- 手机号+密码登录
- 用户注册和资料管理
- JWT令牌认证
- 自动登录状态维护

### 📱 现代化界面
- 响应式设计，支持桌面和移动设备
- Bootstrap 5 UI框架
- 实时Toast通知
- 流畅的用户交互体验

### 🔗 完整API集成
- RESTful API通信
- 统一错误处理
- 自动认证头管理
- 请求超时和重试机制

### 🎨 组件化架构
- Blazor Server组件
- 依赖注入容器
- 服务层抽象
- 状态管理服务

## 🚀 快速开始

### 环境要求
- .NET 9.0 SDK
- Visual Studio 2022 或 VS Code
- 现代浏览器（Chrome、Firefox、Edge、Safari）

### 安装和运行
```bash
# 1. 克隆项目
git clone <repository-url>
cd InfoCardSystem

# 2. 还原依赖
dotnet restore

# 3. 启动Web应用
cd src/InfoCardSystem.Web
dotnet run

# 4. 访问应用
# 浏览器打开: https://localhost:5001
```

### 配置API连接
```json
// appsettings.json
{
  "ApiSettings": {
    "BaseUrl": "https://localhost:7001/"
  }
}
```

## 📁 项目结构

```
src/InfoCardSystem.Web/
├── Components/
│   ├── Layout/
│   │   └── MainLayout.razor      # 主布局
│   └── Pages/
│       ├── Home.razor            # 主页
│       ├── Login.razor           # 登录页
│       ├── Register.razor        # 注册页
│       └── Demo.razor            # 演示页
├── Services/
│   ├── IApiService.cs            # API服务接口
│   ├── ApiService.cs             # API服务实现
│   └── AuthStateService.cs       # 认证状态管理
├── wwwroot/                      # 静态资源
├── Program.cs                    # 应用入口
└── appsettings.json             # 配置文件
```

## 🔧 核心技术

### 前端技术栈
- **Blazor Server** - .NET 9.0 Web框架
- **Bootstrap 5** - CSS框架
- **Font Awesome** - 图标库
- **SignalR** - 实时通信

### 状态管理
- **Blazored.LocalStorage** - 本地存储
- **AuthStateService** - 认证状态管理
- **依赖注入** - 服务容器

### HTTP通信
- **HttpClient** - HTTP客户端
- **JSON序列化** - System.Text.Json
- **JWT认证** - Bearer Token

## 🎯 主要功能

### 用户认证
- [x] 用户登录/注册
- [x] JWT令牌管理
- [x] 自动登录检查
- [x] 登出功能

### 资讯卡管理
- [x] 资讯卡列表展示
- [x] 分页导航
- [x] 收藏功能
- [ ] 创建/编辑资讯卡
- [ ] 转发功能

### 好友系统
- [ ] 好友列表
- [ ] 添加好友
- [ ] 好友请求处理
- [ ] 用户搜索

### 群组功能
- [ ] 群组列表
- [ ] 创建群组
- [ ] 加入/离开群组
- [ ] 群组成员管理

## 🌐 页面导航

| 页面 | 路由 | 功能描述 |
|------|------|----------|
| 主页 | `/` | 资讯卡动态展示 |
| 登录 | `/login` | 用户登录 |
| 注册 | `/register` | 用户注册 |
| 演示 | `/demo` | 功能演示页面 |

## 🔒 安全特性

- **HTTPS通信** - 所有API请求使用HTTPS
- **JWT认证** - 安全的令牌认证机制
- **输入验证** - 客户端和服务端双重验证
- **XSS防护** - 自动HTML编码
- **CSRF防护** - Anti-forgery令牌

## 📊 性能优化

- **响应压缩** - Gzip压缩
- **静态文件缓存** - 浏览器缓存
- **组件优化** - 合理的组件生命周期
- **懒加载** - 按需加载资源

## 🛠️ 开发指南

### 添加新页面
1. 在`Components/Pages/`目录创建`.razor`文件
2. 添加`@page`指令定义路由
3. 注入必要的服务
4. 实现页面逻辑

### 添加新服务
1. 定义服务接口
2. 实现服务类
3. 在`Program.cs`中注册服务
4. 在组件中注入使用

### 调试技巧
- 使用浏览器开发者工具检查网络请求
- 在Visual Studio中设置断点调试
- 查看控制台输出的日志信息

## 📚 文档资源

- [开发者指南](../../docs/web-developer-guide.md)
- [API集成指南](../../docs/web-api-integration-guide.md)
- [部署指南](../../docs/web-deployment-guide.md)
- [项目架构文档](../../docs/project-structure.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建Issue报告问题
- 参考文档寻找解决方案
- 联系开发团队获取支持

---

> **注意**: 这是一个正在开发中的项目，某些功能可能尚未完全实现。请查看项目状态和路线图了解最新进展。
