using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 好友页面ViewModel
/// </summary>
public partial class FriendsViewModel : BaseViewModel
{
    private readonly IInfoCardApiClient _apiClient;

    public FriendsViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IInfoCardApiClient apiClient,
        ILogger<FriendsViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _apiClient = apiClient;
        Title = "好友";
        
        Friends = new ObservableCollection<FriendViewModel>();
        
        // 初始化加载好友列表
        _ = LoadFriendsAsync();
    }

    [ObservableProperty]
    private string searchQuery = string.Empty;

    [ObservableProperty]
    private ObservableCollection<FriendViewModel> friends;

    [ObservableProperty]
    private int totalFriends;

    [ObservableProperty]
    private int onlineFriends;

    [ObservableProperty]
    private int pendingRequests;

    partial void OnSearchQueryChanged(string value)
    {
        FilterFriends();
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            IsRefreshing = true;
            await LoadFriendsAsync();
        }, "刷新", false);
    }

    /// <summary>
    /// 添加好友命令
    /// </summary>
    [RelayCommand]
    private async Task AddFriendAsync()
    {
        await NavigateToAsync("addfriend");
    }

    /// <summary>
    /// 好友请求命令
    /// </summary>
    [RelayCommand]
    private async Task FriendRequestsAsync()
    {
        await NavigateToAsync("friendrequests");
    }

    /// <summary>
    /// 群组命令
    /// </summary>
    [RelayCommand]
    private async Task GroupsAsync()
    {
        await NavigateToAsync("groups");
    }

    /// <summary>
    /// 与好友聊天命令
    /// </summary>
    [RelayCommand]
    private async Task ChatWithFriendAsync(FriendViewModel friend)
    {
        if (friend == null) return;

        await ShowToastAsync($"与 {friend.DisplayName ?? friend.Username} 聊天功能即将推出");
    }

    /// <summary>
    /// 加载好友列表
    /// </summary>
    private async Task LoadFriendsAsync()
    {
        try
        {
            _logger.LogDebug("加载好友列表");

            // 模拟好友数据
            var mockFriends = GenerateMockFriends();
            
            Friends.Clear();
            foreach (var friend in mockFriends)
            {
                Friends.Add(friend);
            }

            // 更新统计信息
            UpdateStatistics();

            _logger.LogInformation("好友列表加载完成: {Count} 个好友", Friends.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载好友列表失败");
            await ShowErrorAsync("加载失败", "无法加载好友列表，请重试");
        }
        finally
        {
            IsRefreshing = false;
        }
    }

    /// <summary>
    /// 筛选好友
    /// </summary>
    private void FilterFriends()
    {
        // 这里可以实现搜索筛选逻辑
        // 暂时不实现，保持所有好友显示
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        TotalFriends = Friends.Count;
        OnlineFriends = Friends.Count(f => f.IsOnline);
        PendingRequests = 3; // 模拟数据
    }

    /// <summary>
    /// 生成模拟好友数据
    /// </summary>
    private List<FriendViewModel> GenerateMockFriends()
    {
        var friends = new List<FriendViewModel>();
        
        for (int i = 1; i <= 8; i++)
        {
            friends.Add(new FriendViewModel
            {
                Id = i,
                Username = $"friend_{i}",
                DisplayName = $"好友 {i}",
                Alias = i % 3 == 0 ? $"别名{i}" : null,
                AvatarUrl = "default_avatar.png",
                IsOnline = i % 2 == 0,
                LastActiveText = i % 2 == 0 ? "在线" : $"{i}小时前",
                CreatedAt = DateTime.Now.AddDays(-i * 10)
            });
        }

        return friends;
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        
        if (!Friends.Any())
        {
            await LoadFriendsAsync();
        }
    }
}

/// <summary>
/// 好友ViewModel
/// </summary>
public class FriendViewModel
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? Alias { get; set; }
    public string AvatarUrl { get; set; } = string.Empty;
    public bool IsOnline { get; set; }
    public string LastActiveText { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}
