@echo off
echo Setting up IIS reverse proxy...

echo Creating API reverse proxy web.config...
echo ^<?xml version="1.0" encoding="utf-8"?^> > "C:\Webs\ICAPI\web.config"
echo ^<configuration^> >> "C:\Webs\ICAPI\web.config"
echo   ^<system.webServer^> >> "C:\Webs\ICAPI\web.config"
echo     ^<rewrite^> >> "C:\Webs\ICAPI\web.config"
echo       ^<rules^> >> "C:\Webs\ICAPI\web.config"
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^> >> "C:\Webs\ICAPI\web.config"
echo           ^<match url="(.*)" /^> >> "C:\Webs\ICAPI\web.config"
echo           ^<action type="Rewrite" url="http://localhost:5000/{R:1}" /^> >> "C:\Webs\ICAPI\web.config"
echo         ^</rule^> >> "C:\Webs\ICAPI\web.config"
echo       ^</rules^> >> "C:\Webs\ICAPI\web.config"
echo     ^</rewrite^> >> "C:\Webs\ICAPI\web.config"
echo   ^</system.webServer^> >> "C:\Webs\ICAPI\web.config"
echo ^</configuration^> >> "C:\Webs\ICAPI\web.config"

echo Creating Web reverse proxy web.config...
echo ^<?xml version="1.0" encoding="utf-8"?^> > "C:\Webs\ICWeb\web.config"
echo ^<configuration^> >> "C:\Webs\ICWeb\web.config"
echo   ^<system.webServer^> >> "C:\Webs\ICWeb\web.config"
echo     ^<rewrite^> >> "C:\Webs\ICWeb\web.config"
echo       ^<rules^> >> "C:\Webs\ICWeb\web.config"
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^> >> "C:\Webs\ICWeb\web.config"
echo           ^<match url="(.*)" /^> >> "C:\Webs\ICWeb\web.config"
echo           ^<action type="Rewrite" url="http://localhost:7000/{R:1}" /^> >> "C:\Webs\ICWeb\web.config"
echo         ^</rule^> >> "C:\Webs\ICWeb\web.config"
echo       ^</rules^> >> "C:\Webs\ICWeb\web.config"
echo     ^</rewrite^> >> "C:\Webs\ICWeb\web.config"
echo   ^</system.webServer^> >> "C:\Webs\ICWeb\web.config"
echo ^</configuration^> >> "C:\Webs\ICWeb\web.config"

echo Reverse proxy configuration completed!
echo.
echo Note: Make sure development services are running:
echo API service: http://localhost:5000
echo Web service: http://localhost:7000
