/*
 * InfoCard Web应用程序启动配置
 *
 * 功能说明：
 * - 配置Blazor Server应用程序
 * - 注册依赖注入服务
 * - 配置HTTP客户端
 * - 设置认证和授权
 * - 配置中间件管道
 *
 * 主要组件：
 * - Blazor Server组件
 * - 本地存储服务
 * - Toast通知服务
 * - HTTP客户端服务
 * - 认证状态管理
 */

using InfoCardSystem.Web.Components;
using InfoCardSystem.Web.Services;
using Blazored.LocalStorage;
using Blazored.Toast;

var builder = WebApplication.CreateBuilder(args);

// ==================== 服务注册 ====================

// 添加Blazor Server组件支持
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// 添加本地存储支持 - 用于JWT令牌持久化
builder.Services.AddBlazoredLocalStorage();

// 添加Toast通知支持 - 用于用户反馈
builder.Services.AddBlazoredToast();

// 配置HTTP客户端 - 用于与后端API通信
builder.Services.AddHttpClient<IApiService, ApiService>(client =>
{
    // 从配置中读取API基础URL，默认为本地开发环境
    var apiBaseUrl = builder.Configuration.GetValue<string>("ApiSettings:BaseUrl") ?? "http://localhost:8081/";
    client.BaseAddress = new Uri(apiBaseUrl);
    client.Timeout = TimeSpan.FromSeconds(30);

    // 设置默认请求头
    client.DefaultRequestHeaders.Add("User-Agent", "InfoCard-Web/1.0");
});

// 注册自定义服务
builder.Services.AddScoped<IApiService, ApiService>();           // API通信服务
builder.Services.AddScoped<AuthStateService>();                  // 认证状态管理服务

// 添加认证支持 - JWT Bearer令牌认证
builder.Services.AddAuthentication()
    .AddJwtBearer();

// 添加授权支持 - 基于角色和策略的授权
builder.Services.AddAuthorizationCore();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();


app.UseAntiforgery();

app.UseStaticFiles();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
