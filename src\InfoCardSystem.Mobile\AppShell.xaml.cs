using InfoCardSystem.Mobile.Views;

namespace InfoCardSystem.Mobile;

public partial class AppShell : Shell
{
    public AppShell()
    {
        InitializeComponent();
        RegisterRoutes();
    }

    private void RegisterRoutes()
    {
        // 认证相关页面
        Routing.RegisterRoute("login", typeof(LoginPage));
        Routing.RegisterRoute("register", typeof(RegisterPage));
        Routing.RegisterRoute("forgotpassword", typeof(ForgotPasswordPage));

        // 主要功能页面
        Routing.RegisterRoute("profile", typeof(ProfilePage));
        // Routing.RegisterRoute("editprofile", typeof(EditProfilePage)); // 待实现
        // Routing.RegisterRoute("changepassword", typeof(ChangePasswordPage)); // 待实现

        // 好友相关页面
        // Routing.RegisterRoute("frienddetail", typeof(FriendDetailPage)); // 待实现
        Routing.RegisterRoute("addfriend", typeof(AddFriendPage));
        // Routing.RegisterRoute("friendrequests", typeof(FriendRequestsPage)); // 待实现

        // 群组相关页面
        // Routing.RegisterRoute("groupdetail", typeof(GroupDetailPage)); // 待实现
        // Routing.RegisterRoute("creategroup", typeof(CreateGroupPage)); // 待实现
        // Routing.RegisterRoute("groupmembers", typeof(GroupMembersPage)); // 待实现

        // 资讯卡相关页面
        // Routing.RegisterRoute("infocarddetail", typeof(InfoCardDetailPage)); // 待实现
        Routing.RegisterRoute("createinfocard", typeof(CreateInfoCardPage));
        // Routing.RegisterRoute("editinfocard", typeof(EditInfoCardPage)); // 待实现

        // 设置页面
        Routing.RegisterRoute("settings", typeof(SettingsPage));
        Routing.RegisterRoute("about", typeof(AboutPage));

        // 添加好友相关页面
        // Routing.RegisterRoute("searchusers", typeof(SearchUsersPage)); // 待实现
        // Routing.RegisterRoute("qrscanner", typeof(QRScannerPage)); // 待实现
        Routing.RegisterRoute("myqrcode", typeof(MyQRCodePage));
        // Routing.RegisterRoute("contactsmatch", typeof(ContactsMatchPage)); // 待实现
    }
}
