<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0-android</TargetFrameworks>
        <OutputType>Exe</OutputType>
        <RootNamespace>InfoCardSystem.Mobile</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <!-- Display Version -->
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <ApplicationVersion>1</ApplicationVersion>

        <!-- Android Specific -->
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>

        <!-- App Info -->
        <ApplicationTitle>InfoCard</ApplicationTitle>
        <ApplicationId>com.infocardsystem.mobile</ApplicationId>
        <ApplicationIdGuid>C2482DA0-9DBE-4FCA-8C75-51317DDC8558</ApplicationIdGuid>
    </PropertyGroup>

    <ItemGroup>
        <!-- App Icon -->
        <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#1976D2" />

        <!-- Splash Screen -->
        <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#1976D2" BaseSize="128,128" />

        <!-- Images -->
        <MauiImage Include="Resources\Images\*" />

        <!-- Custom Fonts -->
        <MauiFont Include="Resources\Fonts\*" />

        <!-- Raw Assets -->
        <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
    </ItemGroup>

    <ItemGroup>
        <!-- MAUI Core -->
        <PackageReference Include="Microsoft.Maui.Controls" Version="8.0.91" />
        <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.91" />

        <!-- MVVM Toolkit -->
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />

        <!-- HTTP Client -->
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
        <PackageReference Include="System.Text.Json" Version="8.0.4" />

        <!-- Local Database -->
        <PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
        <PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.11" />

        <!-- Logging -->
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />

        <!-- JWT Token Handling -->
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.1.0" />
    </ItemGroup>

</Project>
