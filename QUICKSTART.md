# InfoCardSystem 快速开始指南

## 🚀 5分钟快速启动

### 前置要求
- Visual Studio 2022 (17.8+)
- .NET 9.0 SDK
- MySQL 8.0+

### 1. 克隆项目
```bash
git clone <repository-url>
cd InfoCardSystem
```

### 2. 配置数据库
1. 创建MySQL数据库 `infinitycircle`
2. 更新连接字符串：
   - `src/InfoCardSystem.API/appsettings.json`
   - `src/InfoCardSystem.Web/appsettings.json`

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=infinitycircle;Uid=root;Pwd=your_password;"
  }
}
```

### 3. 在Visual Studio 2022中打开
1. 双击 `InfoCardSystem.sln`
2. 等待NuGet包还原完成
3. 设置多个启动项目：
   - 右键解决方案 → 属性
   - 选择"多个启动项目"
   - 设置 `InfoCardSystem.API` 和 `InfoCardSystem.Web` 为"启动"

### 4. 运行项目
1. 按 F5 启动调试
2. API服务：https://localhost:7001
3. Web应用：https://localhost:7000

### 5. 验证安装
- 访问 https://localhost:7001/swagger 查看API文档
- 访问 https://localhost:7000 使用Web应用
- 注册新用户并测试功能

## 📚 更多文档
- [完整安装指南](docs/installation/README.md)
- [部署指南](docs/deployment/README.md)
- [API文档](docs/api/README.md)

## 🆘 遇到问题？
查看 [故障排除文档](docs/troubleshooting/) 或提交Issue。
