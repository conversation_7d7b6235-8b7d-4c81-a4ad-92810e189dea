using CommunityToolkit.Mvvm.ComponentModel;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 基础ViewModel类
/// </summary>
public abstract partial class BaseViewModel : ObservableValidator
{
    protected readonly INavigationService _navigationService;
    protected readonly IDialogService _dialogService;
    protected readonly ILogger _logger;

    protected BaseViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        ILogger logger)
    {
        _navigationService = navigationService;
        _dialogService = dialogService;
        _logger = logger;
    }

    [ObservableProperty]
    private bool isBusy;

    /// <summary>
    /// IsBusy属性变化时的处理方法
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    partial void OnIsBusyChanged(bool value)
    {
        // 基类默认实现，子类可以重写此方法来处理IsBusy状态变化
        // 例如：更新UI状态、显示/隐藏加载指示器等

        // 通知子类IsBusy状态变化
        OnIsBusyChangedCore(value);
    }

    /// <summary>
    /// IsBusy状态变化的核心处理方法，子类可以重写
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected virtual void OnIsBusyChangedCore(bool value)
    {
        // 基类默认实现为空，子类可以重写
    }

    [ObservableProperty]
    private string title = string.Empty;

    [ObservableProperty]
    private bool isRefreshing;

    [ObservableProperty]
    private bool hasError;

    [ObservableProperty]
    private string errorMessage = string.Empty;

    /// <summary>
    /// 导航到指定页面
    /// </summary>
    /// <param name="route">路由</param>
    /// <param name="parameters">参数</param>
    protected async Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null)
    {
        try
        {
            await _navigationService.NavigateToAsync(route, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导航失败: {Route}", route);
            await ShowErrorAsync("导航失败", "页面跳转失败，请重试");
        }
    }

    /// <summary>
    /// 返回上一页
    /// </summary>
    protected async Task GoBackAsync()
    {
        try
        {
            await _navigationService.GoBackAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "返回失败");
            await ShowErrorAsync("操作失败", "返回失败，请重试");
        }
    }

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    protected async Task ShowErrorAsync(string title, string message)
    {
        try
        {
            HasError = true;
            ErrorMessage = message;
            await _dialogService.ShowErrorAsync(title, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示错误对话框失败");
        }
    }

    /// <summary>
    /// 显示成功消息
    /// </summary>
    /// <param name="message">消息</param>
    protected async Task ShowSuccessAsync(string message)
    {
        try
        {
            await _dialogService.ShowSuccessAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示成功消息失败");
        }
    }

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    /// <returns>用户选择结果</returns>
    protected async Task<bool> ShowConfirmAsync(string title, string message)
    {
        try
        {
            return await _dialogService.ShowConfirmAsync(title, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示确认对话框失败");
            return false;
        }
    }

    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="ex">异常</param>
    /// <param name="operation">操作名称</param>
    protected async Task HandleExceptionAsync(Exception ex, string operation = "操作")
    {
        _logger.LogError(ex, "{Operation} 失败", operation);
        
        var message = ex switch
        {
            HttpRequestException => "网络连接失败，请检查网络设置",
            TaskCanceledException => "请求超时，请重试",
            UnauthorizedAccessException => "登录已过期，请重新登录",
            _ => "发生未知错误，请重试"
        };

        await ShowErrorAsync($"{operation}失败", message);
    }

    /// <summary>
    /// 清除错误状态
    /// </summary>
    protected void ClearError()
    {
        HasError = false;
        ErrorMessage = string.Empty;
    }

    /// <summary>
    /// 执行安全操作
    /// </summary>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="showLoading">是否显示加载状态</param>
    protected async Task ExecuteSafelyAsync(Func<Task> operation, string operationName = "操作", bool showLoading = true)
    {
        if (IsBusy) return;

        try
        {
            if (showLoading) IsBusy = true;
            ClearError();
            
            await operation();
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(ex, operationName);
        }
        finally
        {
            if (showLoading) IsBusy = false;
        }
    }

    /// <summary>
    /// 执行安全操作并返回结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="showLoading">是否显示加载状态</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteSafelyAsync<T>(Func<Task<T>> operation, string operationName = "操作", bool showLoading = true)
    {
        if (IsBusy) return default;

        try
        {
            if (showLoading) IsBusy = true;
            ClearError();
            
            return await operation();
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(ex, operationName);
            return default;
        }
        finally
        {
            if (showLoading) IsBusy = false;
        }
    }

    // 生命周期方法
    public virtual Task OnAppearingAsync() => Task.CompletedTask;
    public virtual Task OnDisappearingAsync() => Task.CompletedTask;
    public virtual Task OnNavigatedToAsync(IDictionary<string, object> parameters) => Task.CompletedTask;
    public virtual Task OnNavigatedFromAsync() => Task.CompletedTask;
}
