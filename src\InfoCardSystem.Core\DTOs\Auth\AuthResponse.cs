namespace InfoCardSystem.Core.DTOs.Auth;

/// <summary>
/// 认证响应
/// </summary>
public class AuthResponse
{
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;
    
    /// <summary>
    /// 令牌类型
    /// </summary>
    public string TokenType { get; set; } = "Bearer";
    
    /// <summary>
    /// 过期时间（秒）
    /// </summary>
    public int ExpiresIn { get; set; }
    
    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? RefreshToken { get; set; }
    
    /// <summary>
    /// 用户信息
    /// </summary>
    public UserInfo User { get; set; } = null!;
}

/// <summary>
/// 用户信息
/// </summary>
public class UserInfo
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 自定义用户ID
    /// </summary>
    public string CustomUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    public string? Bio { get; set; }
    
    /// <summary>
    /// 用户状态
    /// </summary>
    public string UserStatus { get; set; } = string.Empty;
    
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
