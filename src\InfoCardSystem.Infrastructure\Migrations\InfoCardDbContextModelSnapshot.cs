﻿// <auto-generated />
using System;
using InfoCardSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace InfoCardSystem.Infrastructure.Migrations
{
    [DbContext(typeof(InfoCardDbContext))]
    partial class InfoCardDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("InfoCardSystem.Core.Entities.AppUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AvatarUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Bio")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CustomUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UserStatus")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CustomUserId")
                        .IsUnique()
                        .HasDatabaseName("idx_users_custom_user_id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("idx_users_email");

                    b.HasIndex("Phone")
                        .IsUnique()
                        .HasDatabaseName("idx_users_phone");

                    b.HasIndex("Username")
                        .HasDatabaseName("idx_users_username");

                    b.ToTable("app_users", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UploaderId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_attachments_created");

                    b.HasIndex("FileName")
                        .HasDatabaseName("idx_attachments_filename");

                    b.HasIndex("UploaderId")
                        .HasDatabaseName("idx_attachments_uploader");

                    b.ToTable("user_attachments", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserBlacklist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BlockedUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlockedUserId")
                        .HasDatabaseName("idx_blacklist_blocked_user");

                    b.HasIndex("UserId", "BlockedUserId")
                        .IsUnique()
                        .HasDatabaseName("idx_blacklist_user_blocked");

                    b.ToTable("user_blacklist", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserFriendship", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FriendAlias")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("FriendId")
                        .HasColumnType("int");

                    b.Property<int>("FriendshipStatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FriendId");

                    b.HasIndex("FriendshipStatus")
                        .HasDatabaseName("idx_friendships_status");

                    b.HasIndex("UserId", "FriendId")
                        .IsUnique()
                        .HasDatabaseName("idx_friendships_user_friend");

                    b.ToTable("user_friendships", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AvatarUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("CreatorId")
                        .HasColumnType("int");

                    b.Property<string>("GroupDescription")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CreatorId")
                        .HasDatabaseName("idx_groups_creator");

                    b.HasIndex("GroupName")
                        .HasDatabaseName("idx_groups_name");

                    b.ToTable("user_groups", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserGroupMember", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("GroupId")
                        .HasColumnType("int");

                    b.Property<DateTime>("JoinedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("idx_group_members_user");

                    b.HasIndex("GroupId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("idx_group_members_group_user");

                    b.ToTable("user_group_members", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllowForward")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("DirectPublisherId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("InfoCardStatus")
                        .HasColumnType("int");

                    b.Property<int>("InfoCardType")
                        .HasColumnType("int");

                    b.Property<int>("OriginalPublisherId")
                        .HasColumnType("int");

                    b.Property<int?>("ParentInfoCardId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_infocards_created");

                    b.HasIndex("DirectPublisherId")
                        .HasDatabaseName("idx_infocards_direct_publisher");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_infocards_expires");

                    b.HasIndex("OriginalPublisherId")
                        .HasDatabaseName("idx_infocards_original_publisher");

                    b.HasIndex("ParentInfoCardId");

                    b.HasIndex("InfoCardType", "InfoCardStatus")
                        .HasDatabaseName("idx_infocards_type_status");

                    b.ToTable("user_infocards", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AttachmentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("InfoCardId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("AttachmentId")
                        .HasDatabaseName("idx_infocard_attachments_attachment");

                    b.HasIndex("InfoCardId", "AttachmentId")
                        .IsUnique()
                        .HasDatabaseName("idx_infocard_attachments_infocard_attachment");

                    b.ToTable("user_infocard_attachments", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardFavorite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("InfoCardId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("idx_favorites_user");

                    b.HasIndex("InfoCardId", "UserId")
                        .IsUnique()
                        .HasDatabaseName("idx_favorites_infocard_user");

                    b.ToTable("user_infocard_favorites", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("InfoCardId")
                        .HasColumnType("int");

                    b.Property<bool>("IsRead")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsVisible")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("ReceivedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("RecipientId")
                        .HasColumnType("int");

                    b.Property<int>("RecipientType")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("InfoCardId")
                        .HasDatabaseName("idx_recipients_infocard");

                    b.HasIndex("RecipientId", "RecipientType")
                        .HasDatabaseName("idx_recipients_recipient");

                    b.HasIndex("RecipientId", "RecipientType", "IsVisible")
                        .HasDatabaseName("idx_recipients_visible");

                    b.ToTable("user_infocard_recipients", (string)null);
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserAttachment", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "Uploader")
                        .WithMany("UploadedAttachments")
                        .HasForeignKey("UploaderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Uploader");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserBlacklist", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "BlockedUser")
                        .WithMany("BlockedByUsers")
                        .HasForeignKey("BlockedUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "User")
                        .WithMany("BlockedUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BlockedUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserFriendship", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "Friend")
                        .WithMany("ReceivedFriendships")
                        .HasForeignKey("FriendId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "User")
                        .WithMany("InitiatedFriendships")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Friend");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserGroup", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "Creator")
                        .WithMany("CreatedGroups")
                        .HasForeignKey("CreatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Creator");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserGroupMember", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.UserGroup", "Group")
                        .WithMany("Members")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "User")
                        .WithMany("GroupMemberships")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCard", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "DirectPublisher")
                        .WithMany("DirectInfoCards")
                        .HasForeignKey("DirectPublisherId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "OriginalPublisher")
                        .WithMany("OriginalInfoCards")
                        .HasForeignKey("OriginalPublisherId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.UserInfoCard", "ParentInfoCard")
                        .WithMany("ChildInfoCards")
                        .HasForeignKey("ParentInfoCardId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("DirectPublisher");

                    b.Navigation("OriginalPublisher");

                    b.Navigation("ParentInfoCard");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardAttachment", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.UserAttachment", "Attachment")
                        .WithMany("InfoCardAttachments")
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.UserInfoCard", "InfoCard")
                        .WithMany("Attachments")
                        .HasForeignKey("InfoCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attachment");

                    b.Navigation("InfoCard");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardFavorite", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.UserInfoCard", "InfoCard")
                        .WithMany("Favorites")
                        .HasForeignKey("InfoCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "User")
                        .WithMany("FavoriteInfoCards")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InfoCard");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCardRecipient", b =>
                {
                    b.HasOne("InfoCardSystem.Core.Entities.UserInfoCard", "InfoCard")
                        .WithMany("Recipients")
                        .HasForeignKey("InfoCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InfoCardSystem.Core.Entities.AppUser", "RecipientUser")
                        .WithMany()
                        .HasForeignKey("RecipientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InfoCardRecipients_Users");

                    b.HasOne("InfoCardSystem.Core.Entities.UserGroup", "RecipientGroup")
                        .WithMany("InfoCardRecipients")
                        .HasForeignKey("RecipientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InfoCard");

                    b.Navigation("RecipientGroup");

                    b.Navigation("RecipientUser");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.AppUser", b =>
                {
                    b.Navigation("BlockedByUsers");

                    b.Navigation("BlockedUsers");

                    b.Navigation("CreatedGroups");

                    b.Navigation("DirectInfoCards");

                    b.Navigation("FavoriteInfoCards");

                    b.Navigation("GroupMemberships");

                    b.Navigation("InitiatedFriendships");

                    b.Navigation("OriginalInfoCards");

                    b.Navigation("ReceivedFriendships");

                    b.Navigation("UploadedAttachments");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserAttachment", b =>
                {
                    b.Navigation("InfoCardAttachments");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserGroup", b =>
                {
                    b.Navigation("InfoCardRecipients");

                    b.Navigation("Members");
                });

            modelBuilder.Entity("InfoCardSystem.Core.Entities.UserInfoCard", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("ChildInfoCards");

                    b.Navigation("Favorites");

                    b.Navigation("Recipients");
                });
#pragma warning restore 612, 618
        }
    }
}
