using System.Text.Json.Serialization;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// API响应基类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }
    
    /// <summary>
    /// 数据
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }
    
    /// <summary>
    /// 消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("error")]
    public ErrorInfo? Error { get; set; }
    
    /// <summary>
    /// 是否成功（兼容性属性）
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => Success;
    
    /// <summary>
    /// 错误消息（兼容性属性）
    /// </summary>
    [JsonIgnore]
    public string? ErrorMessage => Error?.Message ?? (Success ? null : Message);
    
    /// <summary>
    /// 状态码（兼容性属性）
    /// </summary>
    [JsonIgnore]
    public int StatusCode { get; set; } = 200;
    
    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse<T> SuccessResult(T data, string message = "操作成功")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message,
            StatusCode = 200
        };
    }
    

    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误码</param>
    /// <param name="details">错误详情</param>
    /// <returns>失败响应</returns>
    public static ApiResponse<T> ErrorResult(string message, string? errorCode = null, object? details = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            StatusCode = 400,
            Error = new ErrorInfo
            {
                Code = errorCode ?? "UNKNOWN_ERROR",
                Message = message,
                Details = details
            }
        };
    }
    

}

/// <summary>
/// 无数据的API响应
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse SuccessResult(string message = "操作成功")
    {
        return new ApiResponse
        {
            Success = true,
            Message = message,
            StatusCode = 200
        };
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误码</param>
    /// <param name="details">错误详情</param>
    /// <returns>失败响应</returns>
    public static new ApiResponse ErrorResult(string message, string? errorCode = null, object? details = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            StatusCode = 400,
            Error = new ErrorInfo
            {
                Code = errorCode ?? "UNKNOWN_ERROR",
                Message = message,
                Details = details
            }
        };
    }
}

/// <summary>
/// 错误信息
/// </summary>
public class ErrorInfo
{
    /// <summary>
    /// 错误码
    /// </summary>
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误详情
    /// </summary>
    [JsonPropertyName("details")]
    public object? Details { get; set; }
}
