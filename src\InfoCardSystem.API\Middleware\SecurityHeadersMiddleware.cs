namespace InfoCardSystem.API.Middleware;

/// <summary>
/// 安全头中间件
/// </summary>
public class SecurityHeadersMiddleware
{
    private readonly RequestDelegate _next;

    public SecurityHeadersMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 添加安全响应头
        AddSecurityHeaders(context.Response);
        
        await _next(context);
    }

    private static void AddSecurityHeaders(HttpResponse response)
    {
        // 防止点击劫持
        response.Headers["X-Frame-Options"] = "DENY";

        // 防止MIME类型嗅探
        response.Headers["X-Content-Type-Options"] = "nosniff";

        // XSS保护
        response.Headers["X-XSS-Protection"] = "1; mode=block";

        // 引用者策略
        response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

        // 内容安全策略
        response.Headers["Content-Security-Policy"] =
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
            "style-src 'self' 'unsafe-inline'; " +
            "img-src 'self' data: https:; " +
            "font-src 'self'; " +
            "connect-src 'self'; " +
            "frame-ancestors 'none'";

        // 严格传输安全（仅HTTPS）
        if (response.HttpContext.Request.IsHttps)
        {
            response.Headers["Strict-Transport-Security"] =
                "max-age=31536000; includeSubDomains; preload";
        }

        // 权限策略
        response.Headers["Permissions-Policy"] =
            "camera=(), microphone=(), geolocation=(), payment=()";

        // 移除服务器信息
        response.Headers.Remove("Server");
        response.Headers.Remove("X-Powered-By");
        response.Headers.Remove("X-AspNet-Version");
        response.Headers.Remove("X-AspNetMvc-Version");
    }
}

/// <summary>
/// 安全头中间件扩展
/// </summary>
public static class SecurityHeadersMiddlewareExtensions
{
    public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<SecurityHeadersMiddleware>();
    }
}
