using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 创建资讯卡页面ViewModel
/// </summary>
public partial class CreateInfoCardViewModel : BaseViewModel
{
    private readonly IInfoCardApiClient _apiClient;

    public CreateInfoCardViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IInfoCardApiClient apiClient,
        ILogger<CreateInfoCardViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _apiClient = apiClient;
        Title = "创建资讯卡";
        
        Attachments = new ObservableCollection<AttachmentViewModel>();
        
        // 默认分享给所有好友
        ShareToAll = true;
    }

    [ObservableProperty]
    private string title = string.Empty;

    [ObservableProperty]
    private string content = string.Empty;

    [ObservableProperty]
    private ObservableCollection<AttachmentViewModel> attachments;

    [ObservableProperty]
    private bool shareToAll = true;

    [ObservableProperty]
    private bool shareToSelected;

    [ObservableProperty]
    private bool shareToGroups;

    [ObservableProperty]
    private string selectedRecipientsText = string.Empty;

    [ObservableProperty]
    private bool showPreview;

    [ObservableProperty]
    private bool isNotBusy = true;

    [ObservableProperty]
    private bool canPublish;

    [ObservableProperty]
    private string? previewUrl;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    partial void OnTitleChanged(string value)
    {
        UpdateCanPublish();
    }

    partial void OnContentChanged(string value)
    {
        UpdateCanPublish();
    }

    /// <summary>
    /// 拍照命令
    /// </summary>
    [RelayCommand]
    private async Task TakePhotoAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("启动相机拍照");

            // 模拟拍照
            await Task.Delay(1000);
            
            var attachment = new AttachmentViewModel();
            attachment.Id = Attachments.Count + 1;
            attachment.FileName = $"photo_{DateTime.Now:yyyyMMdd_HHmmss}.jpg";
            attachment.ContentType = "image/jpeg";
            
            Attachments.Add(attachment);
            
            await ShowSuccessAsync("照片添加成功");
            
            _logger.LogInformation("照片添加成功: {FileName}", attachment.FileName);
        }, "拍照", false);
    }

    /// <summary>
    /// 选择照片命令
    /// </summary>
    [RelayCommand]
    private async Task PickPhotoAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("从相册选择照片");

            // 模拟选择照片
            await Task.Delay(500);
            
            var attachment = new AttachmentViewModel();
            attachment.Id = Attachments.Count + 1;
            attachment.FileName = $"gallery_{DateTime.Now:yyyyMMdd_HHmmss}.jpg";
            attachment.ContentType = "image/jpeg";
            
            Attachments.Add(attachment);
            
            await ShowSuccessAsync("照片添加成功");
            
            _logger.LogInformation("照片添加成功: {FileName}", attachment.FileName);
        }, "选择照片", false);
    }

    /// <summary>
    /// 选择文件命令
    /// </summary>
    [RelayCommand]
    private async Task PickFileAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("选择文件");

            // 模拟选择文件
            await Task.Delay(500);
            
            var attachment = new AttachmentViewModel();
            attachment.Id = Attachments.Count + 1;
            attachment.FileName = $"document_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            attachment.ContentType = "application/pdf";
            
            Attachments.Add(attachment);
            
            await ShowSuccessAsync("文件添加成功");
            
            _logger.LogInformation("文件添加成功: {FileName}", attachment.FileName);
        }, "选择文件", false);
    }

    /// <summary>
    /// 移除附件命令
    /// </summary>
    [RelayCommand]
    private async Task RemoveAttachmentAsync(AttachmentViewModel attachment)
    {
        if (attachment == null) return;

        var confirmed = await ShowConfirmAsync("删除附件", "确定要删除这个附件吗？");
        if (confirmed)
        {
            Attachments.Remove(attachment);
            _logger.LogInformation("附件删除成功: {FileName}", attachment.FileName);
        }
    }

    /// <summary>
    /// 选择好友命令
    /// </summary>
    [RelayCommand]
    private async Task SelectFriendsAsync()
    {
        await ShowToastAsync("选择好友功能即将推出");
        
        // 模拟选择结果
        SelectedRecipientsText = "已选择 3 位好友";
    }

    /// <summary>
    /// 选择群组命令
    /// </summary>
    [RelayCommand]
    private async Task SelectGroupsAsync()
    {
        await ShowToastAsync("选择群组功能即将推出");
        
        // 模拟选择结果
        SelectedRecipientsText = "已选择 2 个群组";
    }

    /// <summary>
    /// 切换预览命令
    /// </summary>
    [RelayCommand]
    private void TogglePreview()
    {
        ShowPreview = !ShowPreview;
        _logger.LogDebug("预览模式: {ShowPreview}", ShowPreview);
    }

    /// <summary>
    /// 保存草稿命令
    /// </summary>
    [RelayCommand]
    private async Task SaveDraftAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("保存草稿");

            // 模拟保存草稿
            await Task.Delay(1000);
            
            await ShowSuccessAsync("草稿保存成功");
            
            _logger.LogInformation("草稿保存成功");
        }, "保存草稿");
    }

    /// <summary>
    /// 发布命令
    /// </summary>
    [RelayCommand]
    private async Task PublishAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            if (!ValidateInput())
                return;

            _logger.LogInformation("发布资讯卡");

            // 模拟发布
            await Task.Delay(2000);
            
            await ShowSuccessAsync("资讯卡发布成功！");
            
            // 返回主页
            await NavigateToAsync("//main");
            
            _logger.LogInformation("资讯卡发布成功");
        }, "发布资讯卡");
    }

    /// <summary>
    /// 验证输入
    /// </summary>
    private bool ValidateInput()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Content))
        {
            errors.Add("请输入资讯卡内容");
        }

        if (Content.Length > 1000)
        {
            errors.Add("内容长度不能超过1000个字符");
        }

        if (Title.Length > 100)
        {
            errors.Add("标题长度不能超过100个字符");
        }

        if (errors.Any())
        {
            ErrorMessage = string.Join("\n", errors);
            HasError = true;
            return false;
        }

        ClearError();
        return true;
    }

    /// <summary>
    /// 更新发布按钮状态
    /// </summary>
    private void UpdateCanPublish()
    {
        CanPublish = !string.IsNullOrWhiteSpace(Content) && !IsBusy;
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }

    public override Task OnAppearingAsync()
    {
        UpdateCanPublish();
        return base.OnAppearingAsync();
    }
}
