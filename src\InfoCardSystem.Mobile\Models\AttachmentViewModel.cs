using CommunityToolkit.Mvvm.ComponentModel;

namespace InfoCardSystem.Mobile.Models;

/// <summary>
/// 附件视图模型
/// 用于在UI中展示和管理附件信息
/// </summary>
/// <remarks>
/// 此类用于在创建资讯卡时管理附件，包括图片、文档等文件。
/// 提供了附件的基本信息、预览URL和上传状态等功能。
/// </remarks>
public partial class AttachmentViewModel : ObservableObject
{
    /// <summary>
    /// 附件ID
    /// </summary>
    [ObservableProperty]
    private int id;

    /// <summary>
    /// 文件名
    /// </summary>
    [ObservableProperty]
    private string fileName = string.Empty;

    /// <summary>
    /// 文件内容类型（MIME类型）
    /// </summary>
    [ObservableProperty]
    private string contentType = string.Empty;

    /// <summary>
    /// 预览URL或本地路径
    /// </summary>
    [ObservableProperty]
    private string previewUrl = string.Empty;

    /// <summary>
    /// 本地文件路径
    /// </summary>
    [ObservableProperty]
    private string localPath = string.Empty;

    /// <summary>
    /// 服务器URL（上传后）
    /// </summary>
    [ObservableProperty]
    private string serverUrl = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    [ObservableProperty]
    private long fileSize;

    /// <summary>
    /// 是否正在上传
    /// </summary>
    [ObservableProperty]
    private bool isUploading;

    /// <summary>
    /// 上传进度（0-100）
    /// </summary>
    [ObservableProperty]
    private int uploadProgress;

    /// <summary>
    /// 是否上传成功
    /// </summary>
    [ObservableProperty]
    private bool isUploaded;

    /// <summary>
    /// 上传错误消息
    /// </summary>
    [ObservableProperty]
    private string? uploadError;

    /// <summary>
    /// 是否为图片类型
    /// </summary>
    public bool IsImage => ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 是否为视频类型
    /// </summary>
    public bool IsVideo => ContentType.StartsWith("video/", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// 是否为文档类型
    /// </summary>
    public bool IsDocument => !IsImage && !IsVideo;

    /// <summary>
    /// 格式化的文件大小
    /// </summary>
    public string FormattedFileSize
    {
        get
        {
            if (FileSize == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = FileSize;
            int order = 0;

            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }

            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public string FileExtension
    {
        get
        {
            if (string.IsNullOrEmpty(FileName))
                return string.Empty;

            var lastDot = FileName.LastIndexOf('.');
            return lastDot >= 0 ? FileName.Substring(lastDot) : string.Empty;
        }
    }

    /// <summary>
    /// 显示名称（不含扩展名）
    /// </summary>
    public string DisplayName
    {
        get
        {
            if (string.IsNullOrEmpty(FileName))
                return string.Empty;

            var lastDot = FileName.LastIndexOf('.');
            return lastDot >= 0 ? FileName.Substring(0, lastDot) : FileName;
        }
    }

    /// <summary>
    /// 上传状态文本
    /// </summary>
    public string UploadStatusText
    {
        get
        {
            if (IsUploading)
                return $"上传中... {UploadProgress}%";
            if (IsUploaded)
                return "上传完成";
            if (!string.IsNullOrEmpty(UploadError))
                return $"上传失败: {UploadError}";
            return "等待上传";
        }
    }

    /// <summary>
    /// 重置上传状态
    /// </summary>
    public void ResetUploadStatus()
    {
        IsUploading = false;
        UploadProgress = 0;
        IsUploaded = false;
        UploadError = null;
        ServerUrl = string.Empty;
    }

    /// <summary>
    /// 开始上传
    /// </summary>
    public void StartUpload()
    {
        IsUploading = true;
        UploadProgress = 0;
        IsUploaded = false;
        UploadError = null;
    }

    /// <summary>
    /// 更新上传进度
    /// </summary>
    /// <param name="progress">进度百分比（0-100）</param>
    public void UpdateUploadProgress(int progress)
    {
        UploadProgress = Math.Max(0, Math.Min(100, progress));
    }

    /// <summary>
    /// 完成上传
    /// </summary>
    /// <param name="serverUrl">服务器文件URL</param>
    public void CompleteUpload(string serverUrl)
    {
        IsUploading = false;
        UploadProgress = 100;
        IsUploaded = true;
        ServerUrl = serverUrl;
        UploadError = null;
    }

    /// <summary>
    /// 上传失败
    /// </summary>
    /// <param name="error">错误消息</param>
    public void FailUpload(string error)
    {
        IsUploading = false;
        IsUploaded = false;
        UploadError = error;
    }

    /// <summary>
    /// 创建图片附件
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="localPath">本地路径</param>
    /// <param name="fileSize">文件大小</param>
    /// <returns>图片附件实例</returns>
    public static AttachmentViewModel CreateImage(string fileName, string localPath, long fileSize = 0)
    {
        return new AttachmentViewModel
        {
            FileName = fileName,
            LocalPath = localPath,
            PreviewUrl = localPath,
            ContentType = GetImageContentType(fileName),
            FileSize = fileSize
        };
    }

    /// <summary>
    /// 创建文档附件
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="localPath">本地路径</param>
    /// <param name="fileSize">文件大小</param>
    /// <returns>文档附件实例</returns>
    public static AttachmentViewModel CreateDocument(string fileName, string localPath, long fileSize = 0)
    {
        return new AttachmentViewModel
        {
            FileName = fileName,
            LocalPath = localPath,
            PreviewUrl = GetDocumentIcon(fileName),
            ContentType = GetDocumentContentType(fileName),
            FileSize = fileSize
        };
    }

    /// <summary>
    /// 根据文件名获取图片内容类型
    /// </summary>
    private static string GetImageContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".webp" => "image/webp",
            _ => "image/jpeg"
        };
    }

    /// <summary>
    /// 根据文件名获取文档内容类型
    /// </summary>
    private static string GetDocumentContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".ppt" => "application/vnd.ms-powerpoint",
            ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".txt" => "text/plain",
            _ => "application/octet-stream"
        };
    }

    /// <summary>
    /// 根据文件名获取文档图标
    /// </summary>
    private static string GetDocumentIcon(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".pdf" => "pdf_icon.png",
            ".doc" or ".docx" => "word_icon.png",
            ".xls" or ".xlsx" => "excel_icon.png",
            ".ppt" or ".pptx" => "powerpoint_icon.png",
            ".txt" => "text_icon.png",
            _ => "document_icon.png"
        };
    }
}
