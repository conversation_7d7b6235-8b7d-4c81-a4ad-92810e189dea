using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户好友关系实体配置
/// </summary>
public class UserFriendshipConfiguration : IEntityTypeConfiguration<UserFriendship>
{
    public void Configure(EntityTypeBuilder<UserFriendship> builder)
    {
        // 表名
        builder.ToTable("user_friendships");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.FriendId)
            .IsRequired();
            
        builder.Property(x => x.FriendAlias)
            .HasMaxLength(100);
            
        builder.Property(x => x.FriendshipStatus)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => new { x.UserId, x.FriendId })
            .IsUnique()
            .HasDatabaseName("idx_friendships_user_friend");
            
        builder.HasIndex(x => x.FriendshipStatus)
            .HasDatabaseName("idx_friendships_status");
        
        // 关系配置已在AppUserConfiguration中定义
    }
}
