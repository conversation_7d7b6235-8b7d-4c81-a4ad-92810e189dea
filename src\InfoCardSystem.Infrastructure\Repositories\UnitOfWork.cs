using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore.Storage;

namespace InfoCardSystem.Infrastructure.Repositories;

/// <summary>
/// 工作单元实现
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly InfoCardDbContext _context;
    private IDbContextTransaction? _transaction;
    
    // 仓储实例
    private IRepository<AppUser>? _users;
    private IRepository<UserFriendship>? _friendships;
    private IRepository<UserBlacklist>? _blacklists;
    private IRepository<UserGroup>? _groups;
    private IRepository<UserGroupMember>? _groupMembers;
    private IRepository<UserInfoCard>? _infoCards;
    private IRepository<UserInfoCardRecipient>? _infoCardRecipients;
    private IRepository<UserInfoCardFavorite>? _infoCardFavorites;
    private IRepository<UserAttachment>? _attachments;
    private IRepository<UserInfoCardAttachment>? _infoCardAttachments;
    private IRepository<PasswordResetToken>? _passwordResetTokens;
    private IRepository<UserQRCode>? _userQRCodes;

    public UnitOfWork(InfoCardDbContext context)
    {
        _context = context;
    }

    public IRepository<AppUser> Users => 
        _users ??= new Repository<AppUser>(_context);

    public IRepository<UserFriendship> Friendships => 
        _friendships ??= new Repository<UserFriendship>(_context);

    public IRepository<UserBlacklist> Blacklists => 
        _blacklists ??= new Repository<UserBlacklist>(_context);

    public IRepository<UserGroup> Groups => 
        _groups ??= new Repository<UserGroup>(_context);

    public IRepository<UserGroupMember> GroupMembers => 
        _groupMembers ??= new Repository<UserGroupMember>(_context);

    public IRepository<UserInfoCard> InfoCards => 
        _infoCards ??= new Repository<UserInfoCard>(_context);

    public IRepository<UserInfoCardRecipient> InfoCardRecipients => 
        _infoCardRecipients ??= new Repository<UserInfoCardRecipient>(_context);

    public IRepository<UserInfoCardFavorite> InfoCardFavorites => 
        _infoCardFavorites ??= new Repository<UserInfoCardFavorite>(_context);

    public IRepository<UserAttachment> Attachments => 
        _attachments ??= new Repository<UserAttachment>(_context);

    public IRepository<UserInfoCardAttachment> InfoCardAttachments =>
        _infoCardAttachments ??= new Repository<UserInfoCardAttachment>(_context);

    public IRepository<PasswordResetToken> PasswordResetTokens =>
        _passwordResetTokens ??= new Repository<PasswordResetToken>(_context);

    public IRepository<UserQRCode> UserQRCodes =>
        _userQRCodes ??= new Repository<UserQRCode>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync(cancellationToken);
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
