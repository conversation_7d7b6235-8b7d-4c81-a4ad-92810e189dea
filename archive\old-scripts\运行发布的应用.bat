@echo off
title InfoCard 发布应用运行器
color 0D

echo ========================================
echo   InfoCard 发布应用运行器
echo   运行VS2022发布的应用程序
echo ========================================
echo.

echo [INFO] 停止现有服务...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo 步骤 1: 检查发布文件
echo ========================================
if exist "C:\Webs\ICAPI\InfoCardSystem.API.exe" (
    echo ✅ API发布文件存在
) else (
    echo ❌ API发布文件不存在，请先运行VS2022发布
    pause
    exit /b 1
)

if exist "C:\Webs\ICWeb\InfoCardSystem.Web.exe" (
    echo ✅ Web发布文件存在
) else (
    echo ❌ Web发布文件不存在，请先运行VS2022发布
    pause
    exit /b 1
)

echo.
echo ========================================
echo 步骤 2: 检查MySQL服务
echo ========================================
sc query MySQL80 | findstr "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✅ MySQL服务已运行
) else (
    echo ⚠️  MySQL服务未运行，正在启动...
    net start MySQL80 >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ MySQL服务启动成功
    ) else (
        echo ❌ MySQL服务启动失败，请手动启动
    )
)

echo.
echo ========================================
echo 步骤 3: 设置环境变量
echo ========================================
set ASPNETCORE_ENVIRONMENT=Production
set ASPNETCORE_URLS_API=http://localhost:9081
set ASPNETCORE_URLS_WEB=http://localhost:9082
echo ✅ 环境变量设置完成

echo.
echo ========================================
echo 步骤 4: 启动API应用 (端口9081)
echo ========================================
echo 正在后台启动API应用...
start "InfoCard API - 9081" cmd /c "cd /d C:\Webs\ICAPI && set ASPNETCORE_ENVIRONMENT=Production && set ASPNETCORE_URLS=http://localhost:9081 && InfoCardSystem.API.exe"
echo ✅ API应用启动中... (端口: 9081)

echo 等待API应用完全启动...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo 步骤 5: 启动Web应用 (端口9082)
echo ========================================
echo 正在后台启动Web应用...
start "InfoCard Web - 9082" cmd /c "cd /d C:\Webs\ICWeb && set ASPNETCORE_ENVIRONMENT=Production && set ASPNETCORE_URLS=http://localhost:9082 && InfoCardSystem.Web.exe"
echo ✅ Web应用启动中... (端口: 9082)

echo 等待Web应用完全启动...
timeout /t 6 /nobreak >nul

echo.
echo ========================================
echo 步骤 6: 验证服务状态
echo ========================================
echo 检查端口监听状态...
netstat -an | findstr :9081 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ✅ API端口9081正在监听
) else (
    echo ⚠️  API端口9081未监听
)

netstat -an | findstr :9082 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ✅ Web端口9082正在监听
) else (
    echo ⚠️  Web端口9082未监听
)

echo.
echo 检查API健康状态...
curl -s http://localhost:9081/health >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ API服务运行正常 (9081)
) else (
    echo ⚠️  API服务可能还在启动中 (9081)
)

echo 检查Web应用状态...
curl -s -I http://localhost:9082 >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Web应用运行正常 (9082)
) else (
    echo ⚠️  Web应用可能还在启动中 (9082)
)

echo.
echo ========================================
echo 🎉 发布应用运行完成！
echo ========================================
echo.
echo 📱 应用访问地址:
echo    🔧 API服务:     http://localhost:9081
echo    🔧 API文档:     http://localhost:9081/swagger
echo    💚 健康检查:    http://localhost:9081/health
echo    🌐 Web应用:     http://localhost:9082
echo.
echo 🛠️  VS2022发布测试成功:
echo    ✅ VS2022发布功能正常工作
echo    ✅ 发布的应用程序可以独立运行
echo    ✅ 端口9081/9082模拟IIS的8081/8082
echo.
echo 📋 常用操作:
echo    1. 按任意键打开Web应用
echo    2. 输入 'a' 打开API文档
echo    3. 输入 'h' 查看健康检查
echo    4. 输入 'v' 打开VS2022
echo    5. 输入 'q' 退出
echo.

set /p choice="请选择操作 (1/a/h/v/q): "

if /i "%choice%"=="1" (
    echo 正在打开Web应用...
    start http://localhost:9082
) else if /i "%choice%"=="a" (
    echo 正在打开API文档...
    start http://localhost:9081/swagger
) else if /i "%choice%"=="h" (
    echo 正在打开健康检查...
    start http://localhost:9081/health
) else if /i "%choice%"=="v" (
    echo 正在打开VS2022...
    start InfoCardSystem.VS2022.sln
) else if /i "%choice%"=="q" (
    echo 退出启动器...
    exit /b 0
) else (
    echo 正在打开Web应用...
    start http://localhost:9082
)

echo.
echo 🎯 发布应用已就绪！
echo 💡 提示: 这些是通过VS2022发布的独立应用程序
echo 🔄 可以继续测试VS2022的发布功能
echo.
pause
