@echo off
echo ========================================
echo InfoCard Test Environment Startup
echo ========================================
echo.

echo Checking MySQL service...
sc query mysql80 | findstr "RUNNING" >nul
if %errorLevel% neq 0 (
    echo MySQL service not running
    echo Starting MySQL service...
    net start mysql80
    if %errorLevel% neq 0 (
        echo Failed to start MySQL service, please start manually
        pause
        exit /b 1
    ) else (
        echo MySQL service started
    )
) else (
    echo MySQL service is running
)
echo.

echo Checking port usage...
netstat -ano | findstr ":8001" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo Port 8001 is in use, API may already be running
) else (
    echo Port 8001 is available
)

netstat -ano | findstr ":8002" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo Port 8002 is in use, Web app may already be running
) else (
    echo Port 8002 is available
)
echo.

echo Starting API service...
start "InfoCard API" cmd /c "cd C:\Webs\ICAPI && InfoCardSystem.API.exe"
echo API service startup command executed
echo.

echo Waiting for API service to start (5 seconds)...
timeout /t 5 /nobreak >nul
echo.

echo Starting Web application...
start "InfoCard Web" cmd /c "cd C:\Webs\ICWeb && InfoCardSystem.Web.exe"
echo Web application startup command executed
echo.

echo Waiting for Web application to start (5 seconds)...
timeout /t 5 /nobreak >nul
echo.

echo ========================================
echo Test Environment Started
echo ========================================
echo.
echo You can now:
echo.
echo 1. Run "quick-test.bat" to verify service status
echo 2. Access Web app: http://localhost:8002
echo 3. Access API docs: http://localhost:8001/swagger
echo 4. Follow the complete testing guide
echo.
echo Note: If browser shows certificate warning, click "Continue"
echo.

echo Open browser automatically? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    start "" "http://localhost:8002"
    timeout /t 2 /nobreak >nul
    start "" "http://localhost:8001/swagger"
)

echo.
echo After testing, close command windows to stop services
echo.

pause
