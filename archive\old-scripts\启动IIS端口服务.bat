@echo off
title InfoCard IIS端口服务启动器
color 0B

echo ========================================
echo   InfoCard IIS端口服务启动器
echo   API: 8081端口, Web: 8082端口
echo ========================================
echo.

echo [INFO] 停止现有服务...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1
taskkill /IM "dotnet.exe" /F >nul 2>&1

echo 等待进程完全停止...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 步骤 1: 检查MySQL服务
echo ========================================
sc query MySQL80 | findstr "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✅ MySQL服务已运行
) else (
    echo ⚠️  MySQL服务未运行，正在启动...
    net start MySQL80 >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ MySQL服务启动成功
    ) else (
        echo ❌ MySQL服务启动失败，请手动启动
    )
)

echo.
echo ========================================
echo 步骤 2: 清理端口占用
echo ========================================
echo 检查端口8081占用情况...
netstat -an | findstr :8081 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ⚠️  端口8081被占用，正在清理...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081 ^| findstr LISTENING') do taskkill /PID %%a /F >nul 2>&1
)

echo 检查端口8082占用情况...
netstat -an | findstr :8082 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ⚠️  端口8082被占用，正在清理...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8082 ^| findstr LISTENING') do taskkill /PID %%a /F >nul 2>&1
)
echo ✅ 端口清理完成

echo.
echo ========================================
echo 步骤 3: 构建项目
echo ========================================
echo 正在构建解决方案...
dotnet build InfoCardSystem.VS2022.sln -c Release --verbosity quiet
if %errorLevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
) else (
    echo ✅ 项目构建成功
)

echo.
echo ========================================
echo 步骤 4: 启动API服务 (端口8081)
echo ========================================
echo 正在后台启动API服务...
start "InfoCard API - 8081" /MIN dotnet run --project src/InfoCardSystem.API/InfoCardSystem.API.csproj --environment Production --urls "http://localhost:8081"
echo ✅ API服务启动中... (端口: 8081)

echo 等待API服务完全启动...
timeout /t 12 /nobreak >nul

echo.
echo ========================================
echo 步骤 5: 启动Web应用 (端口8082)
echo ========================================
echo 正在后台启动Web应用...
start "InfoCard Web - 8082" /MIN dotnet run --project src/InfoCardSystem.Web/InfoCardSystem.Web.csproj --environment Production --urls "http://localhost:8082"
echo ✅ Web应用启动中... (端口: 8082)

echo 等待Web应用完全启动...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo 步骤 6: 验证服务状态
echo ========================================
echo 检查API服务 (8081)...
curl -s http://localhost:8081/health >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ API服务运行正常 (8081)
) else (
    echo ⚠️  API服务可能还在启动中 (8081)
)

echo 检查Web应用 (8082)...
curl -s -I http://localhost:8082 >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Web应用运行正常 (8082)
) else (
    echo ⚠️  Web应用可能还在启动中 (8082)
)

echo.
echo ========================================
echo 🎉 IIS端口服务启动完成！
echo ========================================
echo.
echo 📱 应用访问地址:
echo    🔧 API服务:     http://localhost:8081
echo    🔧 API文档:     http://localhost:8081/swagger
echo    💚 健康检查:    http://localhost:8081/health
echo    🌐 Web应用:     http://localhost:8082
echo.
echo 🛠️  VS2022发布测试:
echo    现在可以在VS2022中右键发布项目
echo    发布后的文件将覆盖C:\Webs\ICAPI和C:\Webs\ICWeb
echo    但服务将继续在8081和8082端口运行
echo.
echo 📋 常用操作:
echo    1. 按任意键打开Web应用 (8082)
echo    2. 输入 'a' 打开API文档 (8081)
echo    3. 输入 'h' 查看健康检查
echo    4. 输入 'v' 打开VS2022
echo    5. 输入 'q' 退出
echo.

set /p choice="请选择操作 (1/a/h/v/q): "

if /i "%choice%"=="1" (
    echo 正在打开Web应用...
    start http://localhost:8082
) else if /i "%choice%"=="a" (
    echo 正在打开API文档...
    start http://localhost:8081/swagger
) else if /i "%choice%"=="h" (
    echo 正在打开健康检查...
    start http://localhost:8081/health
) else if /i "%choice%"=="v" (
    echo 正在打开VS2022...
    start InfoCardSystem.VS2022.sln
) else if /i "%choice%"=="q" (
    echo 退出启动器...
    exit /b 0
) else (
    echo 正在打开Web应用...
    start http://localhost:8082
)

echo.
echo 🎯 IIS端口服务已就绪！
echo 💡 提示: 关闭此窗口不会停止服务，服务将继续在后台运行
echo 🔄 现在可以在VS2022中测试发布功能
echo.
pause
