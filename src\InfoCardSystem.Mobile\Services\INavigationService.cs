namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 导航服务接口
/// </summary>
public interface INavigationService
{
    /// <summary>
    /// 导航到指定路由
    /// </summary>
    /// <param name="route">路由</param>
    /// <param name="parameters">参数</param>
    Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null);

    /// <summary>
    /// 返回上一页
    /// </summary>
    Task GoBackAsync();

    /// <summary>
    /// 导航到根页面
    /// </summary>
    Task NavigateToRootAsync();

    /// <summary>
    /// 弹出到根页面
    /// </summary>
    Task PopToRootAsync();

    /// <summary>
    /// 获取当前路由
    /// </summary>
    string GetCurrentRoute();
}
