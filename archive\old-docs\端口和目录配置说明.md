# InfoCard 端口和目录配置说明

## 📋 配置概述

根据需求，已将InfoCard系统的端口和发布目录统一配置如下：

### 🔧 API服务
- **端口**: 8001
- **发布目录**: C:\Webs\ICAPI
- **URL**: http://localhost:8001

### 🌐 Web应用
- **端口**: 8002
- **发布目录**: C:\Webs\ICWeb
- **URL**: http://localhost:8002

## 🚀 已更新的文件

### 1. 配置文件
- ✅ API的launchSettings.json - 更新端口为8001
- ✅ Web的launchSettings.json - 更新端口为8002
- ✅ API的appsettings.Development.json - 更新BaseUrl为8001
- ✅ Web的appsettings.Development.json - 更新ApiSettings.BaseUrl为8001
- ✅ API的发布配置文件 - 更新发布目录为C:\Webs\ICAPI
- ✅ Web的发布配置文件 - 更新发布目录为C:\Webs\ICWeb

### 2. 发布文件
- ✅ API的web.config - 更新端口为8001
- ✅ Web的web.config - 更新端口为8002
- ✅ API的appsettings.Production.json - 更新BaseUrl和Urls为8001
- ✅ Web的appsettings.Production.json - 更新ApiSettings.BaseUrl为8001和Urls为8002
- ✅ API的appsettings.IIS.json - 更新所有端口引用
- ✅ Web的appsettings.IIS.json - 更新所有端口引用

### 3. 脚本文件
- ✅ start-test-env.bat - 更新端口和目录引用
- ✅ quick-test.bat - 更新端口和目录引用
- ✅ stop-test-env.bat - 更新端口引用
- ✅ publish-to-webs.bat (新增) - 用于发布到指定目录

## 📁 目录结构

```
C:\Webs\
├── ICAPI\                      # API发布目录
│   ├── InfoCardSystem.API.dll  # API主程序
│   ├── appsettings.json        # 基础配置
│   ├── appsettings.Production.json # 生产环境配置
│   ├── web.config              # IIS配置
│   └── logs\                   # 日志目录
└── ICWeb\                      # Web发布目录
    ├── InfoCardSystem.Web.dll  # Web主程序
    ├── appsettings.json        # 基础配置
    ├── appsettings.Production.json # 生产环境配置
    ├── web.config              # IIS配置
    ├── wwwroot\                # 静态资源
    └── logs\                   # 日志目录
```

## 🛠️ 使用说明

### 1. 发布项目
使用提供的发布脚本将项目发布到指定目录：
```
publish-to-webs.bat
```
此脚本会：
- 创建必要的目录
- 发布API和Web项目
- 更新配置文件
- 创建日志目录

### 2. 启动服务
使用启动脚本启动API和Web服务：
```
start-test-env.bat
```
此脚本会：
- 检查MySQL服务状态
- 检查端口占用情况
- 启动API服务 (端口8001)
- 启动Web应用 (端口8002)
- 提供浏览器访问选项

### 3. 测试服务
使用测试脚本验证服务状态：
```
quick-test.bat
```
此脚本会：
- 测试API健康检查 (端口8001)
- 测试Swagger文档访问
- 测试Web应用主页 (端口8002)
- 测试登录和注册页面
- 检查端口监听状态

### 4. 停止服务
使用停止脚本关闭所有服务：
```
stop-test-env.bat
```
此脚本会：
- 停止API服务 (端口8001)
- 停止Web应用 (端口8002)
- 验证端口释放状态

## 🔍 验证方法

### API服务验证
- 访问 http://localhost:8001/health 检查健康状态
- 访问 http://localhost:8001/swagger 查看API文档

### Web应用验证
- 访问 http://localhost:8002 查看主页
- 访问 http://localhost:8002/login 测试登录页面
- 访问 http://localhost:8002/register 测试注册页面

## ⚠️ 注意事项

### 1. 端口占用
确保端口8001和8002未被其他应用占用。可以使用以下命令检查：
```
netstat -ano | findstr :8001
netstat -ano | findstr :8002
```

### 2. 目录权限
确保C:\Webs目录具有适当的权限：
- 当前用户需要有写入权限
- IIS_IUSRS组需要有读取和执行权限

### 3. 配置文件优先级
- 发布目录中的appsettings.Production.json会覆盖appsettings.json的设置
- 环境变量会覆盖配置文件中的设置

### 4. 日志位置
- API日志: C:\Webs\ICAPI\logs\
- Web日志: C:\Webs\ICWeb\logs\

## 🚀 IIS配置建议

如果需要在IIS中配置网站，建议使用以下设置：

### API网站
- **网站名称**: ICAPI
- **物理路径**: C:\Webs\ICAPI
- **绑定信息**: http / 8001
- **应用程序池**: ICAPI (无托管代码)

### Web网站
- **网站名称**: ICWeb
- **物理路径**: C:\Webs\ICWeb
- **绑定信息**: http / 8002
- **应用程序池**: ICWeb (无托管代码)

---

> **提示**: 所有配置已经统一更新，您可以直接使用提供的脚本进行发布、启动和测试。如果遇到问题，请检查日志文件或使用测试脚本验证服务状态。
