<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.AboutPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:AboutViewModel"
             Title="关于InfoCard"
             BackgroundColor="{StaticResource Background}">

    <ScrollView>
        <StackLayout Padding="24" Spacing="24">
            
            <!-- 应用Logo和信息 -->
            <StackLayout HorizontalOptions="Center" Spacing="16">
                
                <!-- Logo -->
                <Frame WidthRequest="120"
                       HeightRequest="120"
                       CornerRadius="60"
                       Padding="0"
                       BackgroundColor="{StaticResource Primary}"
                       HorizontalOptions="Center">
                    <Image Source="app_logo.png"
                           Aspect="AspectFit"
                           WidthRequest="80"
                           HeightRequest="80" />
                </Frame>
                
                <!-- 应用名称 -->
                <Label Text="InfoCard"
                       FontSize="28"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextPrimary}"
                       HorizontalOptions="Center" />
                
                <!-- 版本信息 -->
                <Label Text="{Binding AppVersion}"
                       FontSize="16"
                       TextColor="{StaticResource TextSecondary}"
                       HorizontalOptions="Center" />
                
                <!-- 构建信息 -->
                <Label Text="{Binding BuildInfo}"
                       FontSize="12"
                       TextColor="{StaticResource TextHint}"
                       HorizontalOptions="Center" />
                
            </StackLayout>

            <!-- 应用描述 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="应用介绍"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />
                    
                    <Label Text="InfoCard是一款专为好友间资讯分享而设计的移动应用。通过简洁优雅的界面，您可以轻松地与好友分享生活中的精彩时刻、有趣想法和重要信息。"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           LineBreakMode="WordWrap" />
                </StackLayout>
            </Frame>

            <!-- 主要功能 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="主要功能"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="12">
                        
                        <Label Grid.Row="0" Grid.Column="0"
                               Text="📋"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="0" Grid.Column="1"
                               Text="创建和分享资讯卡片"
                               FontSize="16"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0" />
                        
                        <Label Grid.Row="1" Grid.Column="0"
                               Text="👥"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="1" Grid.Column="1"
                               Text="好友管理和社交互动"
                               FontSize="16"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0" />
                        
                        <Label Grid.Row="2" Grid.Column="0"
                               Text="📱"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="2" Grid.Column="1"
                               Text="多媒体内容支持"
                               FontSize="16"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0" />
                        
                        <Label Grid.Row="3" Grid.Column="0"
                               Text="🔒"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="3" Grid.Column="1"
                               Text="隐私保护和安全加密"
                               FontSize="16"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0" />
                        
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 技术信息 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="技术信息"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="8">
                        
                        <Label Grid.Row="0" Grid.Column="0"
                               Text="框架:"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               VerticalOptions="Center" />
                        <Label Grid.Row="0" Grid.Column="1"
                               Text=".NET 9.0 MAUI"
                               FontSize="14"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="8,0,0,0" />
                        
                        <Label Grid.Row="1" Grid.Column="0"
                               Text="平台:"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               VerticalOptions="Center" />
                        <Label Grid.Row="1" Grid.Column="1"
                               Text="Android API 35"
                               FontSize="14"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="8,0,0,0" />
                        
                        <Label Grid.Row="2" Grid.Column="0"
                               Text="架构:"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               VerticalOptions="Center" />
                        <Label Grid.Row="2" Grid.Column="1"
                               Text="MVVM + 依赖注入"
                               FontSize="14"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="8,0,0,0" />
                        
                        <Label Grid.Row="3" Grid.Column="0"
                               Text="设计:"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               VerticalOptions="Center" />
                        <Label Grid.Row="3" Grid.Column="1"
                               Text="Material Design 3"
                               FontSize="14"
                               TextColor="{StaticResource TextPrimary}"
                               VerticalOptions="Center"
                               Margin="8,0,0,0" />
                        
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 开发团队 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="开发团队"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />
                    
                    <Label Text="InfoCard开发团队致力于为用户提供最优质的社交分享体验。我们相信技术的力量能够连接人与人之间的情感，让分享变得更加简单和美好。"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           LineBreakMode="WordWrap" />
                </StackLayout>
            </Frame>

            <!-- 联系我们 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="联系我们"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto" RowSpacing="12">
                        
                        <Label Grid.Row="0" Grid.Column="0"
                               Text="📧"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="0" Grid.Column="1"
                               Text="<EMAIL>"
                               FontSize="16"
                               TextColor="{StaticResource Primary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding ContactEmailCommand}" />
                            </Label.GestureRecognizers>
                        </Label>
                        
                        <Label Grid.Row="1" Grid.Column="0"
                               Text="🌐"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="1" Grid.Column="1"
                               Text="www.infocard.com"
                               FontSize="16"
                               TextColor="{StaticResource Primary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding VisitWebsiteCommand}" />
                            </Label.GestureRecognizers>
                        </Label>
                        
                        <Label Grid.Row="2" Grid.Column="0"
                               Text="💬"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <Label Grid.Row="2" Grid.Column="1"
                               Text="在线客服"
                               FontSize="16"
                               TextColor="{StaticResource Primary}"
                               VerticalOptions="Center"
                               Margin="12,0,0,0">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding OnlineServiceCommand}" />
                            </Label.GestureRecognizers>
                        </Label>
                        
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 版权信息 -->
            <StackLayout HorizontalOptions="Center" Spacing="8">
                <Label Text="© 2025 InfoCard Team"
                       FontSize="14"
                       TextColor="{StaticResource TextHint}"
                       HorizontalOptions="Center" />
                <Label Text="All rights reserved"
                       FontSize="12"
                       TextColor="{StaticResource TextHint}"
                       HorizontalOptions="Center" />
            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
