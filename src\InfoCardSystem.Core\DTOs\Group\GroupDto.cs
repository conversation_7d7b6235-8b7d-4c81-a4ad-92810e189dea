using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.Group;

/// <summary>
/// 群组信息DTO
/// </summary>
public class GroupDto
{
    /// <summary>
    /// 群组ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 群组名称
    /// </summary>
    public string GroupName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组描述
    /// </summary>
    public string? GroupDescription { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 创建者ID
    /// </summary>
    public int CreatorId { get; set; }
    
    /// <summary>
    /// 创建者用户名
    /// </summary>
    public string CreatorUsername { get; set; } = string.Empty;
    
    /// <summary>
    /// 成员数量
    /// </summary>
    public int MemberCount { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 创建群组请求DTO
/// </summary>
public class CreateGroupRequest
{
    /// <summary>
    /// 群组名称
    /// </summary>
    [Required(ErrorMessage = "群组名称不能为空")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "群组名称长度必须在1-100个字符之间")]
    public string GroupName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组描述
    /// </summary>
    [StringLength(500, ErrorMessage = "群组描述不能超过500个字符")]
    public string? GroupDescription { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    [StringLength(500, ErrorMessage = "头像URL不能超过500个字符")]
    [Url(ErrorMessage = "请输入有效的URL")]
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 初始成员用户ID列表（只能添加直属好友）
    /// </summary>
    public List<int>? InitialMemberIds { get; set; }
}

/// <summary>
/// 更新群组请求DTO
/// </summary>
public class UpdateGroupRequest
{
    /// <summary>
    /// 群组名称
    /// </summary>
    [StringLength(100, MinimumLength = 1, ErrorMessage = "群组名称长度必须在1-100个字符之间")]
    public string? GroupName { get; set; }
    
    /// <summary>
    /// 群组描述
    /// </summary>
    [StringLength(500, ErrorMessage = "群组描述不能超过500个字符")]
    public string? GroupDescription { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    [StringLength(500, ErrorMessage = "头像URL不能超过500个字符")]
    [Url(ErrorMessage = "请输入有效的URL")]
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// 群组成员DTO
/// </summary>
public class GroupMemberDto
{
    /// <summary>
    /// 成员ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户头像
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 好友别名（如果是好友的话）
    /// </summary>
    public string? FriendAlias { get; set; }
    
    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinedAt { get; set; }
    
    /// <summary>
    /// 是否为群主
    /// </summary>
    public bool IsCreator { get; set; }
}

/// <summary>
/// 添加群组成员请求DTO
/// </summary>
public class AddGroupMembersRequest
{
    /// <summary>
    /// 要添加的用户ID列表（只能添加直属好友）
    /// </summary>
    [Required(ErrorMessage = "用户ID列表不能为空")]
    [MinLength(1, ErrorMessage = "至少需要添加一个用户")]
    public List<int> UserIds { get; set; } = new();
}

/// <summary>
/// 群组详情DTO
/// </summary>
public class GroupDetailDto : GroupDto
{
    /// <summary>
    /// 群组成员列表
    /// </summary>
    public List<GroupMemberDto> Members { get; set; } = new();
    
    /// <summary>
    /// 当前用户是否为群主
    /// </summary>
    public bool IsCurrentUserCreator { get; set; }
    
    /// <summary>
    /// 当前用户是否为群组成员
    /// </summary>
    public bool IsCurrentUserMember { get; set; }
}
