namespace InfoCardSystem.Core.DTOs.User;

/// <summary>
/// 用户资料DTO
/// </summary>
public class UserProfileDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 自定义用户ID
    /// </summary>
    public string CustomUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    public string? Bio { get; set; }
    
    /// <summary>
    /// 用户状态
    /// </summary>
    public string UserStatus { get; set; } = string.Empty;
    
    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
