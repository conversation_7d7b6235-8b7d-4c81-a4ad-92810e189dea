<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.MyQRCodePage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:MyQRCodeViewModel"
             Title="我的二维码"
             BackgroundColor="{StaticResource Background}">

    <Grid RowDefinitions="*,Auto">
        
        <!-- 主内容区域 -->
        <ScrollView Grid.Row="0">
            <StackLayout Padding="24" Spacing="24">
                
                <!-- 用户信息卡片 -->
                <Frame Style="{StaticResource CardFrame}"
                       BackgroundColor="{StaticResource Primary}">
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto">
                        
                        <!-- 头像 -->
                        <Frame Grid.Row="0" Grid.Column="0" Grid.RowSpan="2"
                               WidthRequest="80"
                               HeightRequest="80"
                               CornerRadius="40"
                               Padding="0"
                               BackgroundColor="White"
                               VerticalOptions="Center">
                            <Image Source="{Binding UserInfo.AvatarUrl, FallbackValue='default_avatar.png'}"
                                   Aspect="AspectFill" />
                        </Frame>
                        
                        <!-- 用户信息 -->
                        <StackLayout Grid.Row="0" Grid.Column="1"
                                    Margin="16,0,0,0"
                                    VerticalOptions="Center">
                            <Label Text="{Binding UserInfo.DisplayName, FallbackValue={Binding UserInfo.Username}}"
                                   FontSize="20"
                                   FontAttributes="Bold"
                                   TextColor="White" />
                            <Label Text="{Binding UserInfo.Username}"
                                   FontSize="14"
                                   TextColor="White"
                                   Opacity="0.8"
                                   Margin="0,4,0,0" />
                        </StackLayout>
                        
                        <!-- 二维码提示 -->
                        <Label Grid.Row="1" Grid.Column="1"
                               Text="扫描下方二维码添加我为好友"
                               FontSize="14"
                               TextColor="White"
                               Opacity="0.9"
                               Margin="16,8,0,0" />
                        
                    </Grid>
                </Frame>

                <!-- 二维码区域 -->
                <Frame Style="{StaticResource CardFrame}"
                       Padding="32"
                       HorizontalOptions="Center">
                    <StackLayout HorizontalOptions="Center" Spacing="16">
                        
                        <!-- 二维码图片 -->
                        <Frame WidthRequest="200"
                               HeightRequest="200"
                               CornerRadius="12"
                               Padding="16"
                               BackgroundColor="White"
                               BorderColor="{StaticResource Outline}"
                               HorizontalOptions="Center">
                            <Image Source="{Binding QRCodeImageSource}"
                                   Aspect="AspectFit"
                                   BackgroundColor="White" />
                        </Frame>
                        
                        <!-- 二维码说明 -->
                        <Label Text="好友扫描此二维码即可添加您为好友"
                               FontSize="14"
                               TextColor="{StaticResource TextSecondary}"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center" />
                        
                        <!-- 刷新按钮 -->
                        <Button Text="🔄 刷新二维码"
                               Style="{StaticResource SecondaryButton}"
                               FontSize="14"
                               WidthRequest="120"
                               HeightRequest="36"
                               Command="{Binding RefreshQRCodeCommand}"
                               IsEnabled="{Binding IsNotBusy}" />
                        
                    </StackLayout>
                </Frame>

                <!-- 分享选项 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="分享方式"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,16" />
                        
                        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="12">
                            
                            <!-- 保存到相册 -->
                            <Button Grid.Column="0"
                                   Text="📷"
                                   FontSize="24"
                                   BackgroundColor="{StaticResource Success}"
                                   TextColor="White"
                                   CornerRadius="12"
                                   HeightRequest="60"
                                   Command="{Binding SaveToGalleryCommand}" />
                            
                            <!-- 分享给好友 -->
                            <Button Grid.Column="1"
                                   Text="📤"
                                   FontSize="24"
                                   BackgroundColor="{StaticResource Info}"
                                   TextColor="White"
                                   CornerRadius="12"
                                   HeightRequest="60"
                                   Command="{Binding ShareToFriendsCommand}" />
                            
                            <!-- 复制链接 -->
                            <Button Grid.Column="2"
                                   Text="🔗"
                                   FontSize="24"
                                   BackgroundColor="{StaticResource Warning}"
                                   TextColor="White"
                                   CornerRadius="12"
                                   HeightRequest="60"
                                   Command="{Binding CopyLinkCommand}" />
                            
                        </Grid>
                        
                        <!-- 分享选项说明 -->
                        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="12" Margin="0,8,0,0">
                            
                            <Label Grid.Column="0"
                                   Text="保存图片"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}"
                                   HorizontalOptions="Center" />
                            
                            <Label Grid.Column="1"
                                   Text="分享好友"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}"
                                   HorizontalOptions="Center" />
                            
                            <Label Grid.Column="2"
                                   Text="复制链接"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}"
                                   HorizontalOptions="Center" />
                            
                        </Grid>
                    </StackLayout>
                </Frame>

                <!-- 使用说明 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="使用说明"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <StackLayout Spacing="8">
                            <Label Text="1. 让好友使用InfoCard扫描此二维码"
                                   FontSize="14"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="2. 或者保存二维码图片分享给好友"
                                   FontSize="14"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="3. 好友扫描后会自动发送好友请求"
                                   FontSize="14"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="4. 您可以在好友页面查看和处理请求"
                                   FontSize="14"
                                   TextColor="{StaticResource TextPrimary}" />
                        </StackLayout>
                    </StackLayout>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  HorizontalOptions="Center"
                                  Margin="0,20,0,0" />

                <!-- 错误消息 -->
                <Frame IsVisible="{Binding HasError}"
                       BackgroundColor="{StaticResource Error}"
                       Margin="0,8,0,0">
                    <Label Text="{Binding ErrorMessage}"
                           TextColor="White"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </Frame>

            </StackLayout>
        </ScrollView>

        <!-- 底部操作栏 -->
        <Frame Grid.Row="1"
               BackgroundColor="{StaticResource Surface}"
               CornerRadius="0"
               Padding="16,12"
               HasShadow="True">
            
            <Grid ColumnDefinitions="*,*" ColumnSpacing="12">
                
                <!-- 扫描二维码 -->
                <Button Grid.Column="0"
                       Text="📷 扫一扫"
                       Style="{StaticResource SecondaryButton}"
                       Command="{Binding ScanQRCodeCommand}" />
                
                <!-- 查看好友请求 -->
                <Button Grid.Column="1"
                       Text="👥 好友请求"
                       Command="{Binding ViewFriendRequestsCommand}" />
                
            </Grid>
            
        </Frame>
        
    </Grid>

</ContentPage>
