using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户资讯卡收藏实体配置
/// </summary>
public class UserInfoCardFavoriteConfiguration : IEntityTypeConfiguration<UserInfoCardFavorite>
{
    public void Configure(EntityTypeBuilder<UserInfoCardFavorite> builder)
    {
        // 表名
        builder.ToTable("user_infocard_favorites");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.InfoCardId)
            .IsRequired();
            
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => new { x.InfoCardId, x.UserId })
            .IsUnique()
            .HasDatabaseName("idx_favorites_infocard_user");
            
        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("idx_favorites_user");
        
        // 关系配置已在UserInfoCardConfiguration和AppUserConfiguration中定义
    }
}
