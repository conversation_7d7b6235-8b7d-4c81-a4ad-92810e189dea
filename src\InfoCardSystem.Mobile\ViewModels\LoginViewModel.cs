using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 登录页面ViewModel
/// </summary>
public partial class LoginViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;
    private readonly IPreferencesService _preferencesService;

    public LoginViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        IPreferencesService preferencesService,
        ILogger<LoginViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        _preferencesService = preferencesService;
        Title = "登录";
        
        // 加载保存的用户名
        LoadSavedCredentials();
    }

    [ObservableProperty]
    [Required(ErrorMessage = "请输入用户名、邮箱或手机号")]
    private string username = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "请输入密码")]
    private string password = string.Empty;

    [ObservableProperty]
    private bool rememberMe = true;

    [ObservableProperty]
    private bool isNotBusy = true;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    /// <summary>
    /// 登录命令
    /// </summary>
    [RelayCommand]
    private async Task LoginAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            // 验证输入
            if (!ValidateInput())
                return;

            _logger.LogInformation("开始用户登录: {Username}", Username);

            var request = new LoginRequest
            {
                UsernameOrEmailOrPhone = Username.Trim(),
                Password = Password,
                RememberMe = RememberMe
            };

            var response = await _authService.LoginAsync(request);

            if (response.Success && response.Data != null)
            {
                // 保存用户凭据
                if (RememberMe)
                {
                    await SaveCredentials();
                }

                await ShowSuccessAsync("登录成功");
                
                // 导航到主页
                await NavigateToAsync("//main");
                
                _logger.LogInformation("用户登录成功: {Username}", Username);
            }
            else
            {
                await ShowErrorAsync("登录失败", response.Message);
                _logger.LogWarning("用户登录失败: {Username}, 错误: {Error}", Username, response.Message);
            }
        }, "登录");
    }

    /// <summary>
    /// 注册命令
    /// </summary>
    [RelayCommand]
    private async Task RegisterAsync()
    {
        await NavigateToAsync("register");
    }

    /// <summary>
    /// 忘记密码命令
    /// </summary>
    [RelayCommand]
    private async Task ForgotPasswordAsync()
    {
        await NavigateToAsync("forgotpassword");
    }

    /// <summary>
    /// 手机号登录命令
    /// </summary>
    [RelayCommand]
    private async Task PhoneLoginAsync()
    {
        await ShowToastAsync("手机号登录功能即将推出");
    }

    /// <summary>
    /// 生物识别登录命令
    /// </summary>
    [RelayCommand]
    private async Task BiometricLoginAsync()
    {
        await ShowToastAsync("生物识别登录功能即将推出");
    }

    /// <summary>
    /// 验证输入
    /// </summary>
    private bool ValidateInput()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Username))
        {
            errors.Add("请输入用户名、邮箱或手机号");
        }

        if (string.IsNullOrWhiteSpace(Password))
        {
            errors.Add("请输入密码");
        }

        if (errors.Any())
        {
            ErrorMessage = string.Join("\n", errors);
            HasError = true;
            return false;
        }

        ClearError();
        return true;
    }

    /// <summary>
    /// 加载保存的凭据
    /// </summary>
    private void LoadSavedCredentials()
    {
        try
        {
            var savedUsername = _preferencesService.GetString("saved_username", string.Empty);
            var savedRememberMe = _preferencesService.GetBool("remember_me", true);

            if (!string.IsNullOrEmpty(savedUsername))
            {
                Username = savedUsername;
                RememberMe = savedRememberMe;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载保存的凭据失败");
        }
    }

    /// <summary>
    /// 保存凭据
    /// </summary>
    private async Task SaveCredentials()
    {
        try
        {
            _preferencesService.SetString("saved_username", Username);
            _preferencesService.SetBool("remember_me", RememberMe);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存凭据失败");
        }
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        
        // 检查是否已经登录
        var isAuthenticated = await _authService.IsAuthenticatedAsync();
        if (isAuthenticated)
        {
            await NavigateToAsync("//main");
        }
    }
}
