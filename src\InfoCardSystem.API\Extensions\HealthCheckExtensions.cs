using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Text.Json;
using InfoCardSystem.Infrastructure.Data;

namespace InfoCardSystem.API.Extensions;

/// <summary>
/// 健康检查扩展
/// </summary>
public static class HealthCheckExtensions
{
    /// <summary>
    /// 添加应用程序健康检查
    /// </summary>
    public static IServiceCollection AddApplicationHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            // 数据库健康检查
            .AddDbContextCheck<InfoCardDbContext>("database")
            // 内存使用检查
            .AddCheck<MemoryHealthCheck>("memory")
            // 磁盘空间检查
            .AddCheck<DiskSpaceHealthCheck>("disk_space");

        return services;
    }

    /// <summary>
    /// 配置健康检查端点
    /// </summary>
    public static IApplicationBuilder UseApplicationHealthChecks(this IApplicationBuilder app)
    {
        app.UseHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
        {
            ResponseWriter = WriteHealthCheckResponse
        });

        app.UseHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("ready"),
            ResponseWriter = WriteHealthCheckResponse
        });

        app.UseHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
        {
            Predicate = _ => false,
            ResponseWriter = WriteHealthCheckResponse
        });

        return app;
    }

    private static async Task WriteHealthCheckResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var options = new JsonWriterOptions { Indented = true };
        using var memoryStream = new MemoryStream();
        using (var jsonWriter = new Utf8JsonWriter(memoryStream, options))
        {
            jsonWriter.WriteStartObject();
            jsonWriter.WriteString("status", healthReport.Status.ToString());
            jsonWriter.WriteString("totalDuration", healthReport.TotalDuration.ToString());
            jsonWriter.WriteStartObject("results");

            foreach (var (key, value) in healthReport.Entries)
            {
                jsonWriter.WriteStartObject(key);
                jsonWriter.WriteString("status", value.Status.ToString());
                jsonWriter.WriteString("duration", value.Duration.ToString());
                jsonWriter.WriteString("description", value.Description);

                if (value.Data.Any())
                {
                    jsonWriter.WriteStartObject("data");
                    foreach (var (dataKey, dataValue) in value.Data)
                    {
                        jsonWriter.WritePropertyName(dataKey);
                        JsonSerializer.Serialize(jsonWriter, dataValue, dataValue?.GetType() ?? typeof(object));
                    }
                    jsonWriter.WriteEndObject();
                }

                if (value.Exception != null)
                {
                    jsonWriter.WriteString("exception", value.Exception.Message);
                }

                jsonWriter.WriteEndObject();
            }

            jsonWriter.WriteEndObject();
            jsonWriter.WriteEndObject();
        }

        await context.Response.WriteAsync(System.Text.Encoding.UTF8.GetString(memoryStream.ToArray()));
    }
}

/// <summary>
/// 内存健康检查
/// </summary>
public class MemoryHealthCheck : IHealthCheck
{
    private readonly long _maxMemoryMB;

    public MemoryHealthCheck(IConfiguration configuration)
    {
        _maxMemoryMB = configuration.GetValue<long>("Performance:MaxMemoryMB", 1024);
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var allocatedMB = GC.GetTotalMemory(false) / 1024 / 1024;
        var data = new Dictionary<string, object>
        {
            ["AllocatedMemoryMB"] = allocatedMB,
            ["MaxMemoryMB"] = _maxMemoryMB,
            ["Gen0Collections"] = GC.CollectionCount(0),
            ["Gen1Collections"] = GC.CollectionCount(1),
            ["Gen2Collections"] = GC.CollectionCount(2)
        };

        var status = allocatedMB < _maxMemoryMB ? HealthStatus.Healthy : HealthStatus.Unhealthy;
        var description = $"内存使用: {allocatedMB}MB / {_maxMemoryMB}MB";

        return Task.FromResult(new HealthCheckResult(status, description, data: data));
    }
}

/// <summary>
/// 磁盘空间健康检查
/// </summary>
public class DiskSpaceHealthCheck : IHealthCheck
{
    private readonly long _minFreeSpaceGB;

    public DiskSpaceHealthCheck(IConfiguration configuration)
    {
        _minFreeSpaceGB = configuration.GetValue<long>("Performance:MinFreeSpaceGB", 1);
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var driveInfo = new DriveInfo(Path.GetPathRoot(Environment.CurrentDirectory) ?? "C:");
            var freeSpaceGB = driveInfo.AvailableFreeSpace / 1024 / 1024 / 1024;
            var totalSpaceGB = driveInfo.TotalSize / 1024 / 1024 / 1024;

            var data = new Dictionary<string, object>
            {
                ["FreeSpaceGB"] = freeSpaceGB,
                ["TotalSpaceGB"] = totalSpaceGB,
                ["MinFreeSpaceGB"] = _minFreeSpaceGB,
                ["UsagePercentage"] = Math.Round((double)(totalSpaceGB - freeSpaceGB) / totalSpaceGB * 100, 2)
            };

            var status = freeSpaceGB >= _minFreeSpaceGB ? HealthStatus.Healthy : HealthStatus.Unhealthy;
            var description = $"磁盘空间: {freeSpaceGB}GB 可用 / {totalSpaceGB}GB 总计";

            return Task.FromResult(new HealthCheckResult(status, description, data: data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(new HealthCheckResult(HealthStatus.Unhealthy, "磁盘空间检查失败", ex));
        }
    }
}
