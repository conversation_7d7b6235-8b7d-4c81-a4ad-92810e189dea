using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 注册页面ViewModel
/// </summary>
public partial class RegisterViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;
    private readonly IInfoCardApiClient _apiClient;

    public RegisterViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        IInfoCardApiClient apiClient,
        ILogger<RegisterViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        Title = "注册";
    }

    [ObservableProperty]
    [Required(ErrorMessage = "请输入用户名")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
    private string username = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "请输入邮箱")]
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    private string email = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "请输入手机号")]
    [Phone(ErrorMessage = "手机号格式不正确")]
    private string phone = string.Empty;

    [ObservableProperty]
    private string displayName = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "请输入密码")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    private string password = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "请确认密码")]
    private string confirmPassword = string.Empty;

    [ObservableProperty]
    private bool agreeToTerms;

    [ObservableProperty]
    private bool isNotBusy = true;

    [ObservableProperty]
    private bool isUsernameValid;

    [ObservableProperty]
    private bool isEmailValid;

    [ObservableProperty]
    private bool isPhoneValid;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    partial void OnUsernameChanged(string value)
    {
        _ = ValidateUsernameAsync(value);
    }

    partial void OnEmailChanged(string value)
    {
        _ = ValidateEmailAsync(value);
    }

    partial void OnPhoneChanged(string value)
    {
        _ = ValidatePhoneAsync(value);
    }

    /// <summary>
    /// 注册命令
    /// </summary>
    [RelayCommand]
    private async Task RegisterAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            // 验证输入
            if (!ValidateInput())
                return;

            _logger.LogInformation("开始用户注册: {Username}", Username);

            var request = new RegisterRequest
            {
                Username = Username.Trim(),
                Email = Email.Trim(),
                Phone = Phone.Trim(),
                DisplayName = string.IsNullOrWhiteSpace(DisplayName) ? null : DisplayName.Trim(),
                Password = Password,
                ConfirmPassword = ConfirmPassword
            };

            var response = await _authService.RegisterAsync(request);

            if (response.Success && response.Data != null)
            {
                await ShowSuccessAsync("注册成功！请登录您的账户");
                
                // 导航到登录页面
                await NavigateToAsync("login");
                
                _logger.LogInformation("用户注册成功: {Username}", Username);
            }
            else
            {
                await ShowErrorAsync("注册失败", response.Message);
                _logger.LogWarning("用户注册失败: {Username}, 错误: {Error}", Username, response.Message);
            }
        }, "注册");
    }

    /// <summary>
    /// 登录命令
    /// </summary>
    [RelayCommand]
    private async Task LoginAsync()
    {
        await NavigateToAsync("login");
    }

    /// <summary>
    /// 手机号注册命令
    /// </summary>
    [RelayCommand]
    private async Task PhoneRegisterAsync()
    {
        await ShowToastAsync("手机号快速注册功能即将推出");
    }

    /// <summary>
    /// 邮箱注册命令
    /// </summary>
    [RelayCommand]
    private async Task EmailRegisterAsync()
    {
        await ShowToastAsync("邮箱快速注册功能即将推出");
    }

    /// <summary>
    /// 验证输入
    /// </summary>
    private bool ValidateInput()
    {
        var errors = new List<string>();

        // 用户名验证
        if (string.IsNullOrWhiteSpace(Username))
        {
            errors.Add("请输入用户名");
        }
        else if (Username.Length < 3 || Username.Length > 50)
        {
            errors.Add("用户名长度必须在3-50个字符之间");
        }
        else if (!IsValidUsername(Username))
        {
            errors.Add("用户名只能包含字母、数字和下划线");
        }

        // 邮箱验证
        if (string.IsNullOrWhiteSpace(Email))
        {
            errors.Add("请输入邮箱");
        }
        else if (!IsValidEmail(Email))
        {
            errors.Add("邮箱格式不正确");
        }

        // 手机号验证
        if (string.IsNullOrWhiteSpace(Phone))
        {
            errors.Add("请输入手机号");
        }
        else if (!IsValidPhone(Phone))
        {
            errors.Add("手机号格式不正确");
        }

        // 密码验证
        if (string.IsNullOrWhiteSpace(Password))
        {
            errors.Add("请输入密码");
        }
        else if (Password.Length < 6)
        {
            errors.Add("密码长度至少6个字符");
        }
        else if (!IsValidPassword(Password))
        {
            errors.Add("密码必须包含字母和数字");
        }

        // 确认密码验证
        if (string.IsNullOrWhiteSpace(ConfirmPassword))
        {
            errors.Add("请确认密码");
        }
        else if (Password != ConfirmPassword)
        {
            errors.Add("两次输入的密码不一致");
        }

        // 服务条款验证
        if (!AgreeToTerms)
        {
            errors.Add("请同意服务条款和隐私政策");
        }

        if (errors.Any())
        {
            ErrorMessage = string.Join("\n", errors);
            HasError = true;
            return false;
        }

        ClearError();
        return true;
    }

    /// <summary>
    /// 验证用户名可用性
    /// </summary>
    private async Task ValidateUsernameAsync(string username)
    {
        if (string.IsNullOrWhiteSpace(username) || username.Length < 3)
        {
            IsUsernameValid = false;
            return;
        }

        try
        {
            var response = await _apiClient.CheckUsernameAvailabilityAsync(username);
            IsUsernameValid = response.Success && response.Data == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证用户名可用性失败: {Username}", username);
            IsUsernameValid = false;
        }
    }

    /// <summary>
    /// 验证邮箱可用性
    /// </summary>
    private async Task ValidateEmailAsync(string email)
    {
        if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
        {
            IsEmailValid = false;
            return;
        }

        try
        {
            var response = await _apiClient.CheckEmailAvailabilityAsync(email);
            IsEmailValid = response.Success && response.Data == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证邮箱可用性失败: {Email}", email);
            IsEmailValid = false;
        }
    }

    /// <summary>
    /// 验证手机号可用性
    /// </summary>
    private async Task ValidatePhoneAsync(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone) || !IsValidPhone(phone))
        {
            IsPhoneValid = false;
            return;
        }

        try
        {
            var response = await _apiClient.CheckPhoneAvailabilityAsync(phone);
            IsPhoneValid = response.Success && response.Data == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证手机号可用性失败: {Phone}", phone);
            IsPhoneValid = false;
        }
    }

    /// <summary>
    /// 验证用户名格式
    /// </summary>
    private static bool IsValidUsername(string username)
    {
        return Regex.IsMatch(username, @"^[a-zA-Z0-9_]+$");
    }

    /// <summary>
    /// 验证邮箱格式
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        return Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
    }

    /// <summary>
    /// 验证手机号格式
    /// </summary>
    private static bool IsValidPhone(string phone)
    {
        return Regex.IsMatch(phone, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 验证密码强度
    /// </summary>
    private static bool IsValidPassword(string password)
    {
        // 至少包含一个字母和一个数字
        return Regex.IsMatch(password, @"^(?=.*[a-zA-Z])(?=.*\d).+$");
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }
}
