﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InfoCardSystem.API", "src\InfoCardSystem.API\InfoCardSystem.API.csproj", "{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InfoCardSystem.Core", "src\InfoCardSystem.Core\InfoCardSystem.Core.csproj", "{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InfoCardSystem.Infrastructure", "src\InfoCardSystem.Infrastructure\InfoCardSystem.Infrastructure.csproj", "{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InfoCardSystem.Shared", "src\InfoCardSystem.Shared\InfoCardSystem.Shared.csproj", "{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "InfoCardSystem.Mobile", "src\InfoCardSystem.Mobile\InfoCardSystem.Mobile.csproj", "{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "InfoCardSystem.Web", "src\InfoCardSystem.Web\InfoCardSystem.Web.csproj", "{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}"
	ProjectSection(ProjectDependencies) = postProject
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697} = {DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InfoCardSystem.Tests", "tests\InfoCardSystem.Tests\InfoCardSystem.Tests.csproj", "{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|x64.Build.0 = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Debug|x86.Build.0 = Debug|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|x64.ActiveCfg = Release|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|x64.Build.0 = Release|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|x86.ActiveCfg = Release|Any CPU
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697}.Release|x86.Build.0 = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|x64.Build.0 = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Debug|x86.Build.0 = Debug|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|Any CPU.Build.0 = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|x64.ActiveCfg = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|x64.Build.0 = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|x86.ActiveCfg = Release|Any CPU
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E}.Release|x86.Build.0 = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|x64.Build.0 = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Debug|x86.Build.0 = Debug|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|x64.ActiveCfg = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|x64.Build.0 = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|x86.ActiveCfg = Release|Any CPU
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0}.Release|x86.Build.0 = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|x64.Build.0 = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Debug|x86.Build.0 = Debug|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|x64.ActiveCfg = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|x64.Build.0 = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|x86.ActiveCfg = Release|Any CPU
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18}.Release|x86.Build.0 = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|x64.Build.0 = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Debug|x86.Build.0 = Debug|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|x64.ActiveCfg = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|x64.Build.0 = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|x86.ActiveCfg = Release|Any CPU
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C}.Release|x86.Build.0 = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|x64.Build.0 = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Debug|x86.Build.0 = Debug|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|x64.ActiveCfg = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|x64.Build.0 = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|x86.ActiveCfg = Release|Any CPU
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6}.Release|x86.Build.0 = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|x64.Build.0 = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Debug|x86.Build.0 = Debug|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|x64.ActiveCfg = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|x64.Build.0 = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|x86.ActiveCfg = Release|Any CPU
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA}.Release|x86.Build.0 = Release|Any CPU

	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DC07B8E2-36CC-4F65-8B44-75AE3C0E5697} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{90E1BC31-B026-4BDC-AFB3-1E5E4A600D8E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{77BDDF3F-08FC-4DF4-84F9-024F31BB09A0} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{B0E2AF1D-A8EE-4902-87E1-F31C66B03C18} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{E8F4C2A1-9B3D-4E5F-8A7C-1D2E3F4A5B6C} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{F1A2B3C4-D5E6-F7A8-B9C0-D1E2F3A4B5C6} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{CB2A1905-FB0A-4B46-8E83-54D0144F42AA} = {0AB3BF05-4346-4AA6-1389-037BE0695223}

	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
	EndGlobalSection
EndGlobal
