using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Group;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using InfoCardSystem.Shared.Enums;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 群组服务实现
/// </summary>
public class GroupService : IGroupService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<GroupService> _logger;
    private readonly ICacheService _cacheService;

    public GroupService(
        IUnitOfWork unitOfWork,
        ILogger<GroupService> logger,
        ICacheService cacheService)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 创建群组
    /// </summary>
    public async Task<ApiResponse<GroupDto>> CreateGroupAsync(int userId, CreateGroupRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户是否存在
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<GroupDto>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // 创建群组
            var group = new UserGroup
            {
                GroupName = request.GroupName,
                GroupDescription = request.GroupDescription,
                AvatarUrl = request.AvatarUrl,
                CreatorId = userId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Groups.AddAsync(group, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 添加创建者为群组成员
            var creatorMember = new UserGroupMember
            {
                GroupId = group.Id,
                UserId = userId,
                JoinedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.GroupMembers.AddAsync(creatorMember, cancellationToken);

            // 添加初始成员（如果有）
            if (request.InitialMemberIds?.Any() == true)
            {
                // 验证这些用户都是当前用户的直属好友
                var friendIds = await GetUserFriendIdsAsync(userId, cancellationToken);
                var validMemberIds = request.InitialMemberIds.Where(id => friendIds.Contains(id)).ToList();

                if (validMemberIds.Any())
                {
                    var initialMembers = validMemberIds.Select(memberId => new UserGroupMember
                    {
                        GroupId = group.Id,
                        UserId = memberId,
                        JoinedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }).ToList();

                    await _unitOfWork.GroupMembers.AddRangeAsync(initialMembers, cancellationToken);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("群组创建成功: GroupId={GroupId}, UserId={UserId}", group.Id, userId);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_{userId}_*");

            // 返回群组信息
            var groupDto = new GroupDto
            {
                Id = group.Id,
                GroupName = group.GroupName,
                GroupDescription = group.GroupDescription,
                AvatarUrl = group.AvatarUrl,
                CreatorId = group.CreatorId,
                CreatorUsername = user.Username,
                MemberCount = 1 + (request.InitialMemberIds?.Count ?? 0),
                CreatedAt = group.CreatedAt,
                UpdatedAt = group.UpdatedAt
            };

            return ApiResponse<GroupDto>.SuccessResult(groupDto, "群组创建成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建群组失败: UserId={UserId}", userId);
            return ApiResponse<GroupDto>.ErrorResult("创建群组失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取用户的群组列表
    /// </summary>
    public async Task<ApiResponse<PagedResult<GroupDto>>> GetUserGroupsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"user_groups_{userId}_{page}_{pageSize}";
            
            var cachedResult = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                // 获取用户参与的群组
                var memberQuery = _unitOfWork.GroupMembers.GetQueryable()
                    .Where(gm => gm.UserId == userId)
                    .Select(gm => gm.GroupId);

                var groupQuery = _unitOfWork.Groups.GetQueryable()
                    .Where(g => memberQuery.Contains(g.Id))
                    .OrderByDescending(g => g.UpdatedAt);

                var totalCount = await _unitOfWork.Groups.CountAsync(groupQuery, cancellationToken);
                var groups = await _unitOfWork.Groups.GetPagedAsync(groupQuery, page, pageSize, cancellationToken);

                var groupDtos = new List<GroupDto>();
                foreach (var group in groups)
                {
                    var creator = await _unitOfWork.Users.GetByIdAsync(group.CreatorId, cancellationToken);
                    var memberCount = await _unitOfWork.GroupMembers.CountAsync(
                        gm => gm.GroupId == group.Id, cancellationToken);

                    groupDtos.Add(new GroupDto
                    {
                        Id = group.Id,
                        GroupName = group.GroupName,
                        GroupDescription = group.GroupDescription,
                        AvatarUrl = group.AvatarUrl,
                        CreatorId = group.CreatorId,
                        CreatorUsername = creator?.Username ?? "未知用户",
                        MemberCount = memberCount,
                        CreatedAt = group.CreatedAt,
                        UpdatedAt = group.UpdatedAt
                    });
                }

                return new PagedResult<GroupDto>
                {
                    Items = groupDtos,
                    Pagination = new PaginationInfo
                    {
                        CurrentPage = page,
                        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                        TotalItems = totalCount,
                        ItemsPerPage = pageSize
                    }
                };
            }, TimeSpan.FromMinutes(10));

            return ApiResponse<PagedResult<GroupDto>>.SuccessResult(cachedResult!, "获取群组列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户群组列表失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<GroupDto>>.ErrorResult("获取群组列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取群组详情
    /// </summary>
    public async Task<ApiResponse<GroupDetailDto>> GetGroupDetailAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群组成员
            var isMember = await IsGroupMemberAsync(userId, groupId, cancellationToken);
            if (!isMember)
            {
                return ApiResponse<GroupDetailDto>.ErrorResult("无权访问该群组", ErrorCodes.GROUP_001);
            }

            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            if (group == null)
            {
                return ApiResponse<GroupDetailDto>.ErrorResult("群组不存在", ErrorCodes.GROUP_001);
            }

            var creator = await _unitOfWork.Users.GetByIdAsync(group.CreatorId, cancellationToken);
            var members = await GetGroupMembersAsync(userId, groupId, cancellationToken);

            var groupDetail = new GroupDetailDto
            {
                Id = group.Id,
                GroupName = group.GroupName,
                GroupDescription = group.GroupDescription,
                AvatarUrl = group.AvatarUrl,
                CreatorId = group.CreatorId,
                CreatorUsername = creator?.Username ?? "未知用户",
                MemberCount = members.Data?.Count ?? 0,
                CreatedAt = group.CreatedAt,
                UpdatedAt = group.UpdatedAt,
                Members = members.Data ?? new List<GroupMemberDto>(),
                IsCurrentUserCreator = group.CreatorId == userId,
                IsCurrentUserMember = true
            };

            return ApiResponse<GroupDetailDto>.SuccessResult(groupDetail, "获取群组详情成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组详情失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<GroupDetailDto>.ErrorResult("获取群组详情失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 更新群组信息
    /// </summary>
    public async Task<ApiResponse<GroupDto>> UpdateGroupAsync(int userId, int groupId, UpdateGroupRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群主
            var isCreator = await IsGroupCreatorAsync(userId, groupId, cancellationToken);
            if (!isCreator)
            {
                return ApiResponse<GroupDto>.ErrorResult("只有群主可以修改群组信息", ErrorCodes.GROUP_003);
            }

            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            if (group == null)
            {
                return ApiResponse<GroupDto>.ErrorResult("群组不存在", ErrorCodes.GROUP_001);
            }

            // 更新群组信息
            if (!string.IsNullOrEmpty(request.GroupName))
            {
                group.GroupName = request.GroupName;
            }

            if (request.GroupDescription != null)
            {
                group.GroupDescription = request.GroupDescription;
            }

            if (request.AvatarUrl != null)
            {
                group.AvatarUrl = request.AvatarUrl;
            }

            group.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Groups.Update(group);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_*");
            await _cacheService.RemoveAsync($"group_detail_{groupId}");

            _logger.LogInformation("群组信息更新成功: GroupId={GroupId}, UserId={UserId}", groupId, userId);

            var creator = await _unitOfWork.Users.GetByIdAsync(group.CreatorId, cancellationToken);
            var memberCount = await _unitOfWork.GroupMembers.CountAsync(gm => gm.GroupId == groupId, cancellationToken);

            var groupDto = new GroupDto
            {
                Id = group.Id,
                GroupName = group.GroupName,
                GroupDescription = group.GroupDescription,
                AvatarUrl = group.AvatarUrl,
                CreatorId = group.CreatorId,
                CreatorUsername = creator?.Username ?? "未知用户",
                MemberCount = memberCount,
                CreatedAt = group.CreatedAt,
                UpdatedAt = group.UpdatedAt
            };

            return ApiResponse<GroupDto>.SuccessResult(groupDto, "群组信息更新成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新群组信息失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<GroupDto>.ErrorResult("更新群组信息失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 删除群组
    /// </summary>
    public async Task<ApiResponse<bool>> DeleteGroupAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群主
            var isCreator = await IsGroupCreatorAsync(userId, groupId, cancellationToken);
            if (!isCreator)
            {
                return ApiResponse<bool>.ErrorResult("只有群主可以删除群组", ErrorCodes.GROUP_003);
            }

            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            if (group == null)
            {
                return ApiResponse<bool>.ErrorResult("群组不存在", ErrorCodes.GROUP_001);
            }

            // 删除群组（级联删除会自动删除相关的成员记录和资讯卡接收记录）
            _unitOfWork.Groups.Remove(group);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_*");
            await _cacheService.RemoveAsync($"group_detail_{groupId}");

            _logger.LogInformation("群组删除成功: GroupId={GroupId}, UserId={UserId}", groupId, userId);

            return ApiResponse<bool>.SuccessResult(true, "群组删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除群组失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<bool>.ErrorResult("删除群组失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 添加群组成员
    /// </summary>
    public async Task<ApiResponse<bool>> AddGroupMembersAsync(int userId, int groupId, AddGroupMembersRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群主
            var isCreator = await IsGroupCreatorAsync(userId, groupId, cancellationToken);
            if (!isCreator)
            {
                return ApiResponse<bool>.ErrorResult("只有群主可以添加成员", ErrorCodes.GROUP_003);
            }

            // 验证群组是否存在
            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            if (group == null)
            {
                return ApiResponse<bool>.ErrorResult("群组不存在", ErrorCodes.GROUP_001);
            }

            // 获取当前用户的直属好友ID列表
            var friendIds = await GetUserFriendIdsAsync(userId, cancellationToken);

            // 过滤出有效的好友ID（只能添加直属好友）
            var validUserIds = request.UserIds.Where(id => friendIds.Contains(id)).ToList();

            if (!validUserIds.Any())
            {
                return ApiResponse<bool>.ErrorResult("只能添加直属好友到群组", ErrorCodes.GROUP_002);
            }

            // 获取已经是群组成员的用户ID
            var existingMemberIds = await _unitOfWork.GroupMembers.GetAllAsync(
                gm => gm.GroupId == groupId && validUserIds.Contains(gm.UserId),
                cancellationToken);

            var existingIds = existingMemberIds.Select(gm => gm.UserId).ToList();
            var newMemberIds = validUserIds.Except(existingIds).ToList();

            if (!newMemberIds.Any())
            {
                return ApiResponse<bool>.ErrorResult("所选用户都已经是群组成员", ErrorCodes.GROUP_004);
            }

            // 添加新成员
            var newMembers = newMemberIds.Select(memberId => new UserGroupMember
            {
                GroupId = groupId,
                UserId = memberId,
                JoinedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            await _unitOfWork.GroupMembers.AddRangeAsync(newMembers, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_*");
            await _cacheService.RemoveAsync($"group_detail_{groupId}");

            _logger.LogInformation("群组成员添加成功: GroupId={GroupId}, UserId={UserId}, AddedCount={Count}",
                groupId, userId, newMembers.Count);

            return ApiResponse<bool>.SuccessResult(true, $"成功添加 {newMembers.Count} 个成员");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加群组成员失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<bool>.ErrorResult("添加群组成员失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取群组成员列表
    /// </summary>
    public async Task<ApiResponse<List<GroupMemberDto>>> GetGroupMembersAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群组成员
            var isMember = await IsGroupMemberAsync(userId, groupId, cancellationToken);
            if (!isMember)
            {
                return ApiResponse<List<GroupMemberDto>>.ErrorResult("无权访问该群组", ErrorCodes.GROUP_001);
            }

            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            if (group == null)
            {
                return ApiResponse<List<GroupMemberDto>>.ErrorResult("群组不存在", ErrorCodes.GROUP_001);
            }

            var members = await _unitOfWork.GroupMembers.GetAllAsync(
                gm => gm.GroupId == groupId,
                cancellationToken);

            var memberDtos = new List<GroupMemberDto>();

            foreach (var member in members)
            {
                var user = await _unitOfWork.Users.GetByIdAsync(member.UserId, cancellationToken);
                if (user != null)
                {
                    // 获取好友别名（如果是当前用户的好友）
                    string? friendAlias = null;
                    if (member.UserId != userId)
                    {
                        var friendship = await _unitOfWork.Friendships.FirstOrDefaultAsync(
                            f => f.UserId == userId && f.FriendId == member.UserId && f.FriendshipStatus == FriendshipStatus.Accepted,
                            cancellationToken);
                        friendAlias = friendship?.FriendAlias;
                    }

                    memberDtos.Add(new GroupMemberDto
                    {
                        Id = member.Id,
                        UserId = member.UserId,
                        Username = user.Username,
                        AvatarUrl = user.AvatarUrl,
                        FriendAlias = friendAlias,
                        JoinedAt = member.JoinedAt,
                        IsCreator = member.UserId == group.CreatorId
                    });
                }
            }

            // 按加入时间排序，群主排在最前面
            memberDtos = memberDtos.OrderBy(m => m.IsCreator ? 0 : 1).ThenBy(m => m.JoinedAt).ToList();

            return ApiResponse<List<GroupMemberDto>>.SuccessResult(memberDtos, "获取群组成员列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取群组成员列表失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<List<GroupMemberDto>>.ErrorResult("获取群组成员列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 移除群组成员
    /// </summary>
    public async Task<ApiResponse<bool>> RemoveGroupMemberAsync(int userId, int groupId, int memberId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群主
            var isCreator = await IsGroupCreatorAsync(userId, groupId, cancellationToken);
            if (!isCreator)
            {
                return ApiResponse<bool>.ErrorResult("只有群主可以移除成员", ErrorCodes.GROUP_003);
            }

            // 不能移除群主自己
            if (memberId == userId)
            {
                return ApiResponse<bool>.ErrorResult("群主不能移除自己", ErrorCodes.GROUP_005);
            }

            // 查找群组成员记录
            var member = await _unitOfWork.GroupMembers.FirstOrDefaultAsync(
                gm => gm.GroupId == groupId && gm.UserId == memberId,
                cancellationToken);

            if (member == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不是群组成员", ErrorCodes.GROUP_006);
            }

            // 移除成员
            _unitOfWork.GroupMembers.Remove(member);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_*");
            await _cacheService.RemoveAsync($"group_detail_{groupId}");

            _logger.LogInformation("群组成员移除成功: GroupId={GroupId}, UserId={UserId}, MemberId={MemberId}",
                groupId, userId, memberId);

            return ApiResponse<bool>.SuccessResult(true, "成员移除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除群组成员失败: UserId={UserId}, GroupId={GroupId}, MemberId={MemberId}",
                userId, groupId, memberId);
            return ApiResponse<bool>.ErrorResult("移除群组成员失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 退出群组
    /// </summary>
    public async Task<ApiResponse<bool>> LeaveGroupAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否为群主
            var isCreator = await IsGroupCreatorAsync(userId, groupId, cancellationToken);
            if (isCreator)
            {
                return ApiResponse<bool>.ErrorResult("群主不能退出群组，请先转让群主或删除群组", ErrorCodes.GROUP_007);
            }

            // 查找群组成员记录
            var member = await _unitOfWork.GroupMembers.FirstOrDefaultAsync(
                gm => gm.GroupId == groupId && gm.UserId == userId,
                cancellationToken);

            if (member == null)
            {
                return ApiResponse<bool>.ErrorResult("您不是群组成员", ErrorCodes.GROUP_006);
            }

            // 退出群组
            _unitOfWork.GroupMembers.Remove(member);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_groups_{userId}_*");
            await _cacheService.RemoveAsync($"group_detail_{groupId}");

            _logger.LogInformation("用户退出群组成功: GroupId={GroupId}, UserId={UserId}", groupId, userId);

            return ApiResponse<bool>.SuccessResult(true, "退出群组成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "退出群组失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return ApiResponse<bool>.ErrorResult("退出群组失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 检查用户是否为群组成员
    /// </summary>
    public async Task<bool> IsGroupMemberAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            var member = await _unitOfWork.GroupMembers.FirstOrDefaultAsync(
                gm => gm.GroupId == groupId && gm.UserId == userId,
                cancellationToken);
            return member != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群组成员失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return false;
        }
    }

    /// <summary>
    /// 检查用户是否为群主
    /// </summary>
    public async Task<bool> IsGroupCreatorAsync(int userId, int groupId, CancellationToken cancellationToken = default)
    {
        try
        {
            var group = await _unitOfWork.Groups.GetByIdAsync(groupId, cancellationToken);
            return group?.CreatorId == userId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查群主失败: UserId={UserId}, GroupId={GroupId}", userId, groupId);
            return false;
        }
    }

    /// <summary>
    /// 获取用户的直属好友ID列表
    /// </summary>
    private async Task<List<int>> GetUserFriendIdsAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var friendships = await _unitOfWork.Friendships.GetAllAsync(
                f => f.UserId == userId && f.FriendshipStatus == FriendshipStatus.Accepted,
                cancellationToken);

            return friendships.Select(f => f.FriendId).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户好友列表失败: UserId={UserId}", userId);
            return new List<int>();
        }
    }
}
