@echo off
REM InfoCard System - Test VS2022 Publish Functionality

echo ================================================
echo InfoCard System - VS2022 Publish Test
echo ================================================
echo.

echo [INFO] Testing VS2022 publish functionality...
echo.

echo ================================================
echo STEP 1: Clean Previous Builds
echo ================================================
echo Cleaning solution...
dotnet clean InfoCardSystem.VS2022.sln --verbosity quiet
if %errorLevel% neq 0 (
    echo ❌ Failed to clean solution
    pause
    exit /b 1
) else (
    echo ✅ Solution cleaned successfully
)
echo.

echo ================================================
echo STEP 2: Restore Packages
echo ================================================
echo Restoring NuGet packages...
dotnet restore InfoCardSystem.VS2022.sln --verbosity quiet
if %errorLevel% neq 0 (
    echo ❌ Failed to restore packages
    pause
    exit /b 1
) else (
    echo ✅ Packages restored successfully
)
echo.

echo ================================================
echo STEP 3: Build Solution
echo ================================================
echo Building solution in Release mode...
dotnet build InfoCardSystem.VS2022.sln -c Release --verbosity minimal
if %errorLevel% neq 0 (
    echo ❌ Failed to build solution
    pause
    exit /b 1
) else (
    echo ✅ Solution built successfully
)
echo.

echo ================================================
echo STEP 4: Test API Publish Profile
echo ================================================
echo Publishing API using VS2022 publish profile...
dotnet publish src/InfoCardSystem.API/InfoCardSystem.API.csproj -p:PublishProfile=IIS-ICAPI --verbosity minimal
if %errorLevel% neq 0 (
    echo ❌ Failed to publish API project
    pause
    exit /b 1
) else (
    echo ✅ API project published successfully
)
echo.

echo ================================================
echo STEP 5: Test Web Publish Profile
echo ================================================
echo Publishing Web using VS2022 publish profile...
dotnet publish src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -p:PublishProfile=IIS-ICWeb --verbosity minimal
if %errorLevel% neq 0 (
    echo ❌ Failed to publish Web project
    pause
    exit /b 1
) else (
    echo ✅ Web project published successfully
)
echo.

echo ================================================
echo STEP 6: Verify Published Files
echo ================================================

echo [CHECK 1] API Files...
if exist "C:\Webs\ICAPI\InfoCardSystem.API.exe" (
    echo ✅ API executable found
) else (
    echo ❌ API executable NOT found
)

if exist "C:\Webs\ICAPI\web.config" (
    echo ✅ API web.config found
) else (
    echo ❌ API web.config NOT found
)

echo.
echo [CHECK 2] Web Files...
if exist "C:\Webs\ICWeb\InfoCardSystem.Web.exe" (
    echo ✅ Web executable found
) else (
    echo ❌ Web executable NOT found
)

if exist "C:\Webs\ICWeb\web.config" (
    echo ✅ Web web.config found
) else (
    echo ❌ Web web.config NOT found
)

echo.
echo ================================================
echo STEP 7: File Count Verification
echo ================================================

echo [API Directory File Count]
for /f %%i in ('dir /b "C:\Webs\ICAPI\*.dll" 2^>nul ^| find /c /v ""') do set api_dll_count=%%i
echo API directory contains %api_dll_count% DLL files
if %api_dll_count% GTR 10 (
    echo ✅ Sufficient API DLL files found
) else (
    echo ❌ Insufficient API DLL files
)

echo.
echo [Web Directory File Count]
for /f %%i in ('dir /b "C:\Webs\ICWeb\*.dll" 2^>nul ^| find /c /v ""') do set web_dll_count=%%i
echo Web directory contains %web_dll_count% DLL files
if %web_dll_count% GTR 5 (
    echo ✅ Sufficient Web DLL files found
) else (
    echo ❌ Insufficient Web DLL files
)

echo.
echo ================================================
echo SUMMARY
echo ================================================
echo.
echo ✅ VS2022 publish profiles are working correctly!
echo.
echo 📋 How to use in Visual Studio 2022:
echo    1. Right-click on InfoCardSystem.API project
echo    2. Select "Publish..."
echo    3. Choose "IIS-ICAPI" profile
echo    4. Click "Publish"
echo.
echo    5. Right-click on InfoCardSystem.Web project
echo    6. Select "Publish..."
echo    7. Choose "IIS-ICWeb" profile
echo    8. Click "Publish"
echo.
echo 🎯 Both projects will be published to:
echo    - API: C:\Webs\ICAPI
echo    - Web: C:\Webs\ICWeb
echo.
echo 🌐 Access URLs after IIS restart:
echo    - API: http://localhost:8081
echo    - Web: http://localhost:8082
echo.

pause
