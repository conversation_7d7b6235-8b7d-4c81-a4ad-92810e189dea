using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 设置页面ViewModel
/// </summary>
public partial class SettingsViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;
    private readonly IPreferencesService _preferencesService;
    private readonly ICacheService _cacheService;

    public SettingsViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        IPreferencesService preferencesService,
        ICacheService cacheService,
        ILogger<SettingsViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        _preferencesService = preferencesService;
        _cacheService = cacheService;
        Title = "设置";
        
        // 初始化设置
        LoadSettings();
    }

    [ObservableProperty]
    private string phoneNumber = "未绑定";

    [ObservableProperty]
    private string emailAddress = "未绑定";

    [ObservableProperty]
    private bool pushNotificationEnabled = true;

    [ObservableProperty]
    private bool friendRequestNotificationEnabled = true;

    [ObservableProperty]
    private bool infoCardNotificationEnabled = true;

    [ObservableProperty]
    private bool allowSearchEnabled = true;

    [ObservableProperty]
    private bool showOnlineStatusEnabled = true;

    [ObservableProperty]
    private bool darkModeEnabled;

    [ObservableProperty]
    private string currentLanguage = "简体中文";

    [ObservableProperty]
    private string cacheSize = "计算中...";

    [ObservableProperty]
    private string appVersion = "1.0.0";

    partial void OnPushNotificationEnabledChanged(bool value)
    {
        _preferencesService.SetBool("push_notification_enabled", value);
        _logger.LogInformation("推送通知设置已更改: {Enabled}", value);
    }

    partial void OnFriendRequestNotificationEnabledChanged(bool value)
    {
        _preferencesService.SetBool("friend_request_notification_enabled", value);
        _logger.LogInformation("好友请求通知设置已更改: {Enabled}", value);
    }

    partial void OnInfoCardNotificationEnabledChanged(bool value)
    {
        _preferencesService.SetBool("infocard_notification_enabled", value);
        _logger.LogInformation("资讯卡通知设置已更改: {Enabled}", value);
    }

    partial void OnAllowSearchEnabledChanged(bool value)
    {
        _preferencesService.SetBool("allow_search_enabled", value);
        _logger.LogInformation("允许搜索设置已更改: {Enabled}", value);
    }

    partial void OnShowOnlineStatusEnabledChanged(bool value)
    {
        _preferencesService.SetBool("show_online_status_enabled", value);
        _logger.LogInformation("显示在线状态设置已更改: {Enabled}", value);
    }

    partial void OnDarkModeEnabledChanged(bool value)
    {
        _preferencesService.SetBool("dark_mode_enabled", value);
        _logger.LogInformation("深色模式设置已更改: {Enabled}", value);
        
        // 这里可以实现主题切换逻辑
        ApplyTheme(value);
    }

    /// <summary>
    /// 修改密码命令
    /// </summary>
    [RelayCommand]
    private async Task ChangePasswordAsync()
    {
        await NavigateToAsync("changepassword");
    }

    /// <summary>
    /// 绑定手机命令
    /// </summary>
    [RelayCommand]
    private async Task BindPhoneAsync()
    {
        await NavigateToAsync("bindphone");
    }

    /// <summary>
    /// 绑定邮箱命令
    /// </summary>
    [RelayCommand]
    private async Task BindEmailAsync()
    {
        await NavigateToAsync("bindemail");
    }

    /// <summary>
    /// 更改语言命令
    /// </summary>
    [RelayCommand]
    private async Task ChangeLanguageAsync()
    {
        var languages = new[] { "简体中文", "English", "繁體中文" };
        var selectedLanguage = await _dialogService.ShowActionSheetAsync("选择语言", "取消", null, languages);
        
        if (!string.IsNullOrEmpty(selectedLanguage) && selectedLanguage != "取消")
        {
            CurrentLanguage = selectedLanguage;
            _preferencesService.SetString("current_language", selectedLanguage);
            
            await ShowSuccessAsync($"语言已切换为 {selectedLanguage}");
            _logger.LogInformation("语言设置已更改: {Language}", selectedLanguage);
        }
    }

    /// <summary>
    /// 清除缓存命令
    /// </summary>
    [RelayCommand]
    private async Task ClearCacheAsync()
    {
        var confirmed = await ShowConfirmAsync("清除缓存", "确定要清除所有缓存数据吗？这将删除临时文件和图片缓存。");
        if (!confirmed) return;

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogInformation("开始清除缓存");

            await _cacheService.ClearAsync();
            
            // 清除其他缓存（如图片缓存等）
            // 这里可以添加更多缓存清理逻辑
            
            await CalculateCacheSizeAsync();
            
            await ShowSuccessAsync("缓存清除完成");
            
            _logger.LogInformation("缓存清除完成");
        }, "清除缓存");
    }

    /// <summary>
    /// 关于命令
    /// </summary>
    [RelayCommand]
    private async Task AboutAsync()
    {
        await NavigateToAsync("about");
    }

    /// <summary>
    /// 用户协议命令
    /// </summary>
    [RelayCommand]
    private async Task UserAgreementAsync()
    {
        await NavigateToAsync("useragreement");
    }

    /// <summary>
    /// 隐私政策命令
    /// </summary>
    [RelayCommand]
    private async Task PrivacyPolicyAsync()
    {
        await NavigateToAsync("privacypolicy");
    }

    /// <summary>
    /// 加载设置
    /// </summary>
    private void LoadSettings()
    {
        try
        {
            _logger.LogDebug("加载用户设置");

            // 加载通知设置
            PushNotificationEnabled = _preferencesService.GetBool("push_notification_enabled", true);
            FriendRequestNotificationEnabled = _preferencesService.GetBool("friend_request_notification_enabled", true);
            InfoCardNotificationEnabled = _preferencesService.GetBool("infocard_notification_enabled", true);

            // 加载隐私设置
            AllowSearchEnabled = _preferencesService.GetBool("allow_search_enabled", true);
            ShowOnlineStatusEnabled = _preferencesService.GetBool("show_online_status_enabled", true);

            // 加载应用设置
            DarkModeEnabled = _preferencesService.GetBool("dark_mode_enabled", false);
            CurrentLanguage = _preferencesService.GetString("current_language", "简体中文");

            // 加载用户信息
            LoadUserInfo();
            
            // 计算缓存大小
            _ = CalculateCacheSizeAsync();

            _logger.LogInformation("用户设置加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载用户设置失败");
        }
    }

    /// <summary>
    /// 加载用户信息
    /// </summary>
    private async void LoadUserInfo()
    {
        try
        {
            var currentUser = await _authService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                PhoneNumber = !string.IsNullOrEmpty(currentUser.Phone) ? 
                    MaskPhoneNumber(currentUser.Phone) : "未绑定";
                EmailAddress = !string.IsNullOrEmpty(currentUser.Email) ? 
                    MaskEmail(currentUser.Email) : "未绑定";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载用户信息失败");
        }
    }

    /// <summary>
    /// 计算缓存大小
    /// </summary>
    private async Task CalculateCacheSizeAsync()
    {
        try
        {
            // 模拟计算缓存大小
            await Task.Delay(1000);
            
            // 这里应该实际计算缓存文件大小
            var cacheSizeBytes = 1024 * 1024 * 15; // 15MB
            CacheSize = FormatFileSize(cacheSizeBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算缓存大小失败");
            CacheSize = "未知";
        }
    }

    /// <summary>
    /// 应用主题
    /// </summary>
    private void ApplyTheme(bool isDarkMode)
    {
        try
        {
            // 这里可以实现主题切换逻辑
            // Application.Current.UserAppTheme = isDarkMode ? AppTheme.Dark : AppTheme.Light;
            
            _logger.LogInformation("主题已切换: {Theme}", isDarkMode ? "深色" : "浅色");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "切换主题失败");
        }
    }

    /// <summary>
    /// 掩码手机号
    /// </summary>
    private static string MaskPhoneNumber(string phone)
    {
        if (string.IsNullOrEmpty(phone) || phone.Length < 7)
            return phone;
        
        return phone.Substring(0, 3) + "****" + phone.Substring(phone.Length - 4);
    }

    /// <summary>
    /// 掩码邮箱
    /// </summary>
    private static string MaskEmail(string email)
    {
        if (string.IsNullOrEmpty(email) || !email.Contains("@"))
            return email;
        
        var parts = email.Split('@');
        if (parts[0].Length <= 2)
            return email;
        
        return parts[0].Substring(0, 2) + "***@" + parts[1];
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        
        return $"{len:0.##} {sizes[order]}";
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        LoadSettings();
    }
}
