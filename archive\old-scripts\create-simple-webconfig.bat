@echo off
echo Creating simple web.config files...

echo Creating simple API web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<handlers^>
echo       ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo     ^</handlers^>
echo     ^<aspNetCore processPath="dotnet" arguments=".\InfoCardSystem.API.dll" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" /^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo Creating simple Web web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<handlers^>
echo       ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo     ^</handlers^>
echo     ^<aspNetCore processPath="dotnet" arguments=".\InfoCardSystem.Web.dll" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" /^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo Simple web.config files created!
echo Using dotnet instead of exe files for better compatibility
