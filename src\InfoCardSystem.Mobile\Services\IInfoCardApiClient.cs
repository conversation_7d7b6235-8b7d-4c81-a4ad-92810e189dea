using InfoCardSystem.Mobile.Models;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// InfoCard API客户端接口
/// </summary>
public interface IInfoCardApiClient
{
    #region 认证相关

    /// <summary>
    /// 用户登录
    /// </summary>
    Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);

    /// <summary>
    /// 用户注册
    /// </summary>
    Task<ApiResponse<RegisterResponse>> RegisterAsync(RegisterRequest request);

    /// <summary>
    /// 刷新令牌
    /// </summary>
    Task<ApiResponse<TokenResponse>> RefreshTokenAsync(string refreshToken);

    /// <summary>
    /// 忘记密码
    /// </summary>
    Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request);

    /// <summary>
    /// 重置密码
    /// </summary>
    Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request);

    /// <summary>
    /// 检查用户名可用性
    /// </summary>
    Task<ApiResponse<bool>> CheckUsernameAvailabilityAsync(string username);

    /// <summary>
    /// 检查邮箱可用性
    /// </summary>
    Task<ApiResponse<bool>> CheckEmailAvailabilityAsync(string email);

    /// <summary>
    /// 检查手机号可用性
    /// </summary>
    Task<ApiResponse<bool>> CheckPhoneAvailabilityAsync(string phone);

    #endregion

    #region 用户相关

    /// <summary>
    /// 获取用户资料
    /// </summary>
    Task<ApiResponse<UserProfile>> GetUserProfileAsync();

    /// <summary>
    /// 更新用户资料
    /// </summary>
    Task<ApiResponse<UserProfile>> UpdateUserProfileAsync(UpdateUserProfileRequest request);

    /// <summary>
    /// 修改密码
    /// </summary>
    Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordRequest request);

    /// <summary>
    /// 搜索用户
    /// </summary>
    Task<ApiResponse<List<UserInfo>>> SearchUsersAsync(string query, int page = 1, int pageSize = 20);

    /// <summary>
    /// 获取用户二维码
    /// </summary>
    Task<ApiResponse<UserQRCode>> GetUserQRCodeAsync();

    /// <summary>
    /// 匹配联系人
    /// </summary>
    Task<ApiResponse<List<UserInfo>>> MatchContactsAsync(ContactsMatchRequest request);

    #endregion

    #region 好友相关

    /// <summary>
    /// 获取好友列表
    /// </summary>
    Task<ApiResponse<List<Friend>>> GetFriendsAsync();

    /// <summary>
    /// 发送好友请求
    /// </summary>
    Task<ApiResponse<bool>> SendFriendRequestAsync(FriendRequestRequest request);

    /// <summary>
    /// 处理好友请求
    /// </summary>
    Task<ApiResponse<bool>> HandleFriendRequestAsync(int requestId, bool accept, string? message = null);

    /// <summary>
    /// 获取好友请求列表
    /// </summary>
    Task<ApiResponse<List<FriendRequestService>>> GetFriendRequestsAsync();

    /// <summary>
    /// 扫码添加好友
    /// </summary>
    Task<ApiResponse<bool>> ScanQRCodeAddFriendAsync(ScanQRCodeRequest request);

    /// <summary>
    /// 更新好友别名
    /// </summary>
    Task<ApiResponse<bool>> UpdateFriendAliasAsync(int friendId, string alias);

    /// <summary>
    /// 删除好友
    /// </summary>
    Task<ApiResponse<bool>> DeleteFriendAsync(int friendId);

    #endregion

    #region 资讯卡相关

    /// <summary>
    /// 获取时间线
    /// </summary>
    Task<ApiResponse<PagedResult<InfoCard>>> GetTimelineAsync(int page = 1, int pageSize = 20);

    /// <summary>
    /// 获取我的资讯卡
    /// </summary>
    Task<ApiResponse<PagedResult<InfoCard>>> GetMyInfoCardsAsync(int page = 1, int pageSize = 20);

    /// <summary>
    /// 获取资讯卡详情
    /// </summary>
    Task<ApiResponse<InfoCard>> GetInfoCardAsync(int id);

    /// <summary>
    /// 创建资讯卡
    /// </summary>
    Task<ApiResponse<InfoCard>> CreateInfoCardAsync(CreateInfoCardRequest request);

    /// <summary>
    /// 更新资讯卡
    /// </summary>
    Task<ApiResponse<InfoCard>> UpdateInfoCardAsync(int id, UpdateInfoCardRequest request);

    /// <summary>
    /// 删除资讯卡
    /// </summary>
    Task<ApiResponse<bool>> DeleteInfoCardAsync(int id);

    /// <summary>
    /// 转发资讯卡
    /// </summary>
    Task<ApiResponse<bool>> ForwardInfoCardAsync(int id, ForwardInfoCardRequest request);

    /// <summary>
    /// 收藏资讯卡
    /// </summary>
    Task<ApiResponse<bool>> FavoriteInfoCardAsync(int id);

    /// <summary>
    /// 取消收藏资讯卡
    /// </summary>
    Task<ApiResponse<bool>> UnfavoriteInfoCardAsync(int id);

    /// <summary>
    /// 获取收藏列表
    /// </summary>
    Task<ApiResponse<PagedResult<InfoCard>>> GetFavoritesAsync(int page = 1, int pageSize = 20);

    #endregion

    #region 附件相关

    /// <summary>
    /// 上传文件
    /// </summary>
    Task<ApiResponse<Attachment>> UploadFileAsync(Stream fileStream, string fileName, string contentType);

    /// <summary>
    /// 批量上传文件
    /// </summary>
    Task<ApiResponse<List<Attachment>>> UploadFilesAsync(List<FileUploadInfo> files);

    /// <summary>
    /// 下载文件
    /// </summary>
    Task<Stream> DownloadFileAsync(int attachmentId);

    #endregion

    /// <summary>
    /// 设置认证令牌
    /// </summary>
    void SetAuthToken(string token);

    /// <summary>
    /// 清除认证令牌
    /// </summary>
    void ClearAuthToken();
}

// 辅助模型类
public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}

public class UserProfile
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? AvatarUrl { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class UpdateUserProfileRequest
{
    public string? DisplayName { get; set; }
    public string? AvatarUrl { get; set; }
}

public class ChangePasswordRequest
{
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}

public class UserQRCode
{
    public string QRCodeData { get; set; } = string.Empty;
    public string QRCodeUrl { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public UserInfo User { get; set; } = new();
}

public class ContactsMatchRequest
{
    public List<string> PhoneNumbers { get; set; } = new();
}

public class Friend
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? Alias { get; set; }
    public string? AvatarUrl { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class FriendRequestRequest
{
    public int TargetUserId { get; set; }
    public string Message { get; set; } = string.Empty;
}

public class FriendRequestService
{
    public int Id { get; set; }
    public UserInfo Requester { get; set; } = new();
    public string Message { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class ScanQRCodeRequest
{
    public string QRCodeData { get; set; } = string.Empty;
    public string Message { get; set; } = "我是通过扫描二维码添加您为好友的";
}

public class InfoCard
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public UserInfo Publisher { get; set; } = new();
    public List<Attachment> Attachments { get; set; } = new();
    public bool IsFavorited { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateInfoCardRequest
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public List<int> RecipientIds { get; set; } = new();
    public List<int> AttachmentIds { get; set; } = new();
}

public class UpdateInfoCardRequest
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class ForwardInfoCardRequest
{
    public List<int> RecipientIds { get; set; } = new();
    public string? Message { get; set; }
}

public class Attachment
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string Url { get; set; } = string.Empty;
}

public class FileUploadInfo
{
    public Stream FileStream { get; set; } = null!;
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
}
