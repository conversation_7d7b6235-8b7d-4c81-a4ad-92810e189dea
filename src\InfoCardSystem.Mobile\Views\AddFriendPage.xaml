<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.AddFriendPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             xmlns:models="clr-namespace:InfoCardSystem.Mobile.Models"
             x:DataType="viewmodels:AddFriendViewModel"
             Title="添加好友"
             BackgroundColor="{StaticResource Background}">

    <Grid RowDefinitions="Auto,*">
        
        <!-- 顶部快速操作 -->
        <Frame Grid.Row="0" 
               BackgroundColor="{StaticResource Primary}"
               CornerRadius="0"
               Padding="16"
               HasShadow="True">
            
            <Grid ColumnDefinitions="*,*,*,*" ColumnSpacing="8">
                
                <!-- 扫一扫 -->
                <Button Grid.Column="0"
                       Text="📷"
                       FontSize="24"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       CornerRadius="12"
                       HeightRequest="60"
                       Command="{Binding ScanQRCodeCommand}" />
                
                <!-- 我的二维码 -->
                <Button Grid.Column="1"
                       Text="🔲"
                       FontSize="24"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       CornerRadius="12"
                       HeightRequest="60"
                       Command="{Binding MyQRCodeCommand}" />
                
                <!-- 手机联系人 -->
                <Button Grid.Column="2"
                       Text="📱"
                       FontSize="24"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       CornerRadius="12"
                       HeightRequest="60"
                       Command="{Binding ContactsCommand}" />
                
                <!-- 搜索用户 -->
                <Button Grid.Column="3"
                       Text="🔍"
                       FontSize="24"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       CornerRadius="12"
                       HeightRequest="60"
                       Command="{Binding SearchUsersCommand}" />
                
            </Grid>
            
        </Frame>

        <!-- 主内容区域 -->
        <ScrollView Grid.Row="1" Padding="16">
            
            <StackLayout Spacing="20">
                
                <!-- 搜索框 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="搜索用户"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,8" />
                        
                        <Grid ColumnDefinitions="*,Auto">
                            <Entry Grid.Column="0"
                                   Text="{Binding SearchQuery}"
                                   Placeholder="输入用户名、邮箱或手机号"
                                   FontSize="16"
                                   BackgroundColor="Transparent"
                                   TextColor="{StaticResource TextPrimary}"
                                   PlaceholderColor="{StaticResource TextHint}" />
                            
                            <Button Grid.Column="1"
                                   Text="搜索"
                                   FontSize="14"
                                   WidthRequest="80"
                                   HeightRequest="40"
                                   Command="{Binding SearchCommand}"
                                   IsEnabled="{Binding IsNotBusy}" />
                        </Grid>
                    </StackLayout>
                </Frame>

                <!-- 搜索结果 -->
                <Frame Style="{StaticResource CardFrame}"
                       IsVisible="{Binding SearchResults.Count, Converter={StaticResource CountToBoolConverter}}">
                    <StackLayout>
                        <Label Text="搜索结果"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <CollectionView ItemsSource="{Binding SearchResults}"
                                       SelectionMode="None">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:UserInfo">
                                    <Grid Padding="0,8" ColumnDefinitions="Auto,*,Auto">
                                        
                                        <!-- 头像 -->
                                        <Frame Grid.Column="0"
                                               WidthRequest="50"
                                               HeightRequest="50"
                                               CornerRadius="25"
                                               Padding="0"
                                               BackgroundColor="{StaticResource Gray200}">
                                            <Image Source="{Binding AvatarUrl, FallbackValue='default_avatar.png'}"
                                                   Aspect="AspectFill" />
                                        </Frame>
                                        
                                        <!-- 用户信息 -->
                                        <StackLayout Grid.Column="1"
                                                    Margin="12,0,0,0"
                                                    VerticalOptions="Center">
                                            <Label Text="{Binding DisplayName, FallbackValue={Binding Username}}"
                                                   FontSize="16"
                                                   FontAttributes="Bold"
                                                   TextColor="{StaticResource TextPrimary}" />
                                            <Label Text="{Binding Username}"
                                                   FontSize="14"
                                                   TextColor="{StaticResource TextSecondary}" />
                                        </StackLayout>
                                        
                                        <!-- 添加按钮 -->
                                        <Button Grid.Column="2"
                                               Text="添加"
                                               FontSize="14"
                                               WidthRequest="60"
                                               HeightRequest="32"
                                               CornerRadius="16"
                                               Command="{Binding Source={x:Reference Name=AddFriendPage}, Path=BindingContext.AddFriendCommand}"
                                               CommandParameter="{Binding}" />
                                        
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </Frame>

                <!-- 推荐好友 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="推荐好友"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <Label Text="基于您的联系人和共同好友推荐"
                               FontSize="14"
                               TextColor="{StaticResource TextSecondary}"
                               Margin="0,0,0,16" />
                        
                        <Button Text="查看推荐"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding ViewRecommendationsCommand}" />
                    </StackLayout>
                </Frame>

                <!-- 好友请求 -->
                <Frame Style="{StaticResource CardFrame}"
                       IsVisible="{Binding FriendRequests.Count, Converter={StaticResource CountToBoolConverter}}">
                    <StackLayout>
                        <Grid ColumnDefinitions="*,Auto">
                            <Label Grid.Column="0"
                                   Text="好友请求"
                                   FontSize="16"
                                   FontAttributes="Bold"
                                   TextColor="{StaticResource TextPrimary}"
                                   VerticalOptions="Center" />
                            
                            <Label Grid.Column="1"
                                   Text="{Binding FriendRequests.Count}"
                                   FontSize="14"
                                   TextColor="White"
                                   BackgroundColor="{StaticResource Error}"
                                   WidthRequest="24"
                                   HeightRequest="24"
                                   HorizontalTextAlignment="Center"
                                   VerticalTextAlignment="Center" />
                        </Grid>
                        
                        <CollectionView ItemsSource="{Binding FriendRequests}"
                                       SelectionMode="None"
                                       Margin="0,12,0,0">
                            <CollectionView.ItemTemplate>
                                <DataTemplate x:DataType="models:FriendRequest">
                                    <Grid Padding="0,8" ColumnDefinitions="Auto,*,Auto,Auto">
                                        
                                        <!-- 头像 -->
                                        <Frame Grid.Column="0"
                                               WidthRequest="40"
                                               HeightRequest="40"
                                               CornerRadius="20"
                                               Padding="0"
                                               BackgroundColor="{StaticResource Gray200}">
                                            <Image Source="{Binding Requester.AvatarUrl, FallbackValue='default_avatar.png'}"
                                                   Aspect="AspectFill" />
                                        </Frame>
                                        
                                        <!-- 用户信息 -->
                                        <StackLayout Grid.Column="1"
                                                    Margin="12,0,8,0"
                                                    VerticalOptions="Center">
                                            <Label Text="{Binding Requester.DisplayName, FallbackValue={Binding Requester.Username}}"
                                                   FontSize="14"
                                                   FontAttributes="Bold"
                                                   TextColor="{StaticResource TextPrimary}" />
                                            <Label Text="{Binding Message}"
                                                   FontSize="12"
                                                   TextColor="{StaticResource TextSecondary}"
                                                   MaxLines="1" />
                                        </StackLayout>
                                        
                                        <!-- 接受按钮 -->
                                        <Button Grid.Column="2"
                                               Text="✓"
                                               FontSize="16"
                                               TextColor="White"
                                               BackgroundColor="{StaticResource Success}"
                                               WidthRequest="36"
                                               HeightRequest="36"
                                               CornerRadius="18"
                                               Command="{Binding Source={x:Reference Name=AddFriendPage}, Path=BindingContext.AcceptFriendRequestCommand}"
                                               CommandParameter="{Binding}" />
                                        
                                        <!-- 拒绝按钮 -->
                                        <Button Grid.Column="3"
                                               Text="✗"
                                               FontSize="16"
                                               TextColor="White"
                                               BackgroundColor="{StaticResource Error}"
                                               WidthRequest="36"
                                               HeightRequest="36"
                                               CornerRadius="18"
                                               Margin="8,0,0,0"
                                               Command="{Binding Source={x:Reference Name=AddFriendPage}, Path=BindingContext.RejectFriendRequestCommand}"
                                               CommandParameter="{Binding}" />
                                        
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  HorizontalOptions="Center"
                                  Margin="0,20,0,0" />

                <!-- 错误消息 -->
                <Frame IsVisible="{Binding HasError}"
                       BackgroundColor="{StaticResource Error}"
                       Margin="0,8,0,0">
                    <Label Text="{Binding ErrorMessage}"
                           TextColor="White"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </Frame>

            </StackLayout>
            
        </ScrollView>
        
    </Grid>

</ContentPage>
