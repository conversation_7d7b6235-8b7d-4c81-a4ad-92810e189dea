using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 密码重置令牌实体配置
/// </summary>
public class PasswordResetTokenConfiguration : IEntityTypeConfiguration<PasswordResetToken>
{
    public void Configure(EntityTypeBuilder<PasswordResetToken> builder)
    {
        // 表名
        builder.ToTable("password_reset_tokens");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.Id)
            .IsRequired()
            .ValueGeneratedOnAdd();
            
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.Token)
            .IsRequired()
            .HasMaxLength(255);
            
        builder.Property(x => x.EmailOrPhone)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.ExpiresAt)
            .IsRequired();
            
        builder.Property(x => x.IsUsed)
            .IsRequired()
            .HasDefaultValue(false);
            
        builder.Property(x => x.UsedAt)
            .IsRequired(false);
        
        // 索引
        builder.HasIndex(x => x.Token)
            .IsUnique()
            .HasDatabaseName("idx_password_reset_tokens_token");
            
        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("idx_password_reset_tokens_user_id");
            
        builder.HasIndex(x => x.EmailOrPhone)
            .HasDatabaseName("idx_password_reset_tokens_email_phone");
            
        builder.HasIndex(x => x.ExpiresAt)
            .HasDatabaseName("idx_password_reset_tokens_expires_at");
        
        // 关系配置
        builder.HasOne(x => x.User)
            .WithMany()
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
