using InfoCardSystem.Core.DTOs.Attachment;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 附件服务实现
/// </summary>
public class AttachmentService : IAttachmentService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AttachmentService> _logger;
    private readonly ICacheService _cacheService;
    private readonly IConfiguration _configuration;
    
    // 配置参数
    private readonly string _uploadPath;
    private readonly long _maxFileSize;
    private readonly HashSet<string> _allowedExtensions;
    private readonly HashSet<string> _imageExtensions;

    public AttachmentService(
        IUnitOfWork unitOfWork,
        ILogger<AttachmentService> logger,
        ICacheService cacheService,
        IConfiguration configuration)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _cacheService = cacheService;
        _configuration = configuration;
        
        // 读取配置
        _uploadPath = _configuration.GetValue<string>("FileUpload:UploadPath", "uploads") ?? "uploads";
        _maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSize", 10485760); // 10MB
        
        var allowedExtensionsConfig = _configuration.GetSection("FileUpload:AllowedExtensions").Get<string[]>();
        _allowedExtensions = allowedExtensionsConfig?.ToHashSet(StringComparer.OrdinalIgnoreCase) ?? 
            new HashSet<string>(StringComparer.OrdinalIgnoreCase) { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt" };
        
        _imageExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
        
        // 确保上传目录存在
        EnsureUploadDirectoryExists();
    }

    /// <summary>
    /// 上传单个文件
    /// </summary>
    public async Task<ApiResponse<FileUploadResponse>> UploadFileAsync(int userId, IFormFile file, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<FileUploadResponse>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // 验证文件
            var validationResult = ValidateFile(file);
            if (!validationResult.IsValid)
            {
                var response = new FileUploadResponse
                {
                    Success = false,
                    ErrorMessage = validationResult.ErrorMessage
                };
                return ApiResponse<FileUploadResponse>.ErrorResult(validationResult.ErrorMessage, ErrorCodes.VALIDATION_001);
            }

            // 生成文件信息
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var fileName = $"{Guid.NewGuid()}{fileExtension}";
            var relativePath = Path.Combine("attachments", DateTime.UtcNow.ToString("yyyy/MM/dd"));
            var fullDirectoryPath = Path.Combine(_uploadPath, relativePath);
            var fullFilePath = Path.Combine(fullDirectoryPath, fileName);

            // 确保目录存在
            Directory.CreateDirectory(fullDirectoryPath);

            // 保存文件
            using (var fileStream = new FileStream(fullFilePath, FileMode.Create))
            {
                await file.CopyToAsync(fileStream, cancellationToken);
            }

            // 创建附件记录
            var attachment = new UserAttachment
            {
                FileName = fileName,
                OriginalFileName = file.FileName,
                FilePath = Path.Combine(relativePath, fileName).Replace('\\', '/'),
                FileSize = file.Length,
                MimeType = file.ContentType ?? GetMimeType(fileExtension),
                UploaderId = userId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Attachments.AddAsync(attachment, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("文件上传成功: AttachmentId={AttachmentId}, UserId={UserId}, FileName={FileName}", 
                attachment.Id, userId, file.FileName);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_attachments_{userId}_*");

            var attachmentDto = MapToDto(attachment);
            var uploadResponse = new FileUploadResponse
            {
                Attachment = attachmentDto,
                Success = true
            };

            return ApiResponse<FileUploadResponse>.SuccessResult(uploadResponse, "文件上传成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件上传失败: UserId={UserId}, FileName={FileName}", userId, file?.FileName);
            return ApiResponse<FileUploadResponse>.ErrorResult("文件上传失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 批量上传文件
    /// </summary>
    public async Task<ApiResponse<BatchFileUploadResponse>> UploadFilesAsync(int userId, IFormFileCollection files, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = new BatchFileUploadResponse
            {
                TotalFiles = files.Count
            };

            foreach (var file in files)
            {
                var uploadResult = await UploadFileAsync(userId, file, cancellationToken);
                
                if (uploadResult.Success && uploadResult.Data != null)
                {
                    response.SuccessfulUploads.Add(uploadResult.Data.Attachment);
                    response.SuccessCount++;
                }
                else
                {
                    response.FailedUploads.Add(new FileUploadError
                    {
                        FileName = file.FileName,
                        ErrorMessage = uploadResult.Message,
                        ErrorCode = uploadResult.Error?.Code ?? "UNKNOWN"
                    });
                    response.FailureCount++;
                }
            }

            var message = $"批量上传完成: 成功 {response.SuccessCount} 个，失败 {response.FailureCount} 个";
            return ApiResponse<BatchFileUploadResponse>.SuccessResult(response, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量文件上传失败: UserId={UserId}", userId);
            return ApiResponse<BatchFileUploadResponse>.ErrorResult("批量文件上传失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 下载文件
    /// </summary>
    public async Task<ApiResponse<FileDownloadInfo>> DownloadFileAsync(int userId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查访问权限
            var hasPermission = await HasAccessPermissionAsync(userId, attachmentId, cancellationToken);
            if (!hasPermission)
            {
                return ApiResponse<FileDownloadInfo>.ErrorResult("无权访问该文件", ErrorCodes.AUTH_003);
            }

            var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId, cancellationToken);
            if (attachment == null)
            {
                return ApiResponse<FileDownloadInfo>.ErrorResult("文件不存在", ErrorCodes.NOT_FOUND_001);
            }

            var fullFilePath = Path.Combine(_uploadPath, attachment.FilePath);
            if (!File.Exists(fullFilePath))
            {
                _logger.LogWarning("文件物理路径不存在: AttachmentId={AttachmentId}, Path={Path}", attachmentId, fullFilePath);
                return ApiResponse<FileDownloadInfo>.ErrorResult("文件不存在", ErrorCodes.NOT_FOUND_001);
            }

            var fileStream = new FileStream(fullFilePath, FileMode.Open, FileAccess.Read);
            var downloadInfo = new FileDownloadInfo
            {
                FileStream = fileStream,
                FileName = attachment.OriginalFileName,
                MimeType = attachment.MimeType,
                FileSize = attachment.FileSize
            };

            _logger.LogInformation("文件下载: AttachmentId={AttachmentId}, UserId={UserId}", attachmentId, userId);
            return ApiResponse<FileDownloadInfo>.SuccessResult(downloadInfo, "文件获取成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件下载失败: UserId={UserId}, AttachmentId={AttachmentId}", userId, attachmentId);
            return ApiResponse<FileDownloadInfo>.ErrorResult("文件下载失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取附件信息
    /// </summary>
    public async Task<ApiResponse<AttachmentDto>> GetAttachmentAsync(int userId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查访问权限
            var hasPermission = await HasAccessPermissionAsync(userId, attachmentId, cancellationToken);
            if (!hasPermission)
            {
                return ApiResponse<AttachmentDto>.ErrorResult("无权访问该文件", ErrorCodes.AUTH_003);
            }

            var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId, cancellationToken);
            if (attachment == null)
            {
                return ApiResponse<AttachmentDto>.ErrorResult("附件不存在", ErrorCodes.NOT_FOUND_001);
            }

            var attachmentDto = MapToDto(attachment);
            return ApiResponse<AttachmentDto>.SuccessResult(attachmentDto, "获取附件信息成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附件信息失败: UserId={UserId}, AttachmentId={AttachmentId}", userId, attachmentId);
            return ApiResponse<AttachmentDto>.ErrorResult("获取附件信息失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 删除附件
    /// </summary>
    public async Task<ApiResponse<bool>> DeleteAttachmentAsync(int userId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId, cancellationToken);
            if (attachment == null)
            {
                return ApiResponse<bool>.ErrorResult("附件不存在", ErrorCodes.NOT_FOUND_001);
            }

            // 只有上传者可以删除附件
            if (attachment.UploaderId != userId)
            {
                return ApiResponse<bool>.ErrorResult("只能删除自己上传的附件", ErrorCodes.AUTH_003);
            }

            // 检查是否有资讯卡正在使用该附件
            var infoCardAttachments = await _unitOfWork.InfoCardAttachments.GetAllAsync(
                ica => ica.AttachmentId == attachmentId, cancellationToken);

            if (infoCardAttachments.Any())
            {
                return ApiResponse<bool>.ErrorResult("该附件正在被资讯卡使用，无法删除", ErrorCodes.BUSINESS_002);
            }

            // 删除物理文件
            var fullFilePath = Path.Combine(_uploadPath, attachment.FilePath);
            if (File.Exists(fullFilePath))
            {
                try
                {
                    File.Delete(fullFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "删除物理文件失败: Path={Path}", fullFilePath);
                }
            }

            // 删除数据库记录
            _unitOfWork.Attachments.Remove(attachment);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理缓存
            await _cacheService.RemoveByPatternAsync($"user_attachments_{userId}_*");

            _logger.LogInformation("附件删除成功: AttachmentId={AttachmentId}, UserId={UserId}", attachmentId, userId);
            return ApiResponse<bool>.SuccessResult(true, "附件删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除附件失败: UserId={UserId}, AttachmentId={AttachmentId}", userId, attachmentId);
            return ApiResponse<bool>.ErrorResult("删除附件失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取用户的附件列表
    /// </summary>
    public async Task<ApiResponse<PagedResult<AttachmentDto>>> GetUserAttachmentsAsync(int userId, AttachmentQueryParams queryParams, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"user_attachments_{userId}_{queryParams.Page}_{queryParams.PageSize}_{queryParams.FileType}_{queryParams.FileName}";

            var cachedResult = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var query = _unitOfWork.Attachments.GetQueryable()
                    .Where(a => a.UploaderId == userId);

                // 文件类型过滤
                if (!string.IsNullOrEmpty(queryParams.FileType))
                {
                    var fileType = queryParams.FileType.ToLowerInvariant();
                    if (fileType == "image")
                    {
                        query = query.Where(a => _imageExtensions.Contains(Path.GetExtension(a.FileName).ToLower()));
                    }
                    else
                    {
                        query = query.Where(a => a.MimeType.StartsWith(fileType));
                    }
                }

                // 文件名搜索
                if (!string.IsNullOrEmpty(queryParams.FileName))
                {
                    query = query.Where(a => a.OriginalFileName.Contains(queryParams.FileName));
                }

                // 日期范围过滤
                if (queryParams.StartDate.HasValue)
                {
                    query = query.Where(a => a.CreatedAt >= queryParams.StartDate.Value);
                }
                if (queryParams.EndDate.HasValue)
                {
                    query = query.Where(a => a.CreatedAt <= queryParams.EndDate.Value);
                }

                query = query.OrderByDescending(a => a.CreatedAt);

                var totalCount = await _unitOfWork.Attachments.CountAsync(query, cancellationToken);
                var attachments = await _unitOfWork.Attachments.GetPagedAsync(query, queryParams.Page, queryParams.PageSize, cancellationToken);

                var attachmentDtos = attachments.Select(MapToDto).ToList();

                return new PagedResult<AttachmentDto>
                {
                    Items = attachmentDtos,
                    Pagination = new PaginationInfo
                    {
                        CurrentPage = queryParams.Page,
                        TotalPages = (int)Math.Ceiling((double)totalCount / queryParams.PageSize),
                        TotalItems = totalCount,
                        ItemsPerPage = queryParams.PageSize
                    }
                };
            }, TimeSpan.FromMinutes(5));

            return ApiResponse<PagedResult<AttachmentDto>>.SuccessResult(cachedResult!, "获取附件列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户附件列表失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<AttachmentDto>>.ErrorResult("获取附件列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取资讯卡的附件列表
    /// </summary>
    public async Task<ApiResponse<List<AttachmentDto>>> GetInfoCardAttachmentsAsync(int userId, int infoCardId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查用户是否有访问该资讯卡的权限
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<List<AttachmentDto>>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // TODO: 检查用户是否有访问该资讯卡的权限（通过接收者表）

            var infoCardAttachments = await _unitOfWork.InfoCardAttachments.GetAllAsync(
                ica => ica.InfoCardId == infoCardId, cancellationToken);

            var attachmentIds = infoCardAttachments.Select(ica => ica.AttachmentId).ToList();
            var attachments = await _unitOfWork.Attachments.GetAllAsync(
                a => attachmentIds.Contains(a.Id), cancellationToken);

            var attachmentDtos = attachments.Select(MapToDto).ToList();
            return ApiResponse<List<AttachmentDto>>.SuccessResult(attachmentDtos, "获取资讯卡附件列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取资讯卡附件列表失败: UserId={UserId}, InfoCardId={InfoCardId}", userId, infoCardId);
            return ApiResponse<List<AttachmentDto>>.ErrorResult("获取资讯卡附件列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 关联附件到资讯卡
    /// </summary>
    public async Task<ApiResponse<bool>> AttachToInfoCardAsync(int userId, int infoCardId, AttachToInfoCardRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查资讯卡是否存在且用户有权限
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<bool>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // 只有资讯卡的发布者可以关联附件
            if (infoCard.DirectPublisherId != userId)
            {
                return ApiResponse<bool>.ErrorResult("只能为自己发布的资讯卡关联附件", ErrorCodes.AUTH_003);
            }

            // 验证所有附件都属于当前用户
            var attachments = await _unitOfWork.Attachments.GetAllAsync(
                a => request.AttachmentIds.Contains(a.Id), cancellationToken);

            var invalidAttachments = attachments.Where(a => a.UploaderId != userId).ToList();
            if (invalidAttachments.Any())
            {
                return ApiResponse<bool>.ErrorResult("只能关联自己上传的附件", ErrorCodes.AUTH_003);
            }

            if (attachments.Count != request.AttachmentIds.Count)
            {
                return ApiResponse<bool>.ErrorResult("部分附件不存在", ErrorCodes.NOT_FOUND_001);
            }

            // 检查是否已经关联
            var existingAttachments = await _unitOfWork.InfoCardAttachments.GetAllAsync(
                ica => ica.InfoCardId == infoCardId && request.AttachmentIds.Contains(ica.AttachmentId),
                cancellationToken);

            var existingAttachmentIds = existingAttachments.Select(ica => ica.AttachmentId).ToList();
            var newAttachmentIds = request.AttachmentIds.Except(existingAttachmentIds).ToList();

            if (!newAttachmentIds.Any())
            {
                return ApiResponse<bool>.ErrorResult("所选附件都已经关联到该资讯卡", ErrorCodes.BUSINESS_002);
            }

            // 创建新的关联记录
            var newAttachments = newAttachmentIds.Select(attachmentId => new UserInfoCardAttachment
            {
                InfoCardId = infoCardId,
                AttachmentId = attachmentId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }).ToList();

            await _unitOfWork.InfoCardAttachments.AddRangeAsync(newAttachments, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("附件关联到资讯卡成功: InfoCardId={InfoCardId}, UserId={UserId}, AttachmentCount={Count}",
                infoCardId, userId, newAttachments.Count);

            return ApiResponse<bool>.SuccessResult(true, $"成功关联 {newAttachments.Count} 个附件");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "关联附件到资讯卡失败: UserId={UserId}, InfoCardId={InfoCardId}", userId, infoCardId);
            return ApiResponse<bool>.ErrorResult("关联附件失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 从资讯卡移除附件关联
    /// </summary>
    public async Task<ApiResponse<bool>> DetachFromInfoCardAsync(int userId, int infoCardId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查资讯卡是否存在且用户有权限
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<bool>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // 只有资讯卡的发布者可以移除附件关联
            if (infoCard.DirectPublisherId != userId)
            {
                return ApiResponse<bool>.ErrorResult("只能移除自己发布的资讯卡的附件", ErrorCodes.AUTH_003);
            }

            // 查找关联记录
            var infoCardAttachment = await _unitOfWork.InfoCardAttachments.FirstOrDefaultAsync(
                ica => ica.InfoCardId == infoCardId && ica.AttachmentId == attachmentId,
                cancellationToken);

            if (infoCardAttachment == null)
            {
                return ApiResponse<bool>.ErrorResult("附件未关联到该资讯卡", ErrorCodes.NOT_FOUND_001);
            }

            // 移除关联
            _unitOfWork.InfoCardAttachments.Remove(infoCardAttachment);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("从资讯卡移除附件关联成功: InfoCardId={InfoCardId}, AttachmentId={AttachmentId}, UserId={UserId}",
                infoCardId, attachmentId, userId);

            return ApiResponse<bool>.SuccessResult(true, "移除附件关联成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除附件关联失败: UserId={UserId}, InfoCardId={InfoCardId}, AttachmentId={AttachmentId}",
                userId, infoCardId, attachmentId);
            return ApiResponse<bool>.ErrorResult("移除附件关联失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 检查用户是否有访问附件的权限
    /// </summary>
    public async Task<bool> HasAccessPermissionAsync(int userId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId, cancellationToken);
            if (attachment == null)
            {
                return false;
            }

            // 上传者总是有权限
            if (attachment.UploaderId == userId)
            {
                return true;
            }

            // 检查是否通过资讯卡有访问权限
            var infoCardAttachments = await _unitOfWork.InfoCardAttachments.GetAllAsync(
                ica => ica.AttachmentId == attachmentId, cancellationToken);

            foreach (var infoCardAttachment in infoCardAttachments)
            {
                // 检查用户是否是该资讯卡的接收者
                var recipients = await _unitOfWork.InfoCardRecipients.GetAllAsync(
                    icr => icr.InfoCardId == infoCardAttachment.InfoCardId && icr.RecipientId == userId,
                    cancellationToken);
                var isRecipient = recipients.Any();

                if (isRecipient)
                {
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查附件访问权限失败: UserId={UserId}, AttachmentId={AttachmentId}", userId, attachmentId);
            return false;
        }
    }

    /// <summary>
    /// 获取文件预览URL（仅图片）
    /// </summary>
    public async Task<ApiResponse<string>> GetPreviewUrlAsync(int userId, int attachmentId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查访问权限
            var hasPermission = await HasAccessPermissionAsync(userId, attachmentId, cancellationToken);
            if (!hasPermission)
            {
                return ApiResponse<string>.ErrorResult("无权访问该文件", ErrorCodes.AUTH_003);
            }

            var attachment = await _unitOfWork.Attachments.GetByIdAsync(attachmentId, cancellationToken);
            if (attachment == null)
            {
                return ApiResponse<string>.ErrorResult("附件不存在", ErrorCodes.NOT_FOUND_001);
            }

            var fileExtension = Path.GetExtension(attachment.FileName).ToLowerInvariant();
            if (!_imageExtensions.Contains(fileExtension))
            {
                return ApiResponse<string>.ErrorResult("该文件不是图片，无法预览", ErrorCodes.BUSINESS_001);
            }

            var previewUrl = $"/api/v1/attachments/{attachmentId}/preview";
            return ApiResponse<string>.SuccessResult(previewUrl, "获取预览URL成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取预览URL失败: UserId={UserId}, AttachmentId={AttachmentId}", userId, attachmentId);
            return ApiResponse<string>.ErrorResult("获取预览URL失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 验证文件
    /// </summary>
    private (bool IsValid, string ErrorMessage) ValidateFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
        {
            return (false, "文件不能为空");
        }

        if (file.Length > _maxFileSize)
        {
            return (false, $"文件大小不能超过 {FormatFileSize(_maxFileSize)}");
        }

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!_allowedExtensions.Contains(extension))
        {
            return (false, $"不支持的文件类型: {extension}");
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// 获取MIME类型
    /// </summary>
    private static string GetMimeType(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".webp" => "image/webp",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".txt" => "text/plain",
            ".zip" => "application/zip",
            ".rar" => "application/x-rar-compressed",
            ".mp4" => "video/mp4",
            ".avi" => "video/x-msvideo",
            ".mp3" => "audio/mpeg",
            ".wav" => "audio/wav",
            _ => "application/octet-stream"
        };
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    /// <summary>
    /// 确保上传目录存在
    /// </summary>
    private void EnsureUploadDirectoryExists()
    {
        if (!Directory.Exists(_uploadPath))
        {
            Directory.CreateDirectory(_uploadPath);
        }
    }

    /// <summary>
    /// 映射到DTO
    /// </summary>
    private AttachmentDto MapToDto(UserAttachment attachment)
    {
        var fileExtension = Path.GetExtension(attachment.FileName).ToLowerInvariant();
        var isImage = _imageExtensions.Contains(fileExtension);

        return new AttachmentDto
        {
            Id = attachment.Id,
            FileName = attachment.FileName,
            OriginalFileName = attachment.OriginalFileName,
            FilePath = attachment.FilePath,
            FileSize = attachment.FileSize,
            FormattedFileSize = FormatFileSize(attachment.FileSize),
            MimeType = attachment.MimeType,
            FileType = GetFileType(fileExtension),
            UploaderId = attachment.UploaderId,
            DownloadUrl = $"/api/v1/attachments/{attachment.Id}/download",
            PreviewUrl = isImage ? $"/api/v1/attachments/{attachment.Id}/preview" : null,
            IsImage = isImage,
            CreatedAt = attachment.CreatedAt
        };
    }

    /// <summary>
    /// 获取文件类型
    /// </summary>
    private static string GetFileType(string extension)
    {
        return extension.ToLowerInvariant() switch
        {
            ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => "image",
            ".pdf" => "pdf",
            ".doc" or ".docx" => "document",
            ".txt" => "text",
            ".zip" or ".rar" or ".7z" => "archive",
            ".mp4" or ".avi" or ".mov" => "video",
            ".mp3" or ".wav" or ".flac" => "audio",
            _ => "other"
        };
    }
}
