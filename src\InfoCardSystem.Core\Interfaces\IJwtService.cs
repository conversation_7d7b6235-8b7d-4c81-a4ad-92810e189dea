using InfoCardSystem.Core.Entities;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// JWT服务接口
/// </summary>
public interface IJwtService
{
    /// <summary>
    /// 生成访问令牌
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>JWT令牌</returns>
    string GenerateAccessToken(AppUser user);
    
    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    /// <returns>刷新令牌</returns>
    string GenerateRefreshToken();
    
    /// <summary>
    /// 验证令牌
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>是否有效</returns>
    bool ValidateToken(string token);
    
    /// <summary>
    /// 从令牌中获取用户ID
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>用户ID</returns>
    int? GetUserIdFromToken(string token);
    
    /// <summary>
    /// 获取令牌过期时间（秒）
    /// </summary>
    /// <returns>过期时间</returns>
    int GetTokenExpirationTime();
}
