using InfoCardSystem.Core.DTOs.Attachment;
using InfoCardSystem.Core.DTOs.Common;
using Microsoft.AspNetCore.Http;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 附件服务接口
/// </summary>
public interface IAttachmentService
{
    /// <summary>
    /// 上传单个文件
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="file">文件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>上传结果</returns>
    Task<ApiResponse<FileUploadResponse>> UploadFileAsync(int userId, IFormFile file, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量上传文件
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="files">文件列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量上传结果</returns>
    Task<ApiResponse<BatchFileUploadResponse>> UploadFilesAsync(int userId, IFormFileCollection files, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 下载文件
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>文件下载信息</returns>
    Task<ApiResponse<FileDownloadInfo>> DownloadFileAsync(int userId, int attachmentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取附件信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件信息</returns>
    Task<ApiResponse<AttachmentDto>> GetAttachmentAsync(int userId, int attachmentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除附件
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeleteAttachmentAsync(int userId, int attachmentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的附件列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="queryParams">查询参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件列表</returns>
    Task<ApiResponse<PagedResult<AttachmentDto>>> GetUserAttachmentsAsync(int userId, AttachmentQueryParams queryParams, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取资讯卡的附件列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件列表</returns>
    Task<ApiResponse<List<AttachmentDto>>> GetInfoCardAttachmentsAsync(int userId, int infoCardId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 关联附件到资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="request">关联请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> AttachToInfoCardAsync(int userId, int infoCardId, AttachToInfoCardRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 从资讯卡移除附件关联
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DetachFromInfoCardAsync(int userId, int infoCardId, int attachmentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否有访问附件的权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasAccessPermissionAsync(int userId, int attachmentId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取文件预览URL（仅图片）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预览URL</returns>
    Task<ApiResponse<string>> GetPreviewUrlAsync(int userId, int attachmentId, CancellationToken cancellationToken = default);
}
