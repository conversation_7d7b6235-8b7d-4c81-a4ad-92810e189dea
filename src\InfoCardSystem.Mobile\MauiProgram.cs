using InfoCardSystem.Mobile.Services;
using InfoCardSystem.Mobile.ViewModels;
using InfoCardSystem.Mobile.Views;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

        // 配置HTTP客户端
        builder.Services.AddHttpClient("InfoCardApi", client =>
        {
            // 使用更安全的默认配置，避免网络连接导致的闪退
            try
            {
                client.BaseAddress = new Uri("http://localhost:8081/api/v1/"); // Android模拟器访问本地API
                client.DefaultRequestHeaders.Add("User-Agent", "InfoCard-Android/1.0");
                client.Timeout = TimeSpan.FromSeconds(10); // 减少超时时间
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HTTP client configuration error: {ex}");
            }
        });

        // 注册平台服务
        builder.Services.AddSingleton<IConnectivity>(Connectivity.Current);
        builder.Services.AddSingleton<ISecureStorage>(SecureStorage.Default);
        builder.Services.AddSingleton<IPreferences>(Preferences.Default);

        // 注册应用服务
        try
        {
            builder.Services.AddAppServices();
            builder.Services.AddViewModels();
            builder.Services.AddViews();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Service registration error: {ex}");
        }

#if DEBUG
        builder.Logging.AddDebug();
        builder.Logging.SetMinimumLevel(LogLevel.Debug);
#endif

        var app = builder.Build();

        // 添加全局异常处理
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            System.Diagnostics.Debug.WriteLine($"Unhandled exception: {e.ExceptionObject}");
        };

        TaskScheduler.UnobservedTaskException += (sender, e) =>
        {
            System.Diagnostics.Debug.WriteLine($"Unobserved task exception: {e.Exception}");
            e.SetObserved();
        };

        return app;
    }
}

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAppServices(this IServiceCollection services)
    {
        // 核心服务
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddSingleton<IDialogService, DialogService>();
        services.AddSingleton<IPreferencesService, PreferencesService>();
        services.AddSingleton<ISecureStorageService, SecureStorageService>();

        // API服务
        services.AddSingleton<IInfoCardApiClient, InfoCardApiClient>();
        services.AddSingleton<IAuthenticationService, AuthenticationService>();

        // 本地服务
        services.AddSingleton<ILocalDatabaseService, LocalDatabaseService>();
        services.AddSingleton<ICacheService, CacheService>();
        services.AddSingleton<IFileUploadService, FileUploadService>();

        return services;
    }

    public static IServiceCollection AddViewModels(this IServiceCollection services)
    {
        // 认证ViewModels
        services.AddTransient<LoginViewModel>();
        services.AddTransient<RegisterViewModel>();
        services.AddTransient<ForgotPasswordViewModel>();

        // 主要ViewModels
        services.AddTransient<InfoCardsViewModel>();
        services.AddTransient<ProfileViewModel>();
        services.AddTransient<FriendsViewModel>();
        services.AddTransient<AddFriendViewModel>();
        services.AddTransient<CreateInfoCardViewModel>();
        services.AddTransient<SettingsViewModel>();
        services.AddTransient<AboutViewModel>();
        services.AddTransient<MyQRCodeViewModel>();

        return services;
    }

    public static IServiceCollection AddViews(this IServiceCollection services)
    {
        // 启动页面
        services.AddTransient<SplashPage>();

        // 认证Views
        services.AddTransient<LoginPage>();
        services.AddTransient<RegisterPage>();
        services.AddTransient<ForgotPasswordPage>();

        // 主要Views
        services.AddTransient<InfoCardsPage>();
        services.AddTransient<MyInfoCardsPage>();
        services.AddTransient<FriendsPage>();
        services.AddTransient<ProfilePage>();
        services.AddTransient<SettingsPage>();
        services.AddTransient<AddFriendPage>();
        services.AddTransient<CreateInfoCardPage>();
        services.AddTransient<AboutPage>();
        services.AddTransient<MyQRCodePage>();

        return services;
    }
}
