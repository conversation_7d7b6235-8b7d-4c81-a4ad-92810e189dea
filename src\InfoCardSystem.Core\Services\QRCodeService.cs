using InfoCardSystem.Core.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 二维码服务实现
/// </summary>
public class QRCodeService : IQRCodeService
{
    private readonly ILogger<QRCodeService> _logger;

    public QRCodeService(ILogger<QRCodeService> logger)
    {
        _logger = logger;
    }

    public byte[] GenerateQRCode(string data, int size = 256)
    {
        try
        {
            // 使用简单的实现生成二维码占位符
            // 在生产环境中，建议使用QRCoder或其他二维码库

            // 创建一个简单的二维码占位符图像
            var qrCodeText = $"QR:{data.Substring(0, Math.Min(data.Length, 50))}...";
            return System.Text.Encoding.UTF8.GetBytes(qrCodeText);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成二维码失败: {Data}", data);
            throw;
        }
    }

    public string GenerateQRCodeBase64(string data, int size = 256)
    {
        try
        {
            // 创建一个简单的PNG二维码占位符
            // 在生产环境中，建议使用QRCoder库生成真正的二维码

            // 创建一个简单的SVG二维码占位符
            var svg = $@"<svg width=""{size}"" height=""{size}"" xmlns=""http://www.w3.org/2000/svg"">
                <rect width=""{size}"" height=""{size}"" fill=""white"" stroke=""#ccc"" stroke-width=""2""/>
                <rect x=""20"" y=""20"" width=""{size - 40}"" height=""{size - 40}"" fill=""black""/>
                <rect x=""40"" y=""40"" width=""{size - 80}"" height=""{size - 80}"" fill=""white""/>
                <text x=""{size / 2}"" y=""{size / 2 - 10}"" text-anchor=""middle"" font-family=""Arial"" font-size=""14"" font-weight=""bold"" fill=""black"">InfoCard</text>
                <text x=""{size / 2}"" y=""{size / 2 + 10}"" text-anchor=""middle"" font-family=""Arial"" font-size=""10"" fill=""black"">QR Code</text>
            </svg>";

            var svgBytes = System.Text.Encoding.UTF8.GetBytes(svg);
            return Convert.ToBase64String(svgBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成二维码Base64失败: {Data}", data);
            throw;
        }
    }

    public QRCodeParseResult ParseQRCodeData(string qrCodeData)
    {
        try
        {
            _logger.LogInformation("解析二维码数据");

            if (string.IsNullOrWhiteSpace(qrCodeData))
            {
                return new QRCodeParseResult
                {
                    Success = false,
                    ErrorMessage = "二维码数据为空"
                };
            }

            // 尝试解析JSON格式的二维码数据
            var qrData = JsonSerializer.Deserialize<QRCodeData>(qrCodeData);
            
            if (qrData == null)
            {
                return new QRCodeParseResult
                {
                    Success = false,
                    ErrorMessage = "无法解析二维码数据"
                };
            }

            // 验证二维码类型
            if (qrData.Type != "InfoCardUser")
            {
                return new QRCodeParseResult
                {
                    Success = false,
                    ErrorMessage = "不支持的二维码类型"
                };
            }

            // 检查时间戳（防止重放攻击）
            var timestamp = DateTimeOffset.FromUnixTimeSeconds(qrData.Timestamp);
            if (timestamp < DateTimeOffset.UtcNow.AddDays(-30))
            {
                return new QRCodeParseResult
                {
                    Success = false,
                    ErrorMessage = "二维码数据过旧"
                };
            }

            var expiresAt = qrData.ExpiresAt.HasValue 
                ? DateTimeOffset.FromUnixTimeSeconds(qrData.ExpiresAt.Value).DateTime
                : (DateTime?)null;

            return new QRCodeParseResult
            {
                Success = true,
                UserId = qrData.UserId,
                CustomUserId = qrData.CustomUserId,
                ExpiresAt = expiresAt
            };
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "二维码数据JSON解析失败: {Data}", qrCodeData);
            return new QRCodeParseResult
            {
                Success = false,
                ErrorMessage = "二维码数据格式错误"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析二维码数据时发生错误: {Data}", qrCodeData);
            return new QRCodeParseResult
            {
                Success = false,
                ErrorMessage = "解析二维码数据失败"
            };
        }
    }

    public bool ValidateQRCodeData(string qrCodeData)
    {
        try
        {
            var parseResult = ParseQRCodeData(qrCodeData);
            return parseResult.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证二维码数据时发生错误: {Data}", qrCodeData);
            return false;
        }
    }

    /// <summary>
    /// 二维码数据结构
    /// </summary>
    private class QRCodeData
    {
        public string Type { get; set; } = string.Empty;
        public int? UserId { get; set; }
        public string? CustomUserId { get; set; }
        public string? Username { get; set; }
        public long Timestamp { get; set; }
        public long? ExpiresAt { get; set; }
    }
}
