namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 文件上传响应DTO
/// </summary>
public class FileUploadResponseDto
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public int FileId { get; set; }
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public string ContentType { get; set; } = string.Empty;
    
    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadedAt { get; set; }
}
