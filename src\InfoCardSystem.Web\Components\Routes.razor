﻿@using Blazored.Toast.Configuration
@using Blazored.Toast.Services
@using Blazored.Toast

<BlazoredToasts Position="ToastPosition.TopRight"
                Timeout="5"
                IconType="IconType.FontAwesome"
                SuccessClass="success-toast-override"
                SuccessIcon="fas fa-thumbs-up"
                ErrorIcon="fas fa-bug" />

<Router AppAssembly="typeof(Program).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
</Router>
