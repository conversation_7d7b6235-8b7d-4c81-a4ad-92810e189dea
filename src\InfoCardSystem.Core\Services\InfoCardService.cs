using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.InfoCard;
using InfoCardSystem.Core.DTOs.User;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using InfoCardSystem.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 资讯卡服务实现
/// </summary>
public class InfoCardService : IInfoCardService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<InfoCardService> _logger;

    public InfoCardService(IUnitOfWork unitOfWork, ILogger<InfoCardService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// 创建资讯卡
    /// </summary>
    public async Task<ApiResponse<InfoCardDto>> CreateInfoCardAsync(int userId, CreateInfoCardRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var infoCard = new UserInfoCard
            {
                OriginalPublisherId = userId,
                DirectPublisherId = userId,
                Title = request.Title,
                Content = request.Content,
                InfoCardType = (InfoCardType)request.InfoCardType,
                AllowForward = request.AllowForwarding,
                ExpiresAt = request.ExpiresAt
            };

            await _unitOfWork.InfoCards.AddAsync(infoCard, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 添加接收者
            if (request.RecipientUserIds?.Any() == true)
            {
                var recipients = request.RecipientUserIds.Select(recipientId => new UserInfoCardRecipient
                {
                    InfoCardId = infoCard.Id,
                    RecipientId = recipientId,
                    RecipientType = RecipientType.UserRecipient,
                    ReceivedAt = DateTime.UtcNow
                }).ToList();

                await _unitOfWork.InfoCardRecipients.AddRangeAsync(recipients, cancellationToken);
            }

            if (request.RecipientGroupIds?.Any() == true)
            {
                var groupRecipients = request.RecipientGroupIds.Select(groupId => new UserInfoCardRecipient
                {
                    InfoCardId = infoCard.Id,
                    RecipientId = groupId,
                    RecipientType = RecipientType.GroupRecipient,
                    ReceivedAt = DateTime.UtcNow
                }).ToList();

                await _unitOfWork.InfoCardRecipients.AddRangeAsync(groupRecipients, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("资讯卡创建成功: InfoCardId={InfoCardId}, UserId={UserId}", infoCard.Id, userId);

            // 获取完整的资讯卡信息
            var result = await GetInfoCardAsync(userId, infoCard.Id, cancellationToken);
            return result.Success ? result : ApiResponse<InfoCardDto>.ErrorResult("创建资讯卡失败", ErrorCodes.SERVER_001);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建资讯卡失败: UserId={UserId}", userId);
            return ApiResponse<InfoCardDto>.ErrorResult("创建资讯卡失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取资讯卡详情
    /// </summary>
    public async Task<ApiResponse<InfoCardDto>> GetInfoCardAsync(int userId, int infoCardId, CancellationToken cancellationToken = default)
    {
        try
        {
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<InfoCardDto>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // 检查用户是否有权限查看
            if (!await CanUserViewInfoCardAsync(userId, infoCard, cancellationToken))
            {
                return ApiResponse<InfoCardDto>.ErrorResult("资讯卡对当前用户不可见", ErrorCodes.INFOCARD_005);
            }

            // 检查是否过期
            if (infoCard.ExpiresAt.HasValue && infoCard.ExpiresAt.Value < DateTime.UtcNow)
            {
                return ApiResponse<InfoCardDto>.ErrorResult("资讯卡已过期", ErrorCodes.INFOCARD_003);
            }

            var publisher = await _unitOfWork.Users.GetByIdAsync(infoCard.OriginalPublisherId, cancellationToken);
            if (publisher == null)
            {
                return ApiResponse<InfoCardDto>.ErrorResult("发布者不存在", ErrorCodes.USER_001);
            }

            var infoCardDto = new InfoCardDto
            {
                Id = infoCard.Id,
                Publisher = new UserProfileDto
                {
                    Id = publisher.Id,
                    CustomUserId = publisher.CustomUserId,
                    Username = publisher.Username,
                    Email = publisher.Email,
                    Phone = publisher.Phone,
                    AvatarUrl = publisher.AvatarUrl,
                    Bio = publisher.Bio,
                    UserStatus = publisher.UserStatus.ToString(),
                    CreatedAt = publisher.CreatedAt,
                    UpdatedAt = publisher.UpdatedAt
                },
                Title = infoCard.Title,
                Content = infoCard.Content,
                InfoCardType = infoCard.InfoCardType.ToString(),
                Visibility = "Public", // 简化处理
                AllowForwarding = infoCard.AllowForward,
                ExpiresAt = infoCard.ExpiresAt,
                CreatedAt = infoCard.CreatedAt,
                UpdatedAt = infoCard.UpdatedAt
            };

            return ApiResponse<InfoCardDto>.SuccessResult(infoCardDto, "获取资讯卡成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取资讯卡失败: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
            return ApiResponse<InfoCardDto>.ErrorResult("获取资讯卡失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 更新资讯卡
    /// </summary>
    public async Task<ApiResponse<InfoCardDto>> UpdateInfoCardAsync(int userId, int infoCardId, UpdateInfoCardRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<InfoCardDto>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            if (infoCard.OriginalPublisherId != userId)
            {
                return ApiResponse<InfoCardDto>.ErrorResult("只能修改自己发布的资讯卡", ErrorCodes.AUTH_002);
            }

            // 更新字段
            if (!string.IsNullOrEmpty(request.Title))
                infoCard.Title = request.Title;

            if (!string.IsNullOrEmpty(request.Content))
                infoCard.Content = request.Content;

            if (request.AllowForwarding.HasValue)
                infoCard.AllowForward = request.AllowForwarding.Value;
            
            if (request.ExpiresAt.HasValue)
                infoCard.ExpiresAt = request.ExpiresAt;

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("资讯卡更新成功: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);

            return await GetInfoCardAsync(userId, infoCardId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新资讯卡失败: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
            return ApiResponse<InfoCardDto>.ErrorResult("更新资讯卡失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 删除资讯卡
    /// </summary>
    public async Task<ApiResponse<bool>> DeleteInfoCardAsync(int userId, int infoCardId, CancellationToken cancellationToken = default)
    {
        try
        {
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<bool>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            if (infoCard.OriginalPublisherId != userId)
            {
                return ApiResponse<bool>.ErrorResult("只能删除自己发布的资讯卡", ErrorCodes.AUTH_002);
            }

            _unitOfWork.InfoCards.Remove(infoCard);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("资讯卡删除成功: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
            return ApiResponse<bool>.SuccessResult(true, "资讯卡删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除资讯卡失败: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
            return ApiResponse<bool>.ErrorResult("删除资讯卡失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 检查用户是否可以查看资讯卡
    /// </summary>
    private async Task<bool> CanUserViewInfoCardAsync(int userId, UserInfoCard infoCard, CancellationToken cancellationToken)
    {
        // 发布者总是可以查看自己的资讯卡
        if (infoCard.OriginalPublisherId == userId)
            return true;

        // 简化权限检查 - 暂时允许所有用户查看
        // TODO: 根据实际的可见性规则实现权限检查
        return true;
    }

    /// <summary>
    /// 获取用户的资讯卡列表
    /// </summary>
    public async Task<ApiResponse<PagedResult<InfoCardDto>>> GetUserInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _unitOfWork.InfoCards.GetQueryable()
                .Where(ic => ic.OriginalPublisherId == userId)
                .OrderByDescending(ic => ic.CreatedAt);

            var totalCount = await _unitOfWork.InfoCards.CountAsync(query, cancellationToken);
            var infoCards = await _unitOfWork.InfoCards.GetPagedAsync(query, page, pageSize, cancellationToken);

            var infoCardDtos = new List<InfoCardDto>();

            foreach (var infoCard in infoCards)
            {
                var publisher = await _unitOfWork.Users.GetByIdAsync(infoCard.OriginalPublisherId, cancellationToken);
                if (publisher != null)
                {
                    infoCardDtos.Add(new InfoCardDto
                    {
                        Id = infoCard.Id,
                        Publisher = new UserProfileDto
                        {
                            Id = publisher.Id,
                            CustomUserId = publisher.CustomUserId,
                            Username = publisher.Username,
                            Email = publisher.Email,
                            Phone = publisher.Phone,
                            AvatarUrl = publisher.AvatarUrl,
                            Bio = publisher.Bio,
                            UserStatus = publisher.UserStatus.ToString(),
                            CreatedAt = publisher.CreatedAt,
                            UpdatedAt = publisher.UpdatedAt
                        },
                        Title = infoCard.Title,
                        Content = infoCard.Content,
                        InfoCardType = infoCard.InfoCardType.ToString(),
                        Visibility = "Public",
                        AllowForwarding = infoCard.AllowForward,
                        ExpiresAt = infoCard.ExpiresAt,
                        CreatedAt = infoCard.CreatedAt,
                        UpdatedAt = infoCard.UpdatedAt
                    });
                }
            }

            var pagedResult = new PagedResult<InfoCardDto>
            {
                Items = infoCardDtos,
                Pagination = new PaginationInfo
                {
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    TotalItems = totalCount,
                    ItemsPerPage = pageSize
                }
            };

            return ApiResponse<PagedResult<InfoCardDto>>.SuccessResult(pagedResult, "获取用户资讯卡列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户资讯卡列表失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("获取用户资讯卡列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取用户可见的资讯卡列表（时间线）
    /// </summary>
    public async Task<ApiResponse<PagedResult<InfoCardDto>>> GetTimelineInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取用户的好友列表
            var friendIds = await _unitOfWork.Friendships
                .GetQueryable()
                .Where(f => f.UserId == userId && f.FriendshipStatus == FriendshipStatus.Accepted)
                .Select(f => f.FriendId)
                .ToListAsync(cancellationToken);

            // 添加用户自己的ID
            friendIds.Add(userId);

            // 获取好友和自己发布的资讯卡，按时间倒序
            var query = _unitOfWork.InfoCards.GetQueryable()
                .Where(ic => friendIds.Contains(ic.OriginalPublisherId) &&
                           ic.InfoCardStatus == InfoCardStatus.Active &&
                           (!ic.ExpiresAt.HasValue || ic.ExpiresAt.Value > DateTime.UtcNow))
                .OrderByDescending(ic => ic.CreatedAt);

            var totalCount = await _unitOfWork.InfoCards.CountAsync(query, cancellationToken);
            var infoCards = await _unitOfWork.InfoCards.GetPagedAsync(query, page, pageSize, cancellationToken);

            var infoCardDtos = new List<InfoCardDto>();

            foreach (var infoCard in infoCards)
            {
                // 检查用户是否有权限查看此资讯卡
                if (await CanUserViewInfoCardAsync(userId, infoCard, cancellationToken))
                {
                    var publisher = await _unitOfWork.Users.GetByIdAsync(infoCard.OriginalPublisherId, cancellationToken);
                    if (publisher != null)
                    {
                        infoCardDtos.Add(new InfoCardDto
                        {
                            Id = infoCard.Id,
                            Publisher = new UserProfileDto
                            {
                                Id = publisher.Id,
                                CustomUserId = publisher.CustomUserId,
                                Username = publisher.Username,
                                Email = publisher.Email,
                                Phone = publisher.Phone,
                                AvatarUrl = publisher.AvatarUrl,
                                Bio = publisher.Bio,
                                UserStatus = publisher.UserStatus.ToString(),
                                CreatedAt = publisher.CreatedAt,
                                UpdatedAt = publisher.UpdatedAt
                            },
                            Title = infoCard.Title,
                            Content = infoCard.Content,
                            InfoCardType = infoCard.InfoCardType.ToString(),
                            Visibility = "Public",
                            AllowForwarding = infoCard.AllowForward,
                            ExpiresAt = infoCard.ExpiresAt,
                            CreatedAt = infoCard.CreatedAt,
                            UpdatedAt = infoCard.UpdatedAt
                        });
                    }
                }
            }

            var pagedResult = new PagedResult<InfoCardDto>
            {
                Items = infoCardDtos,
                Pagination = new PaginationInfo
                {
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    TotalItems = totalCount,
                    ItemsPerPage = pageSize
                }
            };

            return ApiResponse<PagedResult<InfoCardDto>>.SuccessResult(pagedResult, "获取时间线成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取时间线失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("获取时间线失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 转发资讯卡
    /// </summary>
    public async Task<ApiResponse<bool>> ForwardInfoCardAsync(int userId, int infoCardId, List<int>? recipientUserIds = null, List<int>? recipientGroupIds = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var originalInfoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (originalInfoCard == null)
            {
                return ApiResponse<bool>.ErrorResult("原资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // 检查是否允许转发
            if (!originalInfoCard.AllowForward)
            {
                return ApiResponse<bool>.ErrorResult("该资讯卡不允许转发", ErrorCodes.INFOCARD_004);
            }

            // 检查用户是否有权限查看原资讯卡
            if (!await CanUserViewInfoCardAsync(userId, originalInfoCard, cancellationToken))
            {
                return ApiResponse<bool>.ErrorResult("无权转发该资讯卡", ErrorCodes.INFOCARD_005);
            }

            // 检查是否过期
            if (originalInfoCard.ExpiresAt.HasValue && originalInfoCard.ExpiresAt.Value < DateTime.UtcNow)
            {
                return ApiResponse<bool>.ErrorResult("资讯卡已过期，无法转发", ErrorCodes.INFOCARD_003);
            }

            // 创建转发的资讯卡
            var forwardedInfoCard = new UserInfoCard
            {
                OriginalPublisherId = originalInfoCard.OriginalPublisherId, // 保持原发布者
                DirectPublisherId = userId, // 当前转发者
                ParentInfoCardId = originalInfoCard.Id, // 指向原资讯卡
                Title = originalInfoCard.Title,
                Content = originalInfoCard.Content,
                InfoCardType = originalInfoCard.InfoCardType,
                AllowForward = originalInfoCard.AllowForward,
                ExpiresAt = originalInfoCard.ExpiresAt,
                InfoCardStatus = InfoCardStatus.Active
            };

            await _unitOfWork.InfoCards.AddAsync(forwardedInfoCard, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 添加接收者
            if (recipientUserIds?.Any() == true)
            {
                var recipients = recipientUserIds.Select(recipientId => new UserInfoCardRecipient
                {
                    InfoCardId = forwardedInfoCard.Id,
                    RecipientId = recipientId,
                    RecipientType = RecipientType.UserRecipient,
                    ReceivedAt = DateTime.UtcNow
                }).ToList();

                await _unitOfWork.InfoCardRecipients.AddRangeAsync(recipients, cancellationToken);
            }

            if (recipientGroupIds?.Any() == true)
            {
                var groupRecipients = recipientGroupIds.Select(groupId => new UserInfoCardRecipient
                {
                    InfoCardId = forwardedInfoCard.Id,
                    RecipientId = groupId,
                    RecipientType = RecipientType.GroupRecipient,
                    ReceivedAt = DateTime.UtcNow
                }).ToList();

                await _unitOfWork.InfoCardRecipients.AddRangeAsync(groupRecipients, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("资讯卡转发成功: OriginalInfoCardId={OriginalInfoCardId}, ForwardedInfoCardId={ForwardedInfoCardId}, UserId={UserId}",
                infoCardId, forwardedInfoCard.Id, userId);

            return ApiResponse<bool>.SuccessResult(true, "资讯卡转发成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转发资讯卡失败: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
            return ApiResponse<bool>.ErrorResult("转发资讯卡失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 收藏/取消收藏资讯卡
    /// </summary>
    public async Task<ApiResponse<bool>> ToggleFavoriteAsync(int userId, int infoCardId, bool isFavorite, CancellationToken cancellationToken = default)
    {
        try
        {
            var infoCard = await _unitOfWork.InfoCards.GetByIdAsync(infoCardId, cancellationToken);
            if (infoCard == null)
            {
                return ApiResponse<bool>.ErrorResult("资讯卡不存在", ErrorCodes.INFOCARD_001);
            }

            // 检查用户是否有权限查看该资讯卡
            if (!await CanUserViewInfoCardAsync(userId, infoCard, cancellationToken))
            {
                return ApiResponse<bool>.ErrorResult("资讯卡对当前用户不可见", ErrorCodes.INFOCARD_005);
            }

            var existingFavorite = await _unitOfWork.InfoCardFavorites.FirstOrDefaultAsync(
                f => f.UserId == userId && f.InfoCardId == infoCardId,
                cancellationToken);

            if (isFavorite)
            {
                // 添加收藏
                if (existingFavorite != null)
                {
                    return ApiResponse<bool>.ErrorResult("已收藏该资讯卡", ErrorCodes.INFOCARD_006);
                }

                var favorite = new UserInfoCardFavorite
                {
                    UserId = userId,
                    InfoCardId = infoCardId
                };

                await _unitOfWork.InfoCardFavorites.AddAsync(favorite, cancellationToken);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("资讯卡收藏成功: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
                return ApiResponse<bool>.SuccessResult(true, "收藏成功");
            }
            else
            {
                // 取消收藏
                if (existingFavorite == null)
                {
                    return ApiResponse<bool>.ErrorResult("未收藏该资讯卡", ErrorCodes.INFOCARD_007);
                }

                _unitOfWork.InfoCardFavorites.Remove(existingFavorite);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("资讯卡取消收藏成功: InfoCardId={InfoCardId}, UserId={UserId}", infoCardId, userId);
                return ApiResponse<bool>.SuccessResult(true, "取消收藏成功");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收藏操作失败: InfoCardId={InfoCardId}, UserId={UserId}, IsFavorite={IsFavorite}", infoCardId, userId, isFavorite);
            return ApiResponse<bool>.ErrorResult("收藏操作失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取用户收藏的资讯卡列表
    /// </summary>
    public async Task<ApiResponse<PagedResult<InfoCardDto>>> GetFavoriteInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = from favorite in _unitOfWork.InfoCardFavorites.GetQueryable()
                       join infoCard in _unitOfWork.InfoCards.GetQueryable() on favorite.InfoCardId equals infoCard.Id
                       where favorite.UserId == userId &&
                             infoCard.InfoCardStatus == InfoCardStatus.Active &&
                             (!infoCard.ExpiresAt.HasValue || infoCard.ExpiresAt.Value > DateTime.UtcNow)
                       orderby favorite.CreatedAt descending
                       select infoCard;

            var totalCount = await _unitOfWork.InfoCards.CountAsync(query, cancellationToken);
            var infoCards = await _unitOfWork.InfoCards.GetPagedAsync(query, page, pageSize, cancellationToken);

            var infoCardDtos = new List<InfoCardDto>();

            foreach (var infoCard in infoCards)
            {
                var publisher = await _unitOfWork.Users.GetByIdAsync(infoCard.OriginalPublisherId, cancellationToken);
                if (publisher != null)
                {
                    infoCardDtos.Add(new InfoCardDto
                    {
                        Id = infoCard.Id,
                        Publisher = new UserProfileDto
                        {
                            Id = publisher.Id,
                            CustomUserId = publisher.CustomUserId,
                            Username = publisher.Username,
                            Email = publisher.Email,
                            Phone = publisher.Phone,
                            AvatarUrl = publisher.AvatarUrl,
                            Bio = publisher.Bio,
                            UserStatus = publisher.UserStatus.ToString(),
                            CreatedAt = publisher.CreatedAt,
                            UpdatedAt = publisher.UpdatedAt
                        },
                        Title = infoCard.Title,
                        Content = infoCard.Content,
                        InfoCardType = infoCard.InfoCardType.ToString(),
                        Visibility = "Public",
                        AllowForwarding = infoCard.AllowForward,
                        ExpiresAt = infoCard.ExpiresAt,
                        CreatedAt = infoCard.CreatedAt,
                        UpdatedAt = infoCard.UpdatedAt
                    });
                }
            }

            var pagedResult = new PagedResult<InfoCardDto>
            {
                Items = infoCardDtos,
                Pagination = new PaginationInfo
                {
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    TotalItems = totalCount,
                    ItemsPerPage = pageSize
                }
            };

            return ApiResponse<PagedResult<InfoCardDto>>.SuccessResult(pagedResult, "获取收藏列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取收藏列表失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("获取收藏列表失败", ErrorCodes.SERVER_001);
        }
    }
}
