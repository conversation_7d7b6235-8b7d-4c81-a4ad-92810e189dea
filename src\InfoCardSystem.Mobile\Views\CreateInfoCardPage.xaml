<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.CreateInfoCardPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:CreateInfoCardViewModel"
             Title="创建资讯卡"
             BackgroundColor="{StaticResource Background}">

    <Grid RowDefinitions="*,Auto">
        
        <!-- 主内容区域 -->
        <ScrollView Grid.Row="0" Padding="16">
            
            <StackLayout Spacing="20">
                
                <!-- 标题输入 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="标题"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,8" />
                        
                        <Entry Text="{Binding Title}"
                               Placeholder="输入资讯卡标题（可选）"
                               FontSize="18"
                               FontAttributes="Bold"
                               BackgroundColor="Transparent"
                               TextColor="{StaticResource TextPrimary}"
                               PlaceholderColor="{StaticResource TextHint}"
                               MaxLength="100" />
                        
                        <Label Text="{Binding Title.Length, StringFormat='{0}/100'}"
                               FontSize="12"
                               TextColor="{StaticResource TextHint}"
                               HorizontalOptions="End"
                               Margin="0,4,0,0" />
                    </StackLayout>
                </Frame>

                <!-- 内容输入 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="内容"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,8" />
                        
                        <Editor Text="{Binding Content}"
                               Placeholder="分享您的想法..."
                               FontSize="16"
                               BackgroundColor="Transparent"
                               TextColor="{StaticResource TextPrimary}"
                               PlaceholderColor="{StaticResource TextHint}"
                               HeightRequest="120"
                               MaxLength="1000"
                               AutoSize="TextChanges" />
                        
                        <Label Text="{Binding Content.Length, StringFormat='{0}/1000'}"
                               FontSize="12"
                               TextColor="{StaticResource TextHint}"
                               HorizontalOptions="End"
                               Margin="0,4,0,0" />
                    </StackLayout>
                </Frame>

                <!-- 附件区域 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="附件"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <!-- 附件操作按钮 -->
                        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="8" Margin="0,0,0,16">
                            
                            <Button Grid.Column="0"
                                   Text="📷 拍照"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding TakePhotoCommand}" />
                            
                            <Button Grid.Column="1"
                                   Text="🖼️ 相册"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding PickPhotoCommand}" />
                            
                            <Button Grid.Column="2"
                                   Text="📎 文件"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding PickFileCommand}" />
                            
                        </Grid>
                        
                        <!-- 附件预览 -->
                        <CollectionView ItemsSource="{Binding Attachments}"
                                       IsVisible="{Binding Attachments.Count, Converter={StaticResource CountToBoolConverter}}"
                                       HeightRequest="100">
                            <CollectionView.ItemsLayout>
                                <LinearItemsLayout Orientation="Horizontal" ItemSpacing="8" />
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Frame WidthRequest="80"
                                               HeightRequest="80"
                                               CornerRadius="8"
                                               Padding="0"
                                               BackgroundColor="{StaticResource Gray100}">
                                            <Image Source="{Binding PreviewUrl}"
                                                   Aspect="AspectFill" />
                                        </Frame>
                                        
                                        <!-- 删除按钮 -->
                                        <Button Text="✗"
                                               FontSize="12"
                                               TextColor="White"
                                               BackgroundColor="{StaticResource Error}"
                                               WidthRequest="24"
                                               HeightRequest="24"
                                               CornerRadius="12"
                                               HorizontalOptions="End"
                                               VerticalOptions="Start"
                                               Margin="0,-8,-8,0"
                                               Command="{Binding Source={x:Reference Name=CreateInfoCardPage}, Path=BindingContext.RemoveAttachmentCommand}"
                                               CommandParameter="{Binding}" />
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </Frame>

                <!-- 分享设置 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="分享设置"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <!-- 分享范围 -->
                        <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto" RowSpacing="8">
                            
                            <RadioButton Grid.Row="0" Grid.Column="0"
                                        IsChecked="{Binding ShareToAll}"
                                        GroupName="ShareScope" />
                            <Label Grid.Row="0" Grid.Column="1"
                                   Text="分享给所有好友"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}"
                                   VerticalOptions="Center"
                                   Margin="8,0,0,0" />
                            
                            <RadioButton Grid.Row="1" Grid.Column="0"
                                        IsChecked="{Binding ShareToSelected}"
                                        GroupName="ShareScope" />
                            <Label Grid.Row="1" Grid.Column="1"
                                   Text="分享给指定好友"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}"
                                   VerticalOptions="Center"
                                   Margin="8,0,0,0" />
                            
                            <RadioButton Grid.Row="2" Grid.Column="0"
                                        IsChecked="{Binding ShareToGroups}"
                                        GroupName="ShareScope" />
                            <Label Grid.Row="2" Grid.Column="1"
                                   Text="分享给群组"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}"
                                   VerticalOptions="Center"
                                   Margin="8,0,0,0" />
                            
                        </Grid>
                        
                        <!-- 选择好友按钮 -->
                        <Button Text="选择好友"
                               Style="{StaticResource SecondaryButton}"
                               Margin="0,16,0,0"
                               IsVisible="{Binding ShareToSelected}"
                               Command="{Binding SelectFriendsCommand}" />
                        
                        <!-- 选择群组按钮 -->
                        <Button Text="选择群组"
                               Style="{StaticResource SecondaryButton}"
                               Margin="0,16,0,0"
                               IsVisible="{Binding ShareToGroups}"
                               Command="{Binding SelectGroupsCommand}" />
                        
                        <!-- 已选择的好友/群组 -->
                        <Label Text="{Binding SelectedRecipientsText}"
                               FontSize="14"
                               TextColor="{StaticResource TextSecondary}"
                               Margin="0,8,0,0"
                               IsVisible="{Binding SelectedRecipientsText, Converter={StaticResource StringToBoolConverter}}" />
                    </StackLayout>
                </Frame>

                <!-- 预览区域 -->
                <Frame Style="{StaticResource CardFrame}"
                       IsVisible="{Binding ShowPreview}">
                    <StackLayout>
                        <Label Text="预览"
                               FontSize="16"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,12" />
                        
                        <!-- 预览内容 -->
                        <Frame BackgroundColor="{StaticResource Surface}"
                               BorderColor="{StaticResource Outline}"
                               CornerRadius="8"
                               Padding="12">
                            <StackLayout>
                                <Label Text="{Binding Title}"
                                       FontSize="18"
                                       FontAttributes="Bold"
                                       TextColor="{StaticResource TextPrimary}"
                                       IsVisible="{Binding Title, Converter={StaticResource StringToBoolConverter}}" />
                                
                                <Label Text="{Binding Content}"
                                       FontSize="16"
                                       TextColor="{StaticResource TextPrimary}"
                                       LineBreakMode="WordWrap"
                                       Margin="0,8,0,0" />
                            </StackLayout>
                        </Frame>
                    </StackLayout>
                </Frame>

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  HorizontalOptions="Center"
                                  Margin="0,20,0,0" />

                <!-- 错误消息 -->
                <Frame IsVisible="{Binding HasError}"
                       BackgroundColor="{StaticResource Error}"
                       Margin="0,8,0,0">
                    <Label Text="{Binding ErrorMessage}"
                           TextColor="White"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </Frame>

            </StackLayout>
            
        </ScrollView>

        <!-- 底部操作栏 -->
        <Frame Grid.Row="1"
               BackgroundColor="{StaticResource Surface}"
               CornerRadius="0"
               Padding="16,12"
               HasShadow="True">
            
            <Grid ColumnDefinitions="*,Auto,*" ColumnSpacing="12">
                
                <!-- 保存草稿 -->
                <Button Grid.Column="0"
                       Text="保存草稿"
                       Style="{StaticResource SecondaryButton}"
                       Command="{Binding SaveDraftCommand}"
                       IsEnabled="{Binding IsNotBusy}" />
                
                <!-- 预览切换 -->
                <Button Grid.Column="1"
                       Text="👁️"
                       FontSize="20"
                       BackgroundColor="Transparent"
                       TextColor="{StaticResource Primary}"
                       WidthRequest="44"
                       HeightRequest="44"
                       CornerRadius="22"
                       Command="{Binding TogglePreviewCommand}" />
                
                <!-- 发布 -->
                <Button Grid.Column="2"
                       Text="发布"
                       FontAttributes="Bold"
                       Command="{Binding PublishCommand}"
                       IsEnabled="{Binding CanPublish}" />
                
            </Grid>
            
        </Frame>
        
    </Grid>

</ContentPage>
