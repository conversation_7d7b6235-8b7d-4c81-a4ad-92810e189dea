using InfoCardSystem.Core.Entities;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 用户仓储
    /// </summary>
    IRepository<AppUser> Users { get; }
    
    /// <summary>
    /// 好友关系仓储
    /// </summary>
    IRepository<UserFriendship> Friendships { get; }
    
    /// <summary>
    /// 黑名单仓储
    /// </summary>
    IRepository<UserBlacklist> Blacklists { get; }
    
    /// <summary>
    /// 群组仓储
    /// </summary>
    IRepository<UserGroup> Groups { get; }
    
    /// <summary>
    /// 群组成员仓储
    /// </summary>
    IRepository<UserGroupMember> GroupMembers { get; }
    
    /// <summary>
    /// 资讯卡仓储
    /// </summary>
    IRepository<UserInfoCard> InfoCards { get; }
    
    /// <summary>
    /// 资讯卡接收者仓储
    /// </summary>
    IRepository<UserInfoCardRecipient> InfoCardRecipients { get; }
    
    /// <summary>
    /// 资讯卡收藏仓储
    /// </summary>
    IRepository<UserInfoCardFavorite> InfoCardFavorites { get; }
    
    /// <summary>
    /// 附件仓储
    /// </summary>
    IRepository<UserAttachment> Attachments { get; }
    
    /// <summary>
    /// 资讯卡附件关联仓储
    /// </summary>
    IRepository<UserInfoCardAttachment> InfoCardAttachments { get; }

    /// <summary>
    /// 密码重置令牌仓储
    /// </summary>
    IRepository<PasswordResetToken> PasswordResetTokens { get; }

    /// <summary>
    /// 用户二维码仓储
    /// </summary>
    IRepository<UserQRCode> UserQRCodes { get; }
    
    /// <summary>
    /// 保存更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 开始事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务</returns>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
