namespace InfoCardSystem.Shared.Constants;

/// <summary>
/// 错误码常量
/// </summary>
public static class ErrorCodes
{
    // 认证相关错误
    public const string AUTH_001 = "AUTH_001"; // 未授权访问
    public const string AUTH_002 = "AUTH_002"; // 权限不足
    public const string AUTH_003 = "AUTH_003"; // 无权限访问资源
    
    // 验证相关错误
    public const string VALID_001 = "VALID_001"; // 参数验证失败
    
    // 用户相关错误
    public const string USER_001 = "USER_001"; // 用户不存在
    public const string USER_002 = "USER_002"; // 用户名、邮箱或手机号已存在
    public const string USER_003 = "USER_003"; // 自定义用户ID已存在

    // 好友关系相关错误
    public const string FRIEND_001 = "FRIEND_001"; // 不能添加自己为好友
    public const string FRIEND_002 = "FRIEND_002"; // 已经是好友关系
    public const string FRIEND_003 = "FRIEND_003"; // 好友请求已发送
    public const string FRIEND_004 = "FRIEND_004"; // 好友关系状态异常
    public const string FRIEND_005 = "FRIEND_005"; // 无法发送好友请求（被拉黑）
    public const string FRIEND_006 = "FRIEND_006"; // 好友请求不存在
    public const string FRIEND_007 = "FRIEND_007"; // 无权处理此好友请求
    public const string FRIEND_008 = "FRIEND_008"; // 好友请求已被处理

    // 黑名单相关错误
    public const string BLACKLIST_001 = "BLACKLIST_001"; // 不能拉黑自己
    public const string BLACKLIST_002 = "BLACKLIST_002"; // 用户已在黑名单中
    public const string BLACKLIST_003 = "BLACKLIST_003"; // 用户不在黑名单中

    // 资讯卡相关错误
    public const string INFOCARD_001 = "INFOCARD_001"; // 资讯卡不存在
    public const string INFOCARD_002 = "INFOCARD_002"; // 资讯卡已删除
    public const string INFOCARD_003 = "INFOCARD_003"; // 资讯卡已过期
    public const string INFOCARD_004 = "INFOCARD_004"; // 不允许转发
    public const string INFOCARD_005 = "INFOCARD_005"; // 资讯卡对当前用户不可见
    public const string INFOCARD_006 = "INFOCARD_006"; // 已收藏该资讯卡
    public const string INFOCARD_007 = "INFOCARD_007"; // 未收藏该资讯卡

    public const string USER_004 = "USER_004"; // 手机号格式不正确

    // 群组相关错误
    public const string GROUP_001 = "GROUP_001"; // 群组不存在或无权访问
    public const string GROUP_002 = "GROUP_002"; // 只能添加直属好友到群组
    public const string GROUP_003 = "GROUP_003"; // 只有群主可以操作
    public const string GROUP_004 = "GROUP_004"; // 用户已经是群组成员
    public const string GROUP_005 = "GROUP_005"; // 群主不能移除自己
    public const string GROUP_006 = "GROUP_006"; // 用户不是群组成员
    public const string GROUP_007 = "GROUP_007"; // 群主不能退出群组

    // 验证相关错误
    public const string VALIDATION_001 = "VALIDATION_001"; // 请求参数验证失败
    public const string VALIDATION_002 = "VALIDATION_002"; // 数据格式错误
    public const string VALIDATION_003 = "VALIDATION_003"; // 必填字段缺失

    // 业务逻辑错误
    public const string BUSINESS_001 = "BUSINESS_001"; // 业务规则违反
    public const string BUSINESS_002 = "BUSINESS_002"; // 操作不被允许
    public const string BUSINESS_003 = "BUSINESS_003"; // 状态不正确

    // 资源相关错误
    public const string NOT_FOUND_001 = "NOT_FOUND_001"; // 资源不存在
    public const string NOT_FOUND_002 = "NOT_FOUND_002"; // 页面不存在

    // 超时相关错误
    public const string TIMEOUT_001 = "TIMEOUT_001"; // 请求超时
    public const string TIMEOUT_002 = "TIMEOUT_002"; // 数据库操作超时

    // 限流相关错误
    public const string RATE_LIMIT_001 = "RATE_LIMIT_001"; // 请求频率过高
    public const string RATE_LIMIT_002 = "RATE_LIMIT_002"; // 超出每日限额
    
    // 文件相关错误
    public const string FILE_001 = "FILE_001"; // 文件大小超限
    public const string FILE_002 = "FILE_002"; // 不支持的文件格式
    
    // 限流相关错误
    public const string RATE_001 = "RATE_001"; // 请求频率超限
    
    // 服务器错误
    public const string SERVER_001 = "SERVER_001"; // 服务器内部错误
}
