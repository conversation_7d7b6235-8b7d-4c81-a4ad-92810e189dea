@echo off
echo Fixing web.config files...

echo Creating corrected API web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath=".\InfoCardSystem.API.exe" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="IIS" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo Creating corrected Web web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath=".\InfoCardSystem.Web.exe" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="IIS" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo Web.config files fixed!
echo Now testing XML validity...

echo Testing API web.config...
powershell -Command "try { [xml]$xml = Get-Content 'C:\Webs\ICAPI\web.config'; Write-Host 'API web.config is valid XML' } catch { Write-Host 'API web.config has XML errors:' $_.Exception.Message }"

echo Testing Web web.config...
powershell -Command "try { [xml]$xml = Get-Content 'C:\Webs\ICWeb\web.config'; Write-Host 'Web web.config is valid XML' } catch { Write-Host 'Web web.config has XML errors:' $_.Exception.Message }"
