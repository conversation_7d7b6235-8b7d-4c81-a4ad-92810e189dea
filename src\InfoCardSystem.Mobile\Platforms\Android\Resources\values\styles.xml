<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Base application theme -->
    <style name="Maui.SplashTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/maui_splash_image</item>
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:navigationBarColor">@color/primary</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- Main application theme -->
    <style name="MainTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorOnError">@color/on_error</item>
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:navigationBarColor">@color/surface</item>
    </style>

</resources>
