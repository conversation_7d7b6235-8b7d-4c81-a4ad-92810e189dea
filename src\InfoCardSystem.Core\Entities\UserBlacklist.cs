namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户黑名单实体
/// </summary>
public class UserBlacklist : BaseEntity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 被拉黑用户ID
    /// </summary>
    public int BlockedUserId { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 用户
    /// </summary>
    public virtual AppUser User { get; set; } = null!;
    
    /// <summary>
    /// 被拉黑的用户
    /// </summary>
    public virtual AppUser BlockedUser { get; set; } = null!;
}
