using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.InfoCard;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 资讯卡服务接口
/// </summary>
public interface IInfoCardService
{
    /// <summary>
    /// 创建资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">创建请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的资讯卡</returns>
    Task<ApiResponse<InfoCardDto>> CreateInfoCardAsync(int userId, CreateInfoCardRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取资讯卡详情
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡详情</returns>
    Task<ApiResponse<InfoCardDto>> GetInfoCardAsync(int userId, int infoCardId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的资讯卡</returns>
    Task<ApiResponse<InfoCardDto>> UpdateInfoCardAsync(int userId, int infoCardId, UpdateInfoCardRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeleteInfoCardAsync(int userId, int infoCardId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的资讯卡列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡列表</returns>
    Task<ApiResponse<PagedResult<InfoCardDto>>> GetUserInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户可见的资讯卡列表（时间线）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡列表</returns>
    Task<ApiResponse<PagedResult<InfoCardDto>>> GetTimelineInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 转发资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">原资讯卡ID</param>
    /// <param name="recipientUserIds">接收者用户ID列表</param>
    /// <param name="recipientGroupIds">接收者群组ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ForwardInfoCardAsync(int userId, int infoCardId, List<int>? recipientUserIds = null, List<int>? recipientGroupIds = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 收藏/取消收藏资讯卡
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="isFavorite">是否收藏</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ToggleFavoriteAsync(int userId, int infoCardId, bool isFavorite, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户收藏的资讯卡列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收藏的资讯卡列表</returns>
    Task<ApiResponse<PagedResult<InfoCardDto>>> GetFavoriteInfoCardsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default);
}
