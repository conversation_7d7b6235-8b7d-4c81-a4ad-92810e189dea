using System.Security.Claims;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Friendship;
using InfoCardSystem.Core.DTOs.User;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 好友管理控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[Authorize]
public class FriendsController : ControllerBase
{
    private readonly IFriendshipService _friendshipService;
    private readonly ILogger<FriendsController> _logger;

    public FriendsController(IFriendshipService friendshipService, ILogger<FriendsController> logger)
    {
        _friendshipService = friendshipService;
        _logger = logger;
    }

    /// <summary>
    /// 发送好友请求
    /// </summary>
    /// <param name="targetCustomUserId">目标用户自定义ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("request")]
    public async Task<ActionResult<ApiResponse<bool>>> SendFriendRequest(
        [FromBody] string targetCustomUserId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (string.IsNullOrWhiteSpace(targetCustomUserId))
        {
            return BadRequest(ApiResponse<bool>.ErrorResult("目标用户ID不能为空", "INVALID_TARGET_USER_ID"));
        }

        var result = await _friendshipService.SendFriendRequestAsync(userId.Value, targetCustomUserId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 处理好友请求
    /// </summary>
    /// <param name="request">处理请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("request/handle")]
    public async Task<ActionResult<ApiResponse<bool>>> HandleFriendRequest(
        [FromBody] HandleFriendRequestDto request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.HandleFriendRequestAsync(userId.Value, request.FriendshipId, request.Accept, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取好友列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>好友列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<FriendshipDto>>>> GetFriends(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<FriendshipDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.GetFriendsAsync(userId.Value, page, pageSize, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取待处理的好友请求
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>好友请求列表</returns>
    [HttpGet("requests")]
    public async Task<ActionResult<ApiResponse<List<FriendRequestDto>>>> GetPendingFriendRequests(
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<List<FriendRequestDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.GetPendingFriendRequestsAsync(userId.Value, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 更新好友别名
    /// </summary>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPut("alias")]
    public async Task<ActionResult<ApiResponse<bool>>> UpdateFriendAlias(
        [FromBody] UpdateFriendAliasDto request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.UpdateFriendAliasAsync(userId.Value, request.FriendshipId, request.Alias, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 删除好友
    /// </summary>
    /// <param name="friendshipId">好友关系ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{friendshipId}")]
    public async Task<ActionResult<ApiResponse<bool>>> RemoveFriend(
        int friendshipId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.RemoveFriendAsync(userId.Value, friendshipId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 拉黑用户
    /// </summary>
    /// <param name="blockedUserId">被拉黑用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("block/{blockedUserId}")]
    public async Task<ActionResult<ApiResponse<bool>>> BlockUser(
        int blockedUserId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.BlockUserAsync(userId.Value, blockedUserId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 解除拉黑
    /// </summary>
    /// <param name="blockedUserId">被拉黑用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("block/{blockedUserId}")]
    public async Task<ActionResult<ApiResponse<bool>>> UnblockUser(
        int blockedUserId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.UnblockUserAsync(userId.Value, blockedUserId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取黑名单
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>黑名单用户列表</returns>
    [HttpGet("blocked")]
    public async Task<ActionResult<ApiResponse<List<UserProfileDto>>>> GetBlockedUsers(
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<List<UserProfileDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _friendshipService.GetBlockedUsersAsync(userId.Value, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 扫码添加好友
    /// </summary>
    /// <param name="request">扫码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("scan-qrcode")]
    public async Task<ActionResult<ApiResponse<bool>>> ScanQRCodeAddFriend(
        [FromBody] ScanQRCodeRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<bool>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _friendshipService.ScanQRCodeAddFriendAsync(userId.Value, request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
