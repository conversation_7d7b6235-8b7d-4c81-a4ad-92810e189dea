#!/bin/bash

# InfoCard系统快速启动脚本
# 一键安装和启动InfoCard系统，适合快速体验和开发环境
# 
# 使用方法:
#   ./scripts/quick-start.sh

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    InfoCard 信息卡片分享系统"
    echo "    快速启动脚本"
    echo "=================================================="
    echo -e "${NC}"
    echo ""
    echo "这个脚本将帮助您快速安装和启动InfoCard系统"
    echo "包括数据库配置、依赖安装、项目构建和服务启动"
    echo ""
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_info "检测到Linux系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "检测到macOS系统"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        log_info "检测到Windows系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    log_success "系统环境检查完成"
}

# 检查和安装依赖
install_dependencies() {
    log_info "检查和安装依赖..."
    
    # 检查 .NET SDK
    if ! command -v dotnet &> /dev/null; then
        log_warning ".NET SDK 未安装，正在安装..."
        
        case $OS in
            "linux")
                # Ubuntu/Debian
                if command -v apt-get &> /dev/null; then
                    wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
                    sudo dpkg -i packages-microsoft-prod.deb
                    sudo apt-get update
                    sudo apt-get install -y dotnet-sdk-8.0
                    rm packages-microsoft-prod.deb
                # CentOS/RHEL
                elif command -v yum &> /dev/null; then
                    sudo rpm -Uvh https://packages.microsoft.com/config/centos/7/packages-microsoft-prod.rpm
                    sudo yum install -y dotnet-sdk-8.0
                fi
                ;;
            "macos")
                if command -v brew &> /dev/null; then
                    brew install --cask dotnet
                else
                    log_error "请先安装Homebrew或手动安装.NET SDK"
                    exit 1
                fi
                ;;
            "windows")
                log_error "请手动下载并安装.NET 8.0 SDK: https://dotnet.microsoft.com/download"
                exit 1
                ;;
        esac
    else
        log_success ".NET SDK 已安装: $(dotnet --version)"
    fi
    
    # 检查 MySQL
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL 未安装，正在安装..."
        
        case $OS in
            "linux")
                if command -v apt-get &> /dev/null; then
                    sudo apt-get update
                    sudo apt-get install -y mysql-server
                    sudo systemctl start mysql
                    sudo systemctl enable mysql
                elif command -v yum &> /dev/null; then
                    sudo yum install -y mysql-server
                    sudo systemctl start mysqld
                    sudo systemctl enable mysqld
                fi
                ;;
            "macos")
                if command -v brew &> /dev/null; then
                    brew install mysql
                    brew services start mysql
                else
                    log_error "请先安装Homebrew或手动安装MySQL"
                    exit 1
                fi
                ;;
            "windows")
                log_error "请手动下载并安装MySQL: https://dev.mysql.com/downloads/"
                exit 1
                ;;
        esac
    else
        log_success "MySQL 已安装"
    fi
    
    log_success "依赖安装完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 生成随机密码
    DB_PASSWORD=$(openssl rand -base64 12 2>/dev/null || echo "InfoCard123!")
    
    # 创建数据库和用户
    mysql -u root -e "
        CREATE DATABASE IF NOT EXISTS InfoCardDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE USER IF NOT EXISTS 'infocard'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
        GRANT ALL PRIVILEGES ON InfoCardDB.* TO 'infocard'@'localhost';
        FLUSH PRIVILEGES;
    " 2>/dev/null || {
        log_warning "无法自动配置数据库，请手动执行以下SQL:"
        echo "CREATE DATABASE InfoCardDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        echo "CREATE USER 'infocard'@'localhost' IDENTIFIED BY 'your_password';"
        echo "GRANT ALL PRIVILEGES ON InfoCardDB.* TO 'infocard'@'localhost';"
        echo "FLUSH PRIVILEGES;"
        echo ""
        read -p "请输入数据库密码: " DB_PASSWORD
    }
    
    # 保存数据库配置
    cat > .env.local << EOF
DB_PASSWORD=$DB_PASSWORD
JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || echo "your-secret-key-here-minimum-32-characters")
EOF
    
    log_success "数据库配置完成"
}

# 更新配置文件
update_config() {
    log_info "更新配置文件..."
    
    # 读取环境变量
    source .env.local
    
    # 更新API配置
    API_CONFIG="src/InfoCardSystem.API/appsettings.Development.json"
    if [ -f "$API_CONFIG" ]; then
        # 备份原配置
        cp "$API_CONFIG" "$API_CONFIG.backup"
        
        # 更新连接字符串
        cat > "$API_CONFIG" << EOF
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=InfoCardDB;Uid=infocard;Pwd=$DB_PASSWORD;CharSet=utf8mb4;"
  },
  "JwtSettings": {
    "SecretKey": "$JWT_SECRET",
    "Issuer": "InfoCardSystem",
    "Audience": "InfoCardUsers",
    "ExpirationMinutes": 60
  },
  "AllowedHosts": "*",
  "FileUpload": {
    "MaxFileSize": 10485760,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"],
    "UploadPath": "wwwroot/uploads"
  }
}
EOF
    fi
    
    log_success "配置文件更新完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 还原NuGet包
    dotnet restore src/InfoCardSystem.sln
    
    # 构建解决方案
    dotnet build src/InfoCardSystem.sln -c Debug
    
    log_success "项目构建完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    cd src/InfoCardSystem.API
    
    # 检查是否有迁移文件
    if [ ! -d "Migrations" ]; then
        log_info "创建初始迁移..."
        dotnet ef migrations add InitialCreate
    fi
    
    # 更新数据库
    dotnet ef database update
    
    cd ../..
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 创建上传目录
    mkdir -p src/InfoCardSystem.API/wwwroot/uploads
    chmod 755 src/InfoCardSystem.API/wwwroot/uploads
    
    # 启动API服务（后台运行）
    log_info "启动API服务..."
    cd src/InfoCardSystem.API
    nohup dotnet run > ../../api.log 2>&1 &
    API_PID=$!
    echo $API_PID > ../../api.pid
    cd ../..
    
    # 等待API启动
    sleep 5
    
    # 启动Web应用（后台运行）
    log_info "启动Web应用..."
    cd src/InfoCardSystem.Web
    nohup dotnet run > ../../web.log 2>&1 &
    WEB_PID=$!
    echo $WEB_PID > ../../web.pid
    cd ../..
    
    # 等待Web应用启动
    sleep 5
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查API服务
    if curl -f http://localhost:5001/health > /dev/null 2>&1; then
        log_success "API服务运行正常"
    else
        log_warning "API服务可能未完全启动，请稍后检查"
    fi
    
    # 检查Web应用
    if curl -f http://localhost:5000 > /dev/null 2>&1; then
        log_success "Web应用运行正常"
    else
        log_warning "Web应用可能未完全启动，请稍后检查"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo -e "${GREEN}=================================================="
    echo "    InfoCard 系统启动完成！"
    echo "==================================================${NC}"
    echo ""
    echo "🌐 Web应用地址: http://localhost:5000"
    echo "🔧 API文档地址: http://localhost:5001/swagger"
    echo "💾 数据库名称: InfoCardDB"
    echo "📁 上传目录: src/InfoCardSystem.API/wwwroot/uploads"
    echo ""
    echo "📋 管理命令:"
    echo "  查看API日志: tail -f api.log"
    echo "  查看Web日志: tail -f web.log"
    echo "  停止服务: ./scripts/stop.sh"
    echo "  重启服务: ./scripts/restart.sh"
    echo ""
    echo "📖 更多信息请查看: docs/README.md"
    echo ""
}

# 创建停止脚本
create_stop_script() {
    cat > scripts/stop.sh << 'EOF'
#!/bin/bash
echo "停止InfoCard服务..."

if [ -f api.pid ]; then
    API_PID=$(cat api.pid)
    if kill -0 $API_PID 2>/dev/null; then
        kill $API_PID
        echo "API服务已停止"
    fi
    rm -f api.pid
fi

if [ -f web.pid ]; then
    WEB_PID=$(cat web.pid)
    if kill -0 $WEB_PID 2>/dev/null; then
        kill $WEB_PID
        echo "Web服务已停止"
    fi
    rm -f web.pid
fi

echo "所有服务已停止"
EOF
    chmod +x scripts/stop.sh
}

# 创建重启脚本
create_restart_script() {
    cat > scripts/restart.sh << 'EOF'
#!/bin/bash
echo "重启InfoCard服务..."

# 停止服务
./scripts/stop.sh

# 等待服务完全停止
sleep 3

# 重新启动
echo "重新启动服务..."
cd src/InfoCardSystem.API
nohup dotnet run > ../../api.log 2>&1 &
echo $! > ../../api.pid
cd ../..

sleep 5

cd src/InfoCardSystem.Web
nohup dotnet run > ../../web.log 2>&1 &
echo $! > ../../web.pid
cd ../..

echo "服务重启完成"
echo "Web应用: http://localhost:5000"
echo "API文档: http://localhost:5001/swagger"
EOF
    chmod +x scripts/restart.sh
}

# 主函数
main() {
    show_welcome
    
    # 检查是否在项目根目录
    if [ ! -f "src/InfoCardSystem.sln" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装流程
    check_system
    install_dependencies
    setup_database
    update_config
    build_project
    run_migrations
    start_services
    
    # 创建管理脚本
    create_stop_script
    create_restart_script
    
    # 健康检查
    sleep 3
    health_check
    
    # 显示完成信息
    show_completion
    
    log_success "InfoCard系统快速启动完成！"
}

# 执行主函数
main "$@"
