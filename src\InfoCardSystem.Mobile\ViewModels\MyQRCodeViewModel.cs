using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 我的二维码页面ViewModel
/// </summary>
public partial class MyQRCodeViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;
    private readonly IInfoCardApiClient _apiClient;

    public MyQRCodeViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        IInfoCardApiClient apiClient,
        ILogger<MyQRCodeViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        Title = "我的二维码";
        
        // 初始化用户信息和二维码
        _ = LoadUserInfoAndQRCodeAsync();
    }

    [ObservableProperty]
    private UserInfo userInfo = new();

    [ObservableProperty]
    private string qrCodeImageSource = "qr_code_placeholder.png";

    /// <summary>
    /// 二维码图片源（用于绑定）
    /// </summary>
    public string QRCodeImageSource => QrCodeImageSource;

    [ObservableProperty]
    private string qrCodeData = string.Empty;

    [ObservableProperty]
    private bool isNotBusy = true;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    /// <summary>
    /// 刷新二维码命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshQRCodeAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("刷新二维码");

            await LoadQRCodeAsync();
            
            await ShowSuccessAsync("二维码已刷新");
            
            _logger.LogInformation("二维码刷新成功");
        }, "刷新二维码");
    }

    /// <summary>
    /// 保存到相册命令
    /// </summary>
    [RelayCommand]
    private async Task SaveToGalleryAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("保存二维码到相册");

            // 这里应该实现保存二维码图片到相册的逻辑
            // 可以使用MediaGallery插件或类似的库
            
            // 模拟保存过程
            await Task.Delay(1000);
            
            await ShowSuccessAsync("二维码已保存到相册");
            
            _logger.LogInformation("二维码保存到相册成功");
        }, "保存到相册", false);
    }

    /// <summary>
    /// 分享给好友命令
    /// </summary>
    [RelayCommand]
    private async Task ShareToFriendsAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("分享二维码给好友");

            var shareText = $"我是 {UserInfo.DisplayName ?? UserInfo.Username}，扫描二维码添加我为好友吧！";
            
            await Share.Default.RequestAsync(new ShareTextRequest
            {
                Text = shareText,
                Title = "分享我的二维码"
            });
            
            _logger.LogInformation("二维码分享成功");
        }, "分享给好友", false);
    }

    /// <summary>
    /// 复制链接命令
    /// </summary>
    [RelayCommand]
    private async Task CopyLinkAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("复制二维码链接");

            // 使用本地Web应用地址
            var link = $"http://localhost:8082/add-friend?code={QrCodeData}";

            await Clipboard.Default.SetTextAsync(link);

            await ShowSuccessAsync("链接已复制到剪贴板");

            _logger.LogInformation("二维码链接复制成功");
        }, "复制链接", false);
    }

    /// <summary>
    /// 扫描二维码命令
    /// </summary>
    [RelayCommand]
    private async Task ScanQRCodeAsync()
    {
        await NavigateToAsync("qrscanner");
    }

    /// <summary>
    /// 查看好友请求命令
    /// </summary>
    [RelayCommand]
    private async Task ViewFriendRequestsAsync()
    {
        await NavigateToAsync("friendrequests");
    }

    /// <summary>
    /// 加载用户信息和二维码
    /// </summary>
    private async Task LoadUserInfoAndQRCodeAsync()
    {
        try
        {
            _logger.LogDebug("加载用户信息和二维码");

            // 加载用户信息
            var currentUser = await _authService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                UserInfo = currentUser;
            }

            // 加载二维码
            await LoadQRCodeAsync();

            _logger.LogInformation("用户信息和二维码加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载用户信息和二维码失败");
            await ShowErrorAsync("加载失败", "无法加载用户信息和二维码，请重试");
        }
    }

    /// <summary>
    /// 加载二维码
    /// </summary>
    private async Task LoadQRCodeAsync()
    {
        try
        {
            _logger.LogDebug("加载二维码数据");

            // 模拟从API获取二维码数据
            // var response = await _apiClient.GetUserQRCodeAsync();
            
            // 模拟二维码数据
            QrCodeData = $"user_{UserInfo.Id}_{DateTime.Now.Ticks}";
            
            // 这里应该生成实际的二维码图片
            // 可以使用ZXing.Net或类似的库生成二维码
            QrCodeImageSource = GenerateQRCodeImage(QrCodeData);

            _logger.LogInformation("二维码数据加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载二维码数据失败");
            throw;
        }
    }

    /// <summary>
    /// 生成二维码图片
    /// </summary>
    private string GenerateQRCodeImage(string data)
    {
        try
        {
            _logger.LogDebug("生成二维码图片: {Data}", data);

            // 这里应该使用二维码生成库生成实际的二维码图片
            // 例如使用ZXing.Net.Maui或类似的库
            
            // 暂时返回占位符图片
            return "qr_code_sample.png";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成二维码图片失败");
            return "qr_code_placeholder.png";
        }
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        
        // 每次进入页面时刷新二维码
        await LoadUserInfoAndQRCodeAsync();
    }
}
