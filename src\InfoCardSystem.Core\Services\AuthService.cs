using InfoCardSystem.Core.DTOs.Auth;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using InfoCardSystem.Shared.Enums;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 认证服务实现
/// </summary>
public class AuthService : IAuthService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordService _passwordService;
    private readonly IJwtService _jwtService;
    private readonly IEmailService _emailService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(
        IUnitOfWork unitOfWork,
        IPasswordService passwordService,
        IJwtService jwtService,
        IEmailService emailService,
        ILogger<AuthService> logger)
    {
        _unitOfWork = unitOfWork;
        _passwordService = passwordService;
        _jwtService = jwtService;
        _emailService = emailService;
        _logger = logger;
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    public async Task<ApiResponse<AuthResponse>> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // 验证用户名是否已存在
            if (!await IsUsernameAvailableAsync(request.Username, cancellationToken))
            {
                return ApiResponse<AuthResponse>.ErrorResult("用户名已存在", ErrorCodes.USER_002);
            }

            // 验证邮箱是否已存在
            if (!await IsEmailAvailableAsync(request.Email, cancellationToken))
            {
                return ApiResponse<AuthResponse>.ErrorResult("邮箱已存在", ErrorCodes.USER_002);
            }

            // 验证手机号是否已存在（如果提供）
            if (!string.IsNullOrEmpty(request.Phone) && !await IsPhoneAvailableAsync(request.Phone, cancellationToken))
            {
                return ApiResponse<AuthResponse>.ErrorResult("手机号已存在", ErrorCodes.USER_002);
            }

            // 生成自定义用户ID（如果未提供）
            var customUserId = request.CustomUserId;
            if (string.IsNullOrEmpty(customUserId))
            {
                customUserId = await GenerateUniqueCustomUserIdAsync(request.Username, cancellationToken);
            }
            else if (!await IsCustomUserIdAvailableAsync(customUserId, cancellationToken))
            {
                return ApiResponse<AuthResponse>.ErrorResult("自定义用户ID已存在", ErrorCodes.USER_003);
            }

            // 创建用户
            var user = new AppUser
            {
                CustomUserId = customUserId,
                Username = request.Username,
                Email = request.Email.ToLowerInvariant(),
                Phone = request.Phone,
                PasswordHash = _passwordService.HashPassword(request.Password),
                Bio = request.Bio,
                UserStatus = UserStatus.Active
            };

            await _unitOfWork.Users.AddAsync(user, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户注册成功: {Username} ({Email})", user.Username, user.Email);

            // 生成认证响应
            var authResponse = GenerateAuthResponse(user);
            return ApiResponse<AuthResponse>.SuccessResult(authResponse, "注册成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户注册失败: {Username}", request.Username);
            return ApiResponse<AuthResponse>.ErrorResult("注册失败，请稍后重试", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    public async Task<ApiResponse<AuthResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            AppUser? user = null;

            // 根据登录类型查找用户
            switch (request.LoginType)
            {
                case LoginType.Email:
                    if (string.IsNullOrEmpty(request.Email))
                        return ApiResponse<AuthResponse>.ErrorResult("邮箱不能为空", ErrorCodes.VALID_001);
                    user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == request.Email.ToLowerInvariant(), cancellationToken);
                    break;

                case LoginType.Phone:
                    if (string.IsNullOrEmpty(request.Phone))
                        return ApiResponse<AuthResponse>.ErrorResult("手机号不能为空", ErrorCodes.VALID_001);
                    user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Phone == request.Phone, cancellationToken);
                    break;

                case LoginType.UserId:
                    if (string.IsNullOrEmpty(request.CustomUserId))
                        return ApiResponse<AuthResponse>.ErrorResult("用户ID不能为空", ErrorCodes.VALID_001);
                    user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.CustomUserId == request.CustomUserId, cancellationToken);
                    break;

                default:
                    return ApiResponse<AuthResponse>.ErrorResult("不支持的登录类型", ErrorCodes.VALID_001);
            }

            if (user == null)
            {
                _logger.LogWarning("登录失败: 用户不存在 - {LoginType}: {Identifier}", 
                    request.LoginType, 
                    request.LoginType switch
                    {
                        LoginType.Email => request.Email,
                        LoginType.Phone => request.Phone,
                        LoginType.UserId => request.CustomUserId,
                        _ => "Unknown"
                    });
                return ApiResponse<AuthResponse>.ErrorResult("用户名或密码错误", ErrorCodes.AUTH_001);
            }

            // 验证密码
            if (!_passwordService.VerifyPassword(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("登录失败: 密码错误 - 用户: {Username}", user.Username);
                return ApiResponse<AuthResponse>.ErrorResult("用户名或密码错误", ErrorCodes.AUTH_001);
            }

            // 检查用户状态
            if (user.UserStatus != UserStatus.Active)
            {
                _logger.LogWarning("登录失败: 用户状态异常 - 用户: {Username}, 状态: {Status}", user.Username, user.UserStatus);
                return ApiResponse<AuthResponse>.ErrorResult("账户已被禁用", ErrorCodes.AUTH_002);
            }

            _logger.LogInformation("用户登录成功: {Username} ({Email})", user.Username, user.Email);

            // 生成认证响应
            var authResponse = GenerateAuthResponse(user);
            return ApiResponse<AuthResponse>.SuccessResult(authResponse, "登录成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录失败");
            return ApiResponse<AuthResponse>.ErrorResult("登录失败，请稍后重试", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public async Task<ApiResponse<AuthResponse>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        // TODO: 实现刷新令牌逻辑（需要存储刷新令牌）
        await Task.CompletedTask;
        return ApiResponse<AuthResponse>.ErrorResult("刷新令牌功能暂未实现", ErrorCodes.SERVER_001);
    }

    /// <summary>
    /// 验证用户名是否可用
    /// </summary>
    public async Task<bool> IsUsernameAvailableAsync(string username, CancellationToken cancellationToken = default)
    {
        return !await _unitOfWork.Users.ExistsAsync(u => u.Username == username, cancellationToken);
    }

    /// <summary>
    /// 验证邮箱是否可用
    /// </summary>
    public async Task<bool> IsEmailAvailableAsync(string email, CancellationToken cancellationToken = default)
    {
        return !await _unitOfWork.Users.ExistsAsync(u => u.Email == email.ToLowerInvariant(), cancellationToken);
    }

    /// <summary>
    /// 验证自定义用户ID是否可用
    /// </summary>
    public async Task<bool> IsCustomUserIdAvailableAsync(string customUserId, CancellationToken cancellationToken = default)
    {
        return !await _unitOfWork.Users.ExistsAsync(u => u.CustomUserId == customUserId, cancellationToken);
    }

    /// <summary>
    /// 验证手机号是否可用
    /// </summary>
    public async Task<bool> IsPhoneAvailableAsync(string phone, CancellationToken cancellationToken = default)
    {
        return !await _unitOfWork.Users.ExistsAsync(u => u.Phone == phone, cancellationToken);
    }

    /// <summary>
    /// 生成认证响应
    /// </summary>
    private AuthResponse GenerateAuthResponse(AppUser user)
    {
        var accessToken = _jwtService.GenerateAccessToken(user);
        var refreshToken = _jwtService.GenerateRefreshToken();
        var expiresIn = _jwtService.GetTokenExpirationTime();

        return new AuthResponse
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = expiresIn,
            User = new UserInfo
            {
                Id = user.Id,
                CustomUserId = user.CustomUserId,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                AvatarUrl = user.AvatarUrl,
                Bio = user.Bio,
                UserStatus = user.UserStatus.ToString(),
                CreatedAt = user.CreatedAt
            }
        };
    }

    /// <summary>
    /// 生成唯一的自定义用户ID
    /// </summary>
    private async Task<string> GenerateUniqueCustomUserIdAsync(string username, CancellationToken cancellationToken)
    {
        // 基于用户名生成，去除特殊字符，转小写
        var baseId = username.ToLowerInvariant()
            .Replace(" ", "")
            .Replace("-", "")
            .Replace("_", "");

        // 如果基础ID可用，直接返回
        if (await IsCustomUserIdAvailableAsync(baseId, cancellationToken))
        {
            return baseId;
        }

        // 否则添加数字后缀
        for (int i = 1; i <= 999; i++)
        {
            var candidateId = $"{baseId}{i}";
            if (await IsCustomUserIdAvailableAsync(candidateId, cancellationToken))
            {
                return candidateId;
            }
        }

        // 如果还是不行，使用GUID
        return Guid.NewGuid().ToString("N")[..8];
    }

    public async Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("处理忘记密码请求: {EmailOrPhone}", request.EmailOrPhone);

            // 查找用户
            var users = await _unitOfWork.Users.FindAsync(
                u => u.Email == request.EmailOrPhone || u.Phone == request.EmailOrPhone,
                cancellationToken);

            var user = users.FirstOrDefault();
            if (user == null)
            {
                // 为了安全，不透露用户是否存在
                _logger.LogWarning("忘记密码请求的用户不存在: {EmailOrPhone}", request.EmailOrPhone);
                return ApiResponse<bool>.SuccessResult(true, "如果该邮箱/手机号已注册，您将收到密码重置邮件");
            }

            // 生成重置令牌
            var resetToken = GenerateResetToken();
            var expiresAt = DateTime.UtcNow.AddHours(1); // 1小时有效期

            // 保存重置令牌
            var passwordResetToken = new PasswordResetToken
            {
                UserId = user.Id,
                Token = resetToken,
                EmailOrPhone = request.EmailOrPhone,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = expiresAt,
                IsUsed = false
            };

            await _unitOfWork.PasswordResetTokens.AddAsync(passwordResetToken, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 发送重置邮件
            if (IsEmail(request.EmailOrPhone))
            {
                var emailSent = await _emailService.SendPasswordResetEmailAsync(
                    request.EmailOrPhone,
                    resetToken,
                    user.Username,
                    cancellationToken);

                if (!emailSent)
                {
                    _logger.LogError("发送密码重置邮件失败: {Email}", request.EmailOrPhone);
                    return ApiResponse<bool>.ErrorResult("发送重置邮件失败，请稍后重试", "EMAIL_SEND_FAILED");
                }
            }
            else
            {
                // TODO: 实现短信发送
                _logger.LogWarning("短信发送功能尚未实现: {Phone}", request.EmailOrPhone);
                return ApiResponse<bool>.ErrorResult("短信发送功能暂未开放", "SMS_NOT_IMPLEMENTED");
            }

            _logger.LogInformation("密码重置邮件发送成功: {EmailOrPhone}", request.EmailOrPhone);
            return ApiResponse<bool>.SuccessResult(true, "密码重置邮件已发送，请查收");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理忘记密码请求时发生错误: {EmailOrPhone}", request.EmailOrPhone);
            return ApiResponse<bool>.ErrorResult("处理请求时发生错误", "FORGOT_PASSWORD_ERROR");
        }
    }

    public async Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("处理重置密码请求");

            // 查找重置令牌
            var tokens = await _unitOfWork.PasswordResetTokens.FindAsync(
                t => t.Token == request.Token && !t.IsUsed,
                cancellationToken);

            var resetToken = tokens.FirstOrDefault();
            if (resetToken == null)
            {
                _logger.LogWarning("无效的重置令牌: {Token}", request.Token);
                return ApiResponse<bool>.ErrorResult("无效的重置令牌", "INVALID_TOKEN");
            }

            // 检查令牌是否过期
            if (resetToken.ExpiresAt < DateTime.UtcNow)
            {
                _logger.LogWarning("重置令牌已过期: {Token}", request.Token);
                return ApiResponse<bool>.ErrorResult("重置令牌已过期，请重新申请", "TOKEN_EXPIRED");
            }

            // 获取用户
            var user = await _unitOfWork.Users.GetByIdAsync(resetToken.UserId, cancellationToken);
            if (user == null)
            {
                _logger.LogError("重置令牌对应的用户不存在: {UserId}", resetToken.UserId);
                return ApiResponse<bool>.ErrorResult("用户不存在", "USER_NOT_FOUND");
            }

            // 更新密码
            user.PasswordHash = _passwordService.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            // 标记令牌为已使用
            resetToken.IsUsed = true;
            resetToken.UsedAt = DateTime.UtcNow;

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户密码重置成功: {UserId}", user.Id);
            return ApiResponse<bool>.SuccessResult(true, "密码重置成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码时发生错误");
            return ApiResponse<bool>.ErrorResult("重置密码时发生错误", "RESET_PASSWORD_ERROR");
        }
    }

    private string GenerateResetToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    private bool IsEmail(string input)
    {
        return input.Contains('@') && input.Contains('.');
    }
}
