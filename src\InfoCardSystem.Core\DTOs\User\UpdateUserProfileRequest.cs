using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.User;

/// <summary>
/// 更新用户资料请求
/// </summary>
public class UpdateUserProfileRequest
{
    /// <summary>
    /// 用户名
    /// </summary>
    [StringLength(100, MinimumLength = 2, ErrorMessage = "用户名长度必须在2-100个字符之间")]
    public string? Username { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    [Phone(ErrorMessage = "手机号格式不正确")]
    [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
    public string? Phone { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    [StringLength(1000, ErrorMessage = "个人简介长度不能超过1000个字符")]
    public string? Bio { get; set; }
}

/// <summary>
/// 修改密码请求
/// </summary>
public class ChangePasswordRequest
{
    /// <summary>
    /// 当前密码
    /// </summary>
    [Required(ErrorMessage = "当前密码不能为空")]
    public string CurrentPassword { get; set; } = string.Empty;
    
    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "新密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = string.Empty;
    
    /// <summary>
    /// 确认新密码
    /// </summary>
    [Required(ErrorMessage = "确认新密码不能为空")]
    [Compare("NewPassword", ErrorMessage = "两次输入的新密码不一致")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}

/// <summary>
/// 用户搜索请求
/// </summary>
public class SearchUsersRequest
{
    /// <summary>
    /// 搜索关键词
    /// </summary>
    [StringLength(100, ErrorMessage = "搜索关键词长度不能超过100个字符")]
    public string? Keyword { get; set; }
    
    /// <summary>
    /// 页码
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
    public int Page { get; set; } = 1;
    
    /// <summary>
    /// 每页大小
    /// </summary>
    [Range(1, 50, ErrorMessage = "每页大小必须在1-50之间")]
    public int PageSize { get; set; } = 10;
}
