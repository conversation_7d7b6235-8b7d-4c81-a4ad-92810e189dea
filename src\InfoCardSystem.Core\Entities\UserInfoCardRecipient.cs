using InfoCardSystem.Shared.Enums;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户资讯卡接收者实体
/// </summary>
public class UserInfoCardRecipient : BaseEntity
{
    /// <summary>
    /// 资讯卡ID
    /// </summary>
    public int InfoCardId { get; set; }
    
    /// <summary>
    /// 接收者ID（可以是用户或群组）
    /// </summary>
    public int RecipientId { get; set; }
    
    /// <summary>
    /// 接收者类型
    /// </summary>
    public RecipientType RecipientType { get; set; }
    
    /// <summary>
    /// 是否已读
    /// </summary>
    public bool IsRead { get; set; } = false;
    
    /// <summary>
    /// 是否可见（群组解散或成员移除时设为false）
    /// </summary>
    public bool IsVisible { get; set; } = true;
    
    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 资讯卡
    /// </summary>
    public virtual UserInfoCard InfoCard { get; set; } = null!;
    
    /// <summary>
    /// 接收用户（当RecipientType为UserRecipient时）
    /// </summary>
    public virtual AppUser? RecipientUser { get; set; }
    
    /// <summary>
    /// 接收群组（当RecipientType为GroupRecipient时）
    /// </summary>
    public virtual UserGroup? RecipientGroup { get; set; }
}
