using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 缓存服务实现
/// </summary>
public class CacheService : ICacheService
{
    private readonly ILogger<CacheService> _logger;
    private readonly ConcurrentDictionary<string, CacheItem> _cache;

    public CacheService(ILogger<CacheService> logger)
    {
        _logger = logger;
        _cache = new ConcurrentDictionary<string, CacheItem>();
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    _logger.LogDebug("缓存已过期: {Key}", key);
                    return null;
                }

                _logger.LogDebug("缓存命中: {Key}", key);
                return JsonSerializer.Deserialize<T>(cacheItem.Value);
            }

            _logger.LogDebug("缓存未命中: {Key}", key);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存失败: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        try
        {
            var json = JsonSerializer.Serialize(value);
            var expiryTime = expiry.HasValue ? DateTime.UtcNow.Add(expiry.Value) : DateTime.MaxValue;
            
            var cacheItem = new CacheItem(json, expiryTime);
            _cache.AddOrUpdate(key, cacheItem, (k, v) => cacheItem);
            
            _logger.LogDebug("缓存设置成功: {Key}, 过期时间: {Expiry}", key, expiryTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置缓存失败: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key)
    {
        try
        {
            _cache.TryRemove(key, out _);
            _logger.LogDebug("缓存移除成功: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除缓存失败: {Key}", key);
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    return false;
                }
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查缓存存在性失败: {Key}", key);
            return false;
        }
    }

    public async Task ClearAsync()
    {
        try
        {
            _cache.Clear();
            _logger.LogInformation("缓存清除完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除缓存失败");
        }
    }

    private class CacheItem
    {
        public string Value { get; }
        public DateTime ExpiryTime { get; }
        public bool IsExpired => DateTime.UtcNow > ExpiryTime;

        public CacheItem(string value, DateTime expiryTime)
        {
            Value = value;
            ExpiryTime = expiryTime;
        }
    }
}
