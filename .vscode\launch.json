{"version": "0.2.0", "configurations": [{"name": "Launch API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/src/InfoCardSystem.API/bin/Debug/net9.0/InfoCardSystem.API.dll", "args": [], "cwd": "${workspaceFolder}/src/InfoCardSystem.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:8001;http://localhost:8000"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build-web", "program": "${workspaceFolder}/src/InfoCardSystem.Web/bin/Debug/net9.0/InfoCardSystem.Web.dll", "args": [], "cwd": "${workspaceFolder}/src/InfoCardSystem.Web", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:8002;http://localhost:8002"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Launch API + Web", "type": "coreclr", "request": "launch", "preLaunchTask": "build-all", "program": "${workspaceFolder}/src/InfoCardSystem.API/bin/Debug/net9.0/InfoCardSystem.API.dll", "args": [], "cwd": "${workspaceFolder}/src/InfoCardSystem.API", "stopAtEntry": false, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:8001;http://localhost:8000"}, "postDebugTask": "launch-web"}], "compounds": [{"name": "Launch API + Web (Compound)", "configurations": ["Launch API", "Launch Web"], "stopAll": true}]}