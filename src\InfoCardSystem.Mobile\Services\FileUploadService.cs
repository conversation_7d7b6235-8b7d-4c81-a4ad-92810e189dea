using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 文件上传服务实现
/// </summary>
public class FileUploadService : IFileUploadService
{
    private readonly ILogger<FileUploadService> _logger;
    private readonly IInfoCardApiClient _apiClient;

    public FileUploadService(ILogger<FileUploadService> logger, IInfoCardApiClient apiClient)
    {
        _logger = logger;
        _apiClient = apiClient;
    }

    public async Task<string?> TakePhotoAsync()
    {
        try
        {
            _logger.LogDebug("启动相机拍照");

            if (MediaPicker.Default.IsCaptureSupported)
            {
                var photo = await MediaPicker.Default.CapturePhotoAsync();
                if (photo != null)
                {
                    // 保存到本地临时目录
                    var localFilePath = Path.Combine(FileSystem.CacheDirectory, photo.FileName);
                    
                    using var sourceStream = await photo.OpenReadAsync();
                    using var localFileStream = File.OpenWrite(localFilePath);
                    await sourceStream.CopyToAsync(localFileStream);

                    _logger.LogInformation("拍照成功: {FilePath}", localFilePath);
                    return localFilePath;
                }
            }
            else
            {
                _logger.LogWarning("设备不支持拍照功能");
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "拍照失败");
            return null;
        }
    }

    public async Task<string?> PickPhotoAsync()
    {
        try
        {
            _logger.LogDebug("从相册选择照片");

            var photo = await MediaPicker.Default.PickPhotoAsync();
            if (photo != null)
            {
                // 保存到本地临时目录
                var localFilePath = Path.Combine(FileSystem.CacheDirectory, photo.FileName);
                
                using var sourceStream = await photo.OpenReadAsync();
                using var localFileStream = File.OpenWrite(localFilePath);
                await sourceStream.CopyToAsync(localFileStream);

                _logger.LogInformation("选择照片成功: {FilePath}", localFilePath);
                return localFilePath;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "选择照片失败");
            return null;
        }
    }

    public async Task<string?> PickFileAsync()
    {
        try
        {
            _logger.LogDebug("选择文件");

            var customFileType = new FilePickerFileType(
                new Dictionary<DevicePlatform, IEnumerable<string>>
                {
                    { DevicePlatform.Android, new[] { "application/pdf", "text/plain", "application/msword" } },
                });

            var options = new PickOptions()
            {
                PickerTitle = "选择文件",
                FileTypes = customFileType,
            };

            var result = await FilePicker.Default.PickAsync(options);
            if (result != null)
            {
                // 保存到本地临时目录
                var localFilePath = Path.Combine(FileSystem.CacheDirectory, result.FileName);
                
                using var sourceStream = await result.OpenReadAsync();
                using var localFileStream = File.OpenWrite(localFilePath);
                await sourceStream.CopyToAsync(localFileStream);

                _logger.LogInformation("选择文件成功: {FilePath}", localFilePath);
                return localFilePath;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "选择文件失败");
            return null;
        }
    }

    public async Task<string?> UploadFileAsync(string filePath, string fileName, string contentType)
    {
        try
        {
            _logger.LogDebug("上传文件: {FileName}", fileName);

            if (!await FileExistsAsync(filePath))
            {
                _logger.LogWarning("文件不存在: {FilePath}", filePath);
                return null;
            }

            using var fileStream = File.OpenRead(filePath);
            var response = await _apiClient.UploadFileAsync(fileStream, fileName, contentType);

            if (response.Success && response.Data != null)
            {
                _logger.LogInformation("文件上传成功: {FileName} -> {Url}", fileName, response.Data.Url);
                return response.Data.Url;
            }
            else
            {
                _logger.LogWarning("文件上传失败: {FileName}, 错误: {Error}", fileName, response.Message);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传文件失败: {FileName}", fileName);
            return null;
        }
    }

    public async Task<List<string>> UploadFilesAsync(List<(string filePath, string fileName, string contentType)> files)
    {
        var uploadedUrls = new List<string>();

        try
        {
            _logger.LogDebug("批量上传文件: {Count} 个文件", files.Count);

            var uploadTasks = files.Select(async file =>
            {
                var url = await UploadFileAsync(file.filePath, file.fileName, file.contentType);
                return url;
            });

            var results = await Task.WhenAll(uploadTasks);
            uploadedUrls.AddRange(results.Where(url => !string.IsNullOrEmpty(url))!);

            _logger.LogInformation("批量上传完成: {Uploaded}/{Total}", uploadedUrls.Count, files.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量上传文件失败");
        }

        return uploadedUrls;
    }

    public async Task<long> GetFileSizeAsync(string filePath)
    {
        try
        {
            if (await FileExistsAsync(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件大小失败: {FilePath}", filePath);
            return 0;
        }
    }

    public async Task<bool> FileExistsAsync(string filePath)
    {
        try
        {
            return File.Exists(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查文件存在性失败: {FilePath}", filePath);
            return false;
        }
    }

    public async Task DeleteFileAsync(string filePath)
    {
        try
        {
            if (await FileExistsAsync(filePath))
            {
                File.Delete(filePath);
                _logger.LogInformation("文件删除成功: {FilePath}", filePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败: {FilePath}", filePath);
        }
    }

    public async Task<string?> CompressImageAsync(string imagePath, int quality = 80)
    {
        try
        {
            _logger.LogDebug("压缩图片: {ImagePath}, 质量: {Quality}", imagePath, quality);

            if (!await FileExistsAsync(imagePath))
            {
                _logger.LogWarning("图片文件不存在: {ImagePath}", imagePath);
                return null;
            }

            // 这里应该实现图片压缩逻辑
            // 可以使用第三方库如SkiaSharp或ImageSharp
            // 暂时返回原文件路径
            
            var compressedPath = Path.Combine(
                FileSystem.CacheDirectory, 
                $"compressed_{Path.GetFileName(imagePath)}");

            // 模拟压缩过程
            File.Copy(imagePath, compressedPath, true);

            _logger.LogInformation("图片压缩完成: {OriginalPath} -> {CompressedPath}", imagePath, compressedPath);
            return compressedPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "压缩图片失败: {ImagePath}", imagePath);
            return null;
        }
    }
}
