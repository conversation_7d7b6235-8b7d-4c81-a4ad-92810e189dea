using System.Security.Claims;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.InfoCard;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 资讯卡管理控制器 - 处理信息卡的创建、查询、更新、删除等操作
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[Authorize]
[SwaggerTag("信息卡管理相关接口，包括创建、查询、更新、删除信息卡等功能（需要认证）")]
public class InfoCardsController : ControllerBase
{
    private readonly IInfoCardService _infoCardService;
    private readonly ILogger<InfoCardsController> _logger;

    public InfoCardsController(IInfoCardService infoCardService, ILogger<InfoCardsController> logger)
    {
        _infoCardService = infoCardService;
        _logger = logger;
    }

    /// <summary>
    /// 创建资讯卡
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的资讯卡</returns>
    /// <remarks>
    /// 创建一张新的信息卡。信息卡可以包含文本内容、标签、附件等。
    ///
    /// 信息卡类型说明：
    /// - 1: 文本卡片
    /// - 2: 图片卡片
    /// - 3: 链接卡片
    /// - 4: 文件卡片
    ///
    /// 示例请求：
    /// ```json
    /// {
    ///   "title": "我的第一张信息卡",
    ///   "content": "这是信息卡的详细内容...",
    ///   "infoCardType": 1,
    ///   "tags": ["生活", "分享"],
    ///   "isPublic": true,
    ///   "expiresAt": "2025-12-31T23:59:59Z"
    /// }
    /// ```
    ///
    /// 注意事项：
    /// - 标题和内容不能为空
    /// - 标签数量限制为10个
    /// - 过期时间可选，不设置则永不过期
    /// - 公开的信息卡可被其他用户搜索到
    /// </remarks>
    /// <response code="200">成功创建信息卡</response>
    /// <response code="400">请求参数验证失败</response>
    /// <response code="401">未授权，需要登录</response>
    /// <response code="500">服务器内部错误</response>
    [HttpPost]
    [SwaggerOperation(
        Summary = "创建信息卡",
        Description = "创建一张新的信息卡，支持多种类型和自定义标签",
        OperationId = "CreateInfoCard",
        Tags = new[] { "InfoCard Management" }
    )]
    [SwaggerResponse(200, "成功创建信息卡", typeof(ApiResponse<InfoCardDto>))]
    [SwaggerResponse(400, "请求参数验证失败")]
    [SwaggerResponse(401, "未授权，需要登录")]
    [SwaggerResponse(500, "服务器内部错误")]
    public async Task<ActionResult<ApiResponse<InfoCardDto>>> CreateInfoCard(
        [FromBody] CreateInfoCardRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<InfoCardDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<InfoCardDto>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _infoCardService.CreateInfoCardAsync(userId.Value, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取资讯卡详情
    /// </summary>
    /// <param name="id">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<InfoCardDto>>> GetInfoCard(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<InfoCardDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.GetInfoCardAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 更新资讯卡
    /// </summary>
    /// <param name="id">资讯卡ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的资讯卡</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<InfoCardDto>>> UpdateInfoCard(
        int id,
        [FromBody] UpdateInfoCardRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<InfoCardDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<InfoCardDto>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _infoCardService.UpdateInfoCardAsync(userId.Value, id, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 删除资讯卡
    /// </summary>
    /// <param name="id">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteInfoCard(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.DeleteInfoCardAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取用户的资讯卡列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡列表</returns>
    [HttpGet("my")]
    public async Task<ActionResult<ApiResponse<PagedResult<InfoCardDto>>>> GetMyInfoCards(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.GetUserInfoCardsAsync(userId.Value, page, pageSize, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取时间线资讯卡
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>资讯卡列表</returns>
    [HttpGet("timeline")]
    public async Task<ActionResult<ApiResponse<PagedResult<InfoCardDto>>>> GetTimeline(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.GetTimelineInfoCardsAsync(userId.Value, page, pageSize, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 转发资讯卡
    /// </summary>
    /// <param name="id">资讯卡ID</param>
    /// <param name="request">转发请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/forward")]
    public async Task<ActionResult<ApiResponse<bool>>> ForwardInfoCard(
        int id,
        [FromBody] ForwardInfoCardRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.ForwardInfoCardAsync(userId.Value, id, request.RecipientUserIds, request.RecipientGroupIds, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 收藏/取消收藏资讯卡
    /// </summary>
    /// <param name="id">资讯卡ID</param>
    /// <param name="request">收藏请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/favorite")]
    public async Task<ActionResult<ApiResponse<bool>>> ToggleFavorite(
        int id,
        [FromBody] FavoriteRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.ToggleFavoriteAsync(userId.Value, id, request.IsFavorite, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取收藏的资讯卡列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>收藏的资讯卡列表</returns>
    [HttpGet("favorites")]
    public async Task<ActionResult<ApiResponse<PagedResult<InfoCardDto>>>> GetFavorites(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<InfoCardDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _infoCardService.GetFavoriteInfoCardsAsync(userId.Value, page, pageSize, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
