using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户群组成员实体配置
/// </summary>
public class UserGroupMemberConfiguration : IEntityTypeConfiguration<UserGroupMember>
{
    public void Configure(EntityTypeBuilder<UserGroupMember> builder)
    {
        // 表名
        builder.ToTable("user_group_members");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.GroupId)
            .IsRequired();
            
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.JoinedAt)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => new { x.GroupId, x.UserId })
            .IsUnique()
            .HasDatabaseName("idx_group_members_group_user");
            
        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("idx_group_members_user");
        
        // 关系配置已在UserGroupConfiguration和AppUserConfiguration中定义
    }
}
