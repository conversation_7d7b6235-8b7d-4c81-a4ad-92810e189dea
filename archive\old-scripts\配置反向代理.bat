@echo off
echo 配置IIS反向代理模式...
echo.

echo 创建API反向代理web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<rewrite^>
echo       ^<rules^>
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^>
echo           ^<match url="(.*)" /^>
echo           ^<action type="Rewrite" url="http://localhost:5000/{R:1}" /^>
echo         ^</rule^>
echo       ^</rules^>
echo     ^</rewrite^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo 创建Web反向代理web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<rewrite^>
echo       ^<rules^>
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^>
echo           ^<match url="(.*)" /^>
echo           ^<action type="Rewrite" url="http://localhost:7000/{R:1}" /^>
echo         ^</rule^>
echo       ^</rules^>
echo     ^</rewrite^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo 反向代理配置完成！
echo.
echo 注意: 需要确保开发环境服务正在运行
echo API服务: http://localhost:5000
echo Web服务: http://localhost:7000
