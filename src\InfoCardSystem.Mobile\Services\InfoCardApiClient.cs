using InfoCardSystem.Mobile.Models;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// InfoCard API客户端实现类
/// 负责与后端API服务进行HTTP通信，处理所有的网络请求和响应
///
/// 主要功能：
/// - 用户认证相关API调用（登录、注册、令牌刷新等）
/// - 资讯卡管理API调用（创建、获取、更新、删除等）
/// - 好友管理API调用（添加、删除、搜索等）
/// - 文件上传API调用
/// - 统一的错误处理和日志记录
///
/// 技术特性：
/// - 基于HttpClient的异步HTTP通信
/// - JSON序列化/反序列化
/// - 自动认证令牌管理
/// - 统一的API响应包装
/// - 完整的错误处理和重试机制
/// </summary>
/// <remarks>
/// 此类是移动应用与后端API通信的唯一入口，所有的网络请求都通过此类进行。
/// 支持自动添加认证头、请求日志记录、响应解析等功能。
/// </remarks>
public class InfoCardApiClient : IInfoCardApiClient
{
    #region 私有字段

    /// <summary>HTTP客户端，用于发送网络请求</summary>
    private readonly HttpClient _httpClient;

    /// <summary>日志记录器，用于记录API调用日志</summary>
    private readonly ILogger<InfoCardApiClient> _logger;

    /// <summary>JSON序列化选项，统一配置序列化行为</summary>
    private readonly JsonSerializerOptions _jsonOptions;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化InfoCard API客户端实例
    /// </summary>
    /// <param name="httpClient">HTTP客户端实例，由依赖注入容器提供</param>
    /// <param name="logger">日志记录器实例</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public InfoCardApiClient(HttpClient httpClient, ILogger<InfoCardApiClient> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 配置JSON序列化选项
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,      // 使用驼峰命名
            PropertyNameCaseInsensitive = true,                     // 忽略大小写
            WriteIndented = false                                   // 压缩JSON输出
        };

        _logger.LogDebug("InfoCard API客户端已初始化，BaseAddress: {BaseAddress}",
            _httpClient.BaseAddress);
    }

    #endregion

    #region 认证相关API

    /// <summary>
    /// 执行用户登录API调用
    /// </summary>
    /// <param name="request">登录请求，包含用户名/邮箱/手机号和密码</param>
    /// <returns>包含登录结果、用户信息和认证令牌的API响应</returns>
    /// <remarks>
    /// 此方法会向后端API发送POST请求到 /auth/login 端点。
    /// 成功登录后，响应中会包含访问令牌、刷新令牌和用户基本信息。
    /// </remarks>
    /// <exception cref="ArgumentNullException">当request为null时抛出</exception>
    public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
    {
        ArgumentNullException.ThrowIfNull(request);

        try
        {
            _logger.LogDebug("发送登录API请求: {Username}", request.UsernameOrEmailOrPhone);

            // 调用通用POST方法发送登录请求
            var response = await PostAsync<LoginResponse>("auth/login", request);

            if (response.Success)
            {
                _logger.LogInformation("登录API请求成功: {Username}", request.UsernameOrEmailOrPhone);
            }
            else
            {
                _logger.LogWarning("登录API请求失败: {Username}, 错误: {Message}",
                    request.UsernameOrEmailOrPhone, response.Message);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "登录API请求异常: {Username}", request.UsernameOrEmailOrPhone);
            return ApiResponse<LoginResponse>.ErrorResult("网络请求失败，请检查网络连接");
        }
    }

    public async Task<ApiResponse<RegisterResponse>> RegisterAsync(RegisterRequest request)
    {
        try
        {
            _logger.LogDebug("发送注册请求: {Username}", request.Username);
            
            var response = await PostAsync<RegisterResponse>("auth/register", request);
            
            if (response.Success)
            {
                _logger.LogInformation("注册请求成功: {Username}", request.Username);
            }
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册请求失败: {Username}", request.Username);
            return ApiResponse<RegisterResponse>.ErrorResult("网络请求失败，请检查网络连接");
        }
    }

    public async Task<ApiResponse<TokenResponse>> RefreshTokenAsync(string refreshToken)
    {
        try
        {
            _logger.LogDebug("发送刷新令牌请求");
            
            var request = new { RefreshToken = refreshToken };
            var response = await PostAsync<TokenResponse>("auth/refresh", request);
            
            if (response.Success)
            {
                _logger.LogInformation("刷新令牌成功");
            }
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌失败");
            return ApiResponse<TokenResponse>.ErrorResult("刷新令牌失败");
        }
    }

    public async Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            _logger.LogDebug("发送忘记密码请求: {EmailOrPhone}", request.EmailOrPhone);
            
            var response = await PostAsync<bool>("auth/forgot-password", request);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "忘记密码请求失败: {EmailOrPhone}", request.EmailOrPhone);
            return ApiResponse<bool>.ErrorResult("网络请求失败，请检查网络连接");
        }
    }

    public async Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            _logger.LogDebug("发送重置密码请求");
            
            var response = await PostAsync<bool>("auth/reset-password", request);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码请求失败");
            return ApiResponse<bool>.ErrorResult("网络请求失败，请检查网络连接");
        }
    }

    public async Task<ApiResponse<bool>> CheckUsernameAvailabilityAsync(string username)
    {
        try
        {
            var response = await GetAsync<bool>($"auth/check-username?username={Uri.EscapeDataString(username)}");
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查用户名可用性失败: {Username}", username);
            return ApiResponse<bool>.ErrorResult("网络请求失败");
        }
    }

    public async Task<ApiResponse<bool>> CheckEmailAvailabilityAsync(string email)
    {
        try
        {
            var response = await GetAsync<bool>($"auth/check-email?email={Uri.EscapeDataString(email)}");
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查邮箱可用性失败: {Email}", email);
            return ApiResponse<bool>.ErrorResult("网络请求失败");
        }
    }

    public async Task<ApiResponse<bool>> CheckPhoneAvailabilityAsync(string phone)
    {
        try
        {
            var response = await GetAsync<bool>($"auth/check-phone?phone={Uri.EscapeDataString(phone)}");
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查手机号可用性失败: {Phone}", phone);
            return ApiResponse<bool>.ErrorResult("网络请求失败");
        }
    }

    #endregion

    #region 用户相关

    public async Task<ApiResponse<UserProfile>> GetUserProfileAsync()
    {
        try
        {
            var response = await GetAsync<UserProfile>("users/profile");
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户资料失败");
            return ApiResponse<UserProfile>.ErrorResult("获取用户资料失败");
        }
    }

    public async Task<ApiResponse<UserProfile>> UpdateUserProfileAsync(UpdateUserProfileRequest request)
    {
        try
        {
            var response = await PutAsync<UserProfile>("users/profile", request);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户资料失败");
            return ApiResponse<UserProfile>.ErrorResult("更新用户资料失败");
        }
    }

    public async Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordRequest request)
    {
        try
        {
            var response = await PutAsync<bool>("users/password", request);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败");
            return ApiResponse<bool>.ErrorResult("修改密码失败");
        }
    }

    public async Task<ApiResponse<List<UserInfo>>> SearchUsersAsync(string query, int page = 1, int pageSize = 20)
    {
        try
        {
            var url = $"users/search?query={Uri.EscapeDataString(query)}&page={page}&pageSize={pageSize}";
            var response = await GetAsync<List<UserInfo>>(url);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索用户失败: {Query}", query);
            return ApiResponse<List<UserInfo>>.ErrorResult("搜索用户失败");
        }
    }

    public async Task<ApiResponse<UserQRCode>> GetUserQRCodeAsync()
    {
        try
        {
            var response = await GetAsync<UserQRCode>("users/qrcode");
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户二维码失败");
            return ApiResponse<UserQRCode>.ErrorResult("获取用户二维码失败");
        }
    }

    public async Task<ApiResponse<List<UserInfo>>> MatchContactsAsync(ContactsMatchRequest request)
    {
        try
        {
            var response = await PostAsync<List<UserInfo>>("users/contacts-match", request);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "匹配联系人失败");
            return ApiResponse<List<UserInfo>>.ErrorResult("匹配联系人失败");
        }
    }

    #endregion

    #region HTTP辅助方法

    private async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
    {
        var response = await _httpClient.GetAsync(endpoint);
        return await ProcessResponseAsync<T>(response);
    }

    private async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
    {
        var json = JsonSerializer.Serialize(data, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync(endpoint, content);
        return await ProcessResponseAsync<T>(response);
    }

    private async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
    {
        var json = JsonSerializer.Serialize(data, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PutAsync(endpoint, content);
        return await ProcessResponseAsync<T>(response);
    }

    private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        if (response.IsSuccessStatusCode)
        {
            try
            {
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
                return apiResponse ?? ApiResponse<T>.ErrorResult("响应解析失败");
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "JSON反序列化失败: {Content}", content);
                return ApiResponse<T>.ErrorResult("响应格式错误");
            }
        }
        else
        {
            _logger.LogWarning("API请求失败: {StatusCode} - {Content}", response.StatusCode, content);
            
            try
            {
                var errorResponse = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
                return errorResponse ?? ApiResponse<T>.ErrorResult($"请求失败: {response.StatusCode}");
            }
            catch
            {
                return ApiResponse<T>.ErrorResult($"请求失败: {response.StatusCode}");
            }
        }
    }

    #endregion

    #region 令牌管理

    public void SetAuthToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        _logger.LogDebug("设置认证令牌");
    }

    public void ClearAuthToken()
    {
        _httpClient.DefaultRequestHeaders.Authorization = null;
        _logger.LogDebug("清除认证令牌");
    }

    #endregion

    #region 未实现的方法 (占位符)

    public Task<ApiResponse<List<Friend>>> GetFriendsAsync()
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> SendFriendRequestAsync(FriendRequestRequest request)
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> HandleFriendRequestAsync(int requestId, bool accept, string? message = null)
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<List<FriendRequestService>>> GetFriendRequestsAsync()
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> ScanQRCodeAddFriendAsync(ScanQRCodeRequest request)
    {
        throw new NotImplementedException("扫码功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> UpdateFriendAliasAsync(int friendId, string alias)
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> DeleteFriendAsync(int friendId)
    {
        throw new NotImplementedException("好友功能将在后续版本实现");
    }

    public Task<ApiResponse<PagedResult<InfoCard>>> GetTimelineAsync(int page = 1, int pageSize = 20)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<PagedResult<InfoCard>>> GetMyInfoCardsAsync(int page = 1, int pageSize = 20)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<InfoCard>> GetInfoCardAsync(int id)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<InfoCard>> CreateInfoCardAsync(CreateInfoCardRequest request)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<InfoCard>> UpdateInfoCardAsync(int id, UpdateInfoCardRequest request)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> DeleteInfoCardAsync(int id)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> ForwardInfoCardAsync(int id, ForwardInfoCardRequest request)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> FavoriteInfoCardAsync(int id)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<bool>> UnfavoriteInfoCardAsync(int id)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<PagedResult<InfoCard>>> GetFavoritesAsync(int page = 1, int pageSize = 20)
    {
        throw new NotImplementedException("资讯卡功能将在后续版本实现");
    }

    public Task<ApiResponse<Attachment>> UploadFileAsync(Stream fileStream, string fileName, string contentType)
    {
        throw new NotImplementedException("文件上传功能将在后续版本实现");
    }

    public Task<ApiResponse<List<Attachment>>> UploadFilesAsync(List<FileUploadInfo> files)
    {
        throw new NotImplementedException("文件上传功能将在后续版本实现");
    }

    public Task<Stream> DownloadFileAsync(int attachmentId)
    {
        throw new NotImplementedException("文件下载功能将在后续版本实现");
    }

    #endregion
}
