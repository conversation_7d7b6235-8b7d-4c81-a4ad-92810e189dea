@echo off
echo ========================================
echo InfoCard Configuration Verification
echo ========================================
echo.

echo Checking API configuration files...
echo.

echo 1. API launchSettings.json:
findstr "applicationUrl" src\InfoCardSystem.API\Properties\launchSettings.json
echo.

echo 2. API appsettings.json:
findstr "BaseUrl" src\InfoCardSystem.API\appsettings.json
echo.

echo 3. API appsettings.Development.json:
findstr "BaseUrl" src\InfoCardSystem.API\appsettings.Development.json
echo.

echo 4. API publish appsettings.json:
findstr "BaseUrl" src\InfoCardSystem.API\publish\appsettings.json
echo.

echo Checking Web configuration files...
echo.

echo 5. Web launchSettings.json:
findstr "applicationUrl" src\InfoCardSystem.Web\Properties\launchSettings.json
echo.

echo 6. Web appsettings.json:
findstr "BaseUrl" src\InfoCardSystem.Web\appsettings.json
echo.

echo 7. Web appsettings.Development.json:
findstr "BaseUrl" src\InfoCardSystem.Web\appsettings.Development.json
echo.

echo 8. Web Program.cs default URL:
findstr "localhost:" src\InfoCardSystem.Web\Program.cs
echo.

echo Checking solution file...
echo.

echo 9. Solution includes Web project:
findstr "InfoCardSystem.Web" InfoCardSystem.sln | findstr "Project"
echo.

echo ========================================
echo Configuration Summary
echo ========================================
echo.
echo Expected Configuration:
echo - API Development: https://localhost:8001
echo - Web Development: https://localhost:8002
echo - API Production: http://localhost:8001
echo - Web Production: http://localhost:8002
echo.

echo Checking for common issues...
echo.

echo Looking for old port references (7001, 5001, 8081, 8082):
findstr /R "700[0-9]\|500[0-9]\|808[0-9]" src\InfoCardSystem.API\appsettings*.json src\InfoCardSystem.Web\appsettings*.json src\InfoCardSystem.Web\Program.cs 2>nul
if %errorLevel% equ 0 (
    echo WARNING: Found old port references above!
) else (
    echo No old port references found - Good!
)
echo.

echo Verification complete!
echo.
echo If all configurations show the correct ports (8001 for API, 8002 for Web),
echo then you can proceed with:
echo 1. start-for-vs2022.bat - For VS2022 development
echo 2. publish-to-webs-fixed.bat - For publishing to C:\Webs
echo 3. start-test-env.bat - For testing published applications
echo.

pause
