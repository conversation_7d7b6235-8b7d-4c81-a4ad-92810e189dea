<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.FriendsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:FriendsViewModel"
             Title="好友"
             BackgroundColor="{StaticResource Background}">

    <Grid RowDefinitions="Auto,*">

        <!-- 顶部搜索和添加 -->
        <Grid Grid.Row="0"
              BackgroundColor="{StaticResource Primary}"
              Padding="16,8"
              ColumnDefinitions="*,Auto">

            <!-- 搜索框 -->
            <Frame Grid.Column="0"
                   BackgroundColor="White"
                   CornerRadius="20"
                   Padding="12,8"
                   HasShadow="False"
                   Margin="0,0,8,0">
                <Entry Text="{Binding SearchQuery}"
                       Placeholder="搜索好友..."
                       FontSize="16"
                       BackgroundColor="Transparent"
                       TextColor="{StaticResource TextPrimary}"
                       PlaceholderColor="{StaticResource TextHint}" />
            </Frame>

            <!-- 添加好友按钮 -->
            <Button Grid.Column="1"
                   Text="+"
                   BackgroundColor="White"
                   TextColor="{StaticResource Primary}"
                   FontSize="24"
                   FontAttributes="Bold"
                   WidthRequest="44"
                   HeightRequest="44"
                   CornerRadius="22"
                   Command="{Binding AddFriendCommand}" />

        </Grid>

        <!-- 好友列表 -->
        <RefreshView Grid.Row="1"
                    IsRefreshing="{Binding IsRefreshing}"
                    Command="{Binding RefreshCommand}">

            <CollectionView ItemsSource="{Binding Friends}"
                           BackgroundColor="Transparent"
                           SelectionMode="None">

                <CollectionView.Header>
                    <StackLayout Padding="16,16,16,8">

                        <!-- 好友统计 -->
                        <Frame Style="{StaticResource CardFrame}"
                               BackgroundColor="{StaticResource PrimaryLight}"
                               Margin="0,0,0,8">
                            <Grid ColumnDefinitions="*,*,*">

                                <StackLayout Grid.Column="0" HorizontalOptions="Center">
                                    <Label Text="{Binding TotalFriends}"
                                           FontSize="20"
                                           FontAttributes="Bold"
                                           TextColor="White"
                                           HorizontalOptions="Center" />
                                    <Label Text="好友"
                                           FontSize="14"
                                           TextColor="White"
                                           Opacity="0.9"
                                           HorizontalOptions="Center" />
                                </StackLayout>

                                <StackLayout Grid.Column="1" HorizontalOptions="Center">
                                    <Label Text="{Binding OnlineFriends}"
                                           FontSize="20"
                                           FontAttributes="Bold"
                                           TextColor="White"
                                           HorizontalOptions="Center" />
                                    <Label Text="在线"
                                           FontSize="14"
                                           TextColor="White"
                                           Opacity="0.9"
                                           HorizontalOptions="Center" />
                                </StackLayout>

                                <StackLayout Grid.Column="2" HorizontalOptions="Center">
                                    <Label Text="{Binding PendingRequests}"
                                           FontSize="20"
                                           FontAttributes="Bold"
                                           TextColor="White"
                                           HorizontalOptions="Center" />
                                    <Label Text="请求"
                                           FontSize="14"
                                           TextColor="White"
                                           Opacity="0.9"
                                           HorizontalOptions="Center" />
                                </StackLayout>

                            </Grid>
                        </Frame>

                        <!-- 快速操作 -->
                        <Grid ColumnDefinitions="*,*"
                              ColumnSpacing="8"
                              Margin="0,0,0,16">

                            <Button Grid.Column="0"
                                   Text="📋 好友请求"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding FriendRequestsCommand}" />

                            <Button Grid.Column="1"
                                   Text="👥 群组"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding GroupsCommand}" />

                        </Grid>

                    </StackLayout>
                </CollectionView.Header>

                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="viewmodels:FriendViewModel">
                        <Grid Padding="16,8">

                            <!-- 好友卡片 -->
                            <Frame Style="{StaticResource CardFrame}"
                                   Margin="0">
                                <Grid ColumnDefinitions="Auto,*,Auto">

                                    <!-- 头像和在线状态 -->
                                    <Grid Grid.Column="0">
                                        <Frame WidthRequest="50"
                                               HeightRequest="50"
                                               CornerRadius="25"
                                               Padding="0"
                                               BackgroundColor="{StaticResource Gray200}">
                                            <Image Source="{Binding AvatarUrl, FallbackValue='default_avatar.png'}"
                                                   Aspect="AspectFill" />
                                        </Frame>

                                        <!-- 在线状态指示器 -->
                                        <Ellipse Fill="{Binding IsOnline, Converter={StaticResource OnlineStatusColorConverter}}"
                                                WidthRequest="16"
                                                HeightRequest="16"
                                                HorizontalOptions="End"
                                                VerticalOptions="End"
                                                Margin="0,0,-2,-2" />
                                    </Grid>

                                    <!-- 好友信息 -->
                                    <StackLayout Grid.Column="1"
                                                Margin="12,0,0,0"
                                                VerticalOptions="Center">
                                        <Label Text="{Binding DisplayName, FallbackValue={Binding Username}}"
                                               FontSize="16"
                                               FontAttributes="Bold"
                                               TextColor="{StaticResource TextPrimary}" />
                                        <Label Text="{Binding Alias}"
                                               FontSize="14"
                                               TextColor="{StaticResource TextSecondary}"
                                               IsVisible="{Binding Alias, Converter={StaticResource StringToBoolConverter}}" />
                                        <Label Text="{Binding LastActiveText}"
                                               FontSize="12"
                                               TextColor="{StaticResource TextHint}" />
                                    </StackLayout>

                                    <!-- 操作按钮 -->
                                    <Button Grid.Column="2"
                                           Text="💬"
                                           FontSize="18"
                                           BackgroundColor="Transparent"
                                           TextColor="{StaticResource Primary}"
                                           WidthRequest="44"
                                           HeightRequest="44"
                                           CornerRadius="22"
                                           Command="{Binding Source={x:Reference Name=FriendsPage}, Path=BindingContext.ChatWithFriendCommand}"
                                           CommandParameter="{Binding}" />

                                </Grid>
                            </Frame>

                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>

                <CollectionView.EmptyView>
                    <StackLayout VerticalOptions="Center"
                                HorizontalOptions="Center"
                                Padding="32">
                        <Label Text="👥"
                               FontSize="48"
                               HorizontalOptions="Center" />
                        <Label Text="暂无好友"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               HorizontalOptions="Center"
                               Margin="0,16,0,8" />
                        <Label Text="开始添加您的第一个好友吧"
                               FontSize="14"
                               TextColor="{StaticResource TextHint}"
                               HorizontalOptions="Center" />
                        <Button Text="添加好友"
                               Command="{Binding AddFriendCommand}"
                               Margin="0,16,0,0" />
                    </StackLayout>
                </CollectionView.EmptyView>

            </CollectionView>

        </RefreshView>

        <!-- 加载指示器 -->
        <ActivityIndicator Grid.Row="1"
                          IsRunning="{Binding IsBusy}"
                          IsVisible="{Binding IsBusy}"
                          VerticalOptions="Center"
                          HorizontalOptions="Center" />

    </Grid>

</ContentPage>
