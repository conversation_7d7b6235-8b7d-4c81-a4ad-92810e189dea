# InfoCard System - IIS配置指南

## 📋 概述

本指南将帮助您在Windows IIS上配置InfoCard系统的API和Web应用程序。

## 🔧 前提条件

- Windows 11 家庭版
- IIS 10.0.26
- .NET 9.0 Runtime
- ASP.NET Core Hosting Bundle
- MySQL 8.0

## 📁 文件部署

### 1. 发布文件位置
- **API**: `C:\Webs\ICAPI\`
- **Web**: `C:\Webs\ICWeb\`

### 2. 验证文件
确保以下文件存在：
```
C:\Webs\ICAPI\
├── InfoCardSystem.API.dll
├── InfoCardSystem.API.exe
├── web.config
├── appsettings.IIS.json
└── [其他依赖文件]

C:\Webs\ICWeb\
├── InfoCardSystem.Web.dll
├── InfoCardSystem.Web.exe
├── web.config
├── appsettings.IIS.json
└── wwwroot\
```

## 🌐 IIS网站配置

### 1. 创建应用程序池

#### API应用程序池 (ICAPI)
1. 打开IIS管理器
2. 右键点击"应用程序池" → "添加应用程序池"
3. 配置：
   - **名称**: ICAPI
   - **.NET CLR版本**: 无托管代码
   - **托管管道模式**: 集成
   - **标识**: ApplicationPoolIdentity

#### Web应用程序池 (ICWeb)
1. 右键点击"应用程序池" → "添加应用程序池"
2. 配置：
   - **名称**: ICWeb
   - **.NET CLR版本**: 无托管代码
   - **托管管道模式**: 集成
   - **标识**: ApplicationPoolIdentity

### 2. 创建网站

#### API网站 (ICAPI)
1. 右键点击"网站" → "添加网站"
2. 配置：
   - **网站名称**: ICAPI
   - **应用程序池**: ICAPI
   - **物理路径**: C:\Webs\ICAPI
   - **绑定类型**: http
   - **端口**: 8081
   - **主机名**: (留空)

#### Web网站 (ICWeb)
1. 右键点击"网站" → "添加网站"
2. 配置：
   - **网站名称**: ICWeb
   - **应用程序池**: ICWeb
   - **物理路径**: C:\Webs\ICWeb
   - **绑定类型**: http
   - **端口**: 8082
   - **主机名**: (留空)

## 🔐 权限配置

### 设置文件夹权限
为每个网站目录设置适当的权限：

```cmd
icacls "C:\Webs\ICAPI" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\Webs\ICAPI" /grant "IUSR:(OI)(CI)R" /T
icacls "C:\Webs\ICAPI" /grant "IIS AppPool\ICAPI:(OI)(CI)F" /T

icacls "C:\Webs\ICWeb" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\Webs\ICWeb" /grant "IUSR:(OI)(CI)R" /T
icacls "C:\Webs\ICWeb" /grant "IIS AppPool\ICWeb:(OI)(CI)F" /T
```

## ✅ 测试配置

### 1. 启动网站
1. 在IIS管理器中启动两个应用程序池
2. 启动两个网站

### 2. 测试URL
- **API健康检查**: http://localhost:8081/health
- **API文档**: http://localhost:8081/swagger
- **Web应用**: http://localhost:8082/

### 3. 预期响应
- API健康检查应返回JSON格式的健康状态
- Swagger页面应显示API文档
- Web应用应显示登录页面

## 🔧 故障排除

### 常见问题

1. **500.19错误 - 配置错误**
   - 检查web.config文件格式
   - 确保ASP.NET Core Hosting Bundle已安装

2. **500.30错误 - 应用程序启动失败**
   - 检查应用程序池配置
   - 查看Windows事件日志
   - 检查数据库连接

3. **403错误 - 禁止访问**
   - 检查文件夹权限
   - 确保应用程序池标识有适当权限

4. **数据库连接失败**
   - 验证MySQL服务正在运行
   - 检查连接字符串配置
   - 确保数据库用户权限正确

### 日志位置
- **IIS日志**: `C:\inetpub\logs\LogFiles\`
- **应用程序日志**: 检查应用程序目录下的logs文件夹
- **Windows事件日志**: 事件查看器 → Windows日志 → 应用程序

## 📞 支持

如果遇到问题，请检查：
1. IIS功能是否完全安装
2. .NET运行时版本是否正确
3. 数据库服务是否正常运行
4. 防火墙设置是否允许端口8081和8082

---

**注意**: 确保在生产环境中使用适当的安全配置和HTTPS。
