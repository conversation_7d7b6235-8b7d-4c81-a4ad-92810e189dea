using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.User;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using InfoCardSystem.Shared.Enums;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 用户服务实现
/// </summary>
public class UserService : IUserService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPasswordService _passwordService;
    private readonly IQRCodeService _qrCodeService;
    private readonly ILogger<UserService> _logger;
    private readonly ICacheService _cacheService;

    public UserService(
        IUnitOfWork unitOfWork,
        IPasswordService passwordService,
        IQRCodeService qrCodeService,
        ILogger<UserService> logger,
        ICacheService cacheService)
    {
        _unitOfWork = unitOfWork;
        _passwordService = passwordService;
        _qrCodeService = qrCodeService;
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 获取用户资料
    /// </summary>
    public async Task<ApiResponse<UserProfileDto>> GetUserProfileAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"user_profile_{userId}";

            // 尝试从缓存获取
            var cachedProfile = await _cacheService.GetOrSetAsync(cacheKey, async () =>
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
                if (user == null) return null;

                return new UserProfileDto
                {
                    Id = user.Id,
                    CustomUserId = user.CustomUserId,
                    Username = user.Username,
                    Email = user.Email,
                    Phone = user.Phone,
                    AvatarUrl = user.AvatarUrl,
                    Bio = user.Bio,
                    UserStatus = user.UserStatus.ToString(),
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                };
            }, TimeSpan.FromMinutes(15)); // 缓存15分钟

            if (cachedProfile == null)
            {
                return ApiResponse<UserProfileDto>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            return ApiResponse<UserProfileDto>.SuccessResult(cachedProfile, "获取用户资料成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户资料失败: UserId={UserId}", userId);
            return ApiResponse<UserProfileDto>.ErrorResult("获取用户资料失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 根据自定义用户ID获取用户资料
    /// </summary>
    public async Task<ApiResponse<UserProfileDto>> GetUserProfileByCustomIdAsync(string customUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.CustomUserId == customUserId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<UserProfileDto>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            var userProfile = new UserProfileDto
            {
                Id = user.Id,
                CustomUserId = user.CustomUserId,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                AvatarUrl = user.AvatarUrl,
                Bio = user.Bio,
                UserStatus = user.UserStatus.ToString(),
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };

            return ApiResponse<UserProfileDto>.SuccessResult(userProfile, "获取用户资料成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户资料失败: CustomUserId={CustomUserId}", customUserId);
            return ApiResponse<UserProfileDto>.ErrorResult("获取用户资料失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 更新用户资料
    /// </summary>
    public async Task<ApiResponse<UserProfileDto>> UpdateUserProfileAsync(int userId, UpdateUserProfileRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<UserProfileDto>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // 检查用户名是否已被其他用户使用
            if (!string.IsNullOrEmpty(request.Username) && request.Username != user.Username)
            {
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == request.Username && u.Id != userId, cancellationToken);
                if (existingUser != null)
                {
                    return ApiResponse<UserProfileDto>.ErrorResult("用户名已存在", ErrorCodes.USER_002);
                }
                user.Username = request.Username;
            }

            // 检查手机号是否已被其他用户使用
            if (!string.IsNullOrEmpty(request.Phone) && request.Phone != user.Phone)
            {
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Phone == request.Phone && u.Id != userId, cancellationToken);
                if (existingUser != null)
                {
                    return ApiResponse<UserProfileDto>.ErrorResult("手机号已存在", ErrorCodes.USER_002);
                }
                user.Phone = request.Phone;
            }

            // 更新个人简介
            if (request.Bio != null)
            {
                user.Bio = request.Bio;
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // 清理用户资料缓存
            var cacheKey = $"user_profile_{userId}";
            await _cacheService.RemoveAsync(cacheKey);

            _logger.LogInformation("用户资料更新成功: UserId={UserId}", userId);

            var userProfile = new UserProfileDto
            {
                Id = user.Id,
                CustomUserId = user.CustomUserId,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                AvatarUrl = user.AvatarUrl,
                Bio = user.Bio,
                UserStatus = user.UserStatus.ToString(),
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };

            return ApiResponse<UserProfileDto>.SuccessResult(userProfile, "更新用户资料成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新用户资料失败: UserId={UserId}", userId);
            return ApiResponse<UserProfileDto>.ErrorResult("更新用户资料失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    public async Task<ApiResponse<bool>> ChangePasswordAsync(int userId, ChangePasswordRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // 验证当前密码
            if (!_passwordService.VerifyPassword(request.CurrentPassword, user.PasswordHash))
            {
                return ApiResponse<bool>.ErrorResult("当前密码错误", ErrorCodes.AUTH_001);
            }

            // 更新密码
            user.PasswordHash = _passwordService.HashPassword(request.NewPassword);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户密码修改成功: UserId={UserId}", userId);
            return ApiResponse<bool>.SuccessResult(true, "密码修改成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修改密码失败: UserId={UserId}", userId);
            return ApiResponse<bool>.ErrorResult("修改密码失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 搜索用户
    /// </summary>
    public async Task<ApiResponse<PagedResult<UserProfileDto>>> SearchUsersAsync(SearchUsersRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _unitOfWork.Users.GetQueryable();

            // 应用搜索条件
            if (!string.IsNullOrEmpty(request.Keyword))
            {
                var keyword = request.Keyword.Trim();
                query = query.Where(u => 
                    u.Username.Contains(keyword) || 
                    u.CustomUserId.Contains(keyword) ||
                    (u.Email.Contains(keyword) && u.UserStatus == UserStatus.Active));
            }

            // 只返回活跃用户
            query = query.Where(u => u.UserStatus == UserStatus.Active);

            // 排序
            query = query.OrderBy(u => u.Username);

            // 分页
            var totalCount = await _unitOfWork.Users.CountAsync(query, cancellationToken);
            var users = await _unitOfWork.Users.GetPagedAsync(query, request.Page, request.PageSize, cancellationToken);

            var userProfiles = users.Select(user => new UserProfileDto
            {
                Id = user.Id,
                CustomUserId = user.CustomUserId,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                AvatarUrl = user.AvatarUrl,
                Bio = user.Bio,
                UserStatus = user.UserStatus.ToString(),
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            }).ToList();

            var pagedResult = new PagedResult<UserProfileDto>
            {
                Items = userProfiles,
                Pagination = new PaginationInfo
                {
                    CurrentPage = request.Page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize),
                    TotalItems = totalCount,
                    ItemsPerPage = request.PageSize
                }
            };

            return ApiResponse<PagedResult<UserProfileDto>>.SuccessResult(pagedResult, "搜索用户成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索用户失败: Keyword={Keyword}", request.Keyword);
            return ApiResponse<PagedResult<UserProfileDto>>.ErrorResult("搜索用户失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 上传头像
    /// </summary>
    public async Task<ApiResponse<string>> UploadAvatarAsync(int userId, Stream avatarFile, string fileName, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<string>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // TODO: 实现文件上传逻辑
            // 这里暂时返回一个模拟的URL
            var avatarUrl = $"/uploads/avatars/{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}_{fileName}";
            
            user.AvatarUrl = avatarUrl;
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户头像上传成功: UserId={UserId}, AvatarUrl={AvatarUrl}", userId, avatarUrl);
            return ApiResponse<string>.SuccessResult(avatarUrl, "头像上传成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "上传头像失败: UserId={UserId}", userId);
            return ApiResponse<string>.ErrorResult("上传头像失败", ErrorCodes.SERVER_001);
        }
    }

    public async Task<ApiResponse<UserQRCodeDto>> GetUserQRCodeAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("获取用户二维码: UserId={UserId}", userId);

            // 获取用户信息
            var user = await _unitOfWork.Users.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return ApiResponse<UserQRCodeDto>.ErrorResult("用户不存在", ErrorCodes.USER_001);
            }

            // 检查是否有有效的二维码
            var existingQRCodes = await _unitOfWork.UserQRCodes.FindAsync(
                q => q.UserId == userId && q.IsActive && q.ExpiresAt > DateTime.UtcNow,
                cancellationToken);

            var existingQRCode = existingQRCodes.FirstOrDefault();
            if (existingQRCode != null)
            {
                // 返回现有的二维码
                var existingDto = new UserQRCodeDto
                {
                    QRCodeData = existingQRCode.QRCodeData,
                    QRCodeUrl = existingQRCode.QRCodeUrl ?? string.Empty,
                    ExpiresAt = existingQRCode.ExpiresAt,
                    User = new UserProfileDto
                    {
                        Id = user.Id,
                        Username = user.Username,
                        CustomUserId = user.CustomUserId,
                        Email = user.Email,
                        Phone = user.Phone,
                        AvatarUrl = user.AvatarUrl,
                        DisplayName = user.DisplayName
                    }
                };

                return ApiResponse<UserQRCodeDto>.SuccessResult(existingDto, "获取二维码成功");
            }

            // 生成新的二维码
            var qrCodeData = GenerateQRCodeData(user);
            var qrCodeBase64 = _qrCodeService.GenerateQRCodeBase64(qrCodeData);
            var expiresAt = DateTime.UtcNow.AddDays(30); // 30天有效期

            // 保存二维码记录
            var userQRCode = new UserQRCode
            {
                UserId = userId,
                QRCodeData = qrCodeData,
                QRCodeUrl = $"data:image/png;base64,{qrCodeBase64}",
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = expiresAt,
                IsActive = true
            };

            await _unitOfWork.UserQRCodes.AddAsync(userQRCode, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var result = new UserQRCodeDto
            {
                QRCodeData = qrCodeData,
                QRCodeUrl = userQRCode.QRCodeUrl,
                ExpiresAt = expiresAt,
                User = new UserProfileDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    CustomUserId = user.CustomUserId,
                    Email = user.Email,
                    Phone = user.Phone,
                    AvatarUrl = user.AvatarUrl,
                    DisplayName = user.DisplayName
                }
            };

            _logger.LogInformation("用户二维码生成成功: UserId={UserId}", userId);
            return ApiResponse<UserQRCodeDto>.SuccessResult(result, "二维码生成成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户二维码失败: UserId={UserId}", userId);
            return ApiResponse<UserQRCodeDto>.ErrorResult("获取二维码失败", ErrorCodes.SERVER_001);
        }
    }

    public async Task<ApiResponse<List<UserProfileDto>>> MatchContactsAsync(ContactsMatchRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("匹配联系人: 联系人数量={Count}", request.PhoneNumbers.Count);

            // 验证手机号格式并去重
            var validPhoneNumbers = request.PhoneNumbers
                .Where(phone => !string.IsNullOrWhiteSpace(phone))
                .Select(phone => phone.Trim())
                .Distinct()
                .ToList();

            if (!validPhoneNumbers.Any())
            {
                return ApiResponse<List<UserProfileDto>>.SuccessResult(new List<UserProfileDto>(), "没有有效的手机号");
            }

            // 查找匹配的用户
            var matchedUsers = await _unitOfWork.Users.FindAsync(
                u => validPhoneNumbers.Contains(u.Phone) && u.UserStatus == UserStatus.Active,
                cancellationToken);

            var result = matchedUsers.Select(user => new UserProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                CustomUserId = user.CustomUserId,
                Email = user.Email,
                Phone = user.Phone,
                AvatarUrl = user.AvatarUrl,
                DisplayName = user.DisplayName,
                CreatedAt = user.CreatedAt
            }).ToList();

            _logger.LogInformation("联系人匹配完成: 输入={InputCount}, 匹配={MatchedCount}",
                validPhoneNumbers.Count, result.Count);

            return ApiResponse<List<UserProfileDto>>.SuccessResult(result, $"找到 {result.Count} 个匹配的联系人");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "匹配联系人失败");
            return ApiResponse<List<UserProfileDto>>.ErrorResult("匹配联系人失败", ErrorCodes.SERVER_001);
        }
    }

    private string GenerateQRCodeData(Core.Entities.AppUser user)
    {
        var qrData = new
        {
            Type = "InfoCardUser",
            UserId = user.Id,
            CustomUserId = user.CustomUserId,
            Username = user.Username,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
            ExpiresAt = DateTimeOffset.UtcNow.AddDays(30).ToUnixTimeSeconds()
        };

        return JsonSerializer.Serialize(qrData);
    }
}
