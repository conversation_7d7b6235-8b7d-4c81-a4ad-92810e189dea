using InfoCardSystem.Core.DTOs.Auth;
using InfoCardSystem.Core.DTOs.Common;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    Task<ApiResponse<AuthResponse>> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    Task<ApiResponse<AuthResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新的认证响应</returns>
    Task<ApiResponse<AuthResponse>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证用户名是否可用
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsUsernameAvailableAsync(string username, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证邮箱是否可用
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsEmailAvailableAsync(string email, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证自定义用户ID是否可用
    /// </summary>
    /// <param name="customUserId">自定义用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsCustomUserIdAvailableAsync(string customUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 验证手机号是否可用
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    Task<bool> IsPhoneAvailableAsync(string phone, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送忘记密码邮件/短信
    /// </summary>
    /// <param name="request">忘记密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request, CancellationToken cancellationToken = default);
}
