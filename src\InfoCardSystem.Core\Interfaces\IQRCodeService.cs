namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 二维码服务接口
/// </summary>
public interface IQRCodeService
{
    /// <summary>
    /// 生成二维码
    /// </summary>
    /// <param name="data">二维码数据</param>
    /// <param name="size">二维码大小</param>
    /// <returns>二维码图片字节数组</returns>
    byte[] GenerateQRCode(string data, int size = 256);

    /// <summary>
    /// 生成二维码Base64字符串
    /// </summary>
    /// <param name="data">二维码数据</param>
    /// <param name="size">二维码大小</param>
    /// <returns>二维码Base64字符串</returns>
    string GenerateQRCodeBase64(string data, int size = 256);

    /// <summary>
    /// 解析二维码数据
    /// </summary>
    /// <param name="qrCodeData">二维码数据</param>
    /// <returns>解析结果</returns>
    QRCodeParseResult ParseQRCodeData(string qrCodeData);

    /// <summary>
    /// 验证二维码数据
    /// </summary>
    /// <param name="qrCodeData">二维码数据</param>
    /// <returns>是否有效</returns>
    bool ValidateQRCodeData(string qrCodeData);
}

/// <summary>
/// 二维码解析结果
/// </summary>
public class QRCodeParseResult
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// 用户自定义ID
    /// </summary>
    public string? CustomUserId { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
