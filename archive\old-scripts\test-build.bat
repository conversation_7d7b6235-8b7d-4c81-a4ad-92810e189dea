@echo off
echo ========================================
echo InfoCard Build Test
echo ========================================
echo.

echo Cleaning solution...
dotnet clean --verbosity quiet
echo.

echo Restoring packages...
dotnet restore --verbosity quiet
echo.

echo Building API project...
dotnet build src/InfoCardSystem.API/InfoCardSystem.API.csproj -c Release --verbosity minimal --no-restore
if %errorLevel% neq 0 (
    echo API build failed
    pause
    exit /b 1
) else (
    echo API build successful
)
echo.

echo Building Web project...
dotnet build src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -c Release --verbosity minimal --no-restore
if %errorLevel% neq 0 (
    echo Web build failed
    pause
    exit /b 1
) else (
    echo Web build successful
)
echo.

echo ========================================
echo Build Test Complete
echo ========================================
echo.
echo All projects built successfully!
echo You can now run publish-to-webs-fixed.bat to publish to C:\Webs
echo.

pause
