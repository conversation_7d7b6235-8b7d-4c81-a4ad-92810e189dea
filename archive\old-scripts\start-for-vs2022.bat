@echo off
echo ========================================
echo InfoCard VS2022 Development Setup
echo ========================================
echo.

echo Checking MySQL service...
sc query mysql80 | findstr "RUNNING" >nul
if %errorLevel% neq 0 (
    echo MySQL service not running
    echo Starting MySQL service...
    net start mysql80
    if %errorLevel% neq 0 (
        echo Failed to start MySQL service, please start manually
        pause
        exit /b 1
    ) else (
        echo MySQL service started
    )
) else (
    echo MySQL service is running
)
echo.

echo Checking port availability...
netstat -ano | findstr ":8001" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo Port 8001 is in use, stopping existing API service...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8001"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)

netstat -ano | findstr ":8002" | findstr "LISTENING" >nul
if %errorLevel% equ 0 (
    echo Port 8002 is in use, stopping existing Web service...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8002"') do (
        taskkill /PID %%a /F >nul 2>&1
    )
)
echo.

echo Building solution...
dotnet build --verbosity quiet
if %errorLevel% neq 0 (
    echo Build failed, please check for errors
    pause
    exit /b 1
) else (
    echo Build successful
)
echo.

echo Starting API service in background...
start "InfoCard API" /MIN cmd /c "cd src\InfoCardSystem.API && dotnet run --urls https://localhost:8001;http://localhost:8000"
echo API service starting on https://localhost:8001
echo.

echo Waiting for API to start (10 seconds)...
timeout /t 10 /nobreak >nul
echo.

echo Testing API connection...
curl -k https://localhost:8001/health -s -o nul -w "API Health Check: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo Warning: API may not be fully started yet
) else (
    echo API is responding
)
echo.

echo ========================================
echo VS2022 Development Environment Ready
echo ========================================
echo.
echo API Service: https://localhost:8001
echo API Swagger: https://localhost:8001/swagger
echo.
echo You can now:
echo 1. Open InfoCardSystem.sln in Visual Studio 2022
echo 2. Set InfoCardSystem.Web as startup project
echo 3. Press F5 to debug the Web application
echo.
echo The API is already running in the background.
echo Web will start on https://localhost:8002 when you debug.
echo.
echo To stop the API service, close this window or run:
echo   stop-test-env.bat
echo.

echo Open Visual Studio 2022 now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Opening Visual Studio 2022...
    start "" "InfoCardSystem.sln"
)

echo.
echo Press any key to keep this window open (API will keep running)
echo Or close this window to stop the API service
pause >nul
