using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 用户资料DTO
/// </summary>
public class UserProfileDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 自定义用户ID
    /// </summary>
    public string CustomUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    public string? Bio { get; set; }
    
    /// <summary>
    /// 用户状态
    /// </summary>
    public string UserStatus { get; set; } = string.Empty;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 更新用户资料请求
/// </summary>
public class UpdateUserProfileDto
{
    /// <summary>
    /// 显示名称
    /// </summary>
    [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
    public string? DisplayName { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    public string? Email { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    [StringLength(1000, ErrorMessage = "个人简介长度不能超过1000个字符")]
    public string? Bio { get; set; }
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// 修改密码请求
/// </summary>
public class ChangePasswordDto
{
    /// <summary>
    /// 当前密码
    /// </summary>
    [Required(ErrorMessage = "当前密码不能为空")]
    public string CurrentPassword { get; set; } = string.Empty;
    
    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = string.Empty;
}

/// <summary>
/// 用户搜索结果
/// </summary>
public class UserSearchResultDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 手机号（部分显示）
    /// </summary>
    public string? MaskedPhoneNumber { get; set; }
    
    /// <summary>
    /// 是否已是好友
    /// </summary>
    public bool IsFriend { get; set; }
}
