@echo off
title InfoCard 开发环境启动器
color 0A

echo ========================================
echo   InfoCard 信息卡片分享系统
echo   开发环境快速启动器
echo ========================================
echo.

echo [INFO] 正在启动开发环境...
echo.

echo ========================================
echo 步骤 1: 检查MySQL服务
echo ========================================
sc query MySQL80 | findstr "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✅ MySQL服务已运行
) else (
    echo ⚠️  MySQL服务未运行，正在启动...
    net start MySQL80 >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ MySQL服务启动成功
    ) else (
        echo ❌ MySQL服务启动失败，请手动启动
    )
)
echo.

echo ========================================
echo 步骤 2: 清理端口占用
echo ========================================
echo 检查端口占用情况...
netstat -an | findstr :5000 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ⚠️  端口5000被占用，正在清理...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000 ^| findstr LISTENING') do taskkill /PID %%a /F >nul 2>&1
)

netstat -an | findstr :7000 | findstr LISTENING >nul
if %errorLevel% equ 0 (
    echo ⚠️  端口7000被占用，正在清理...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :7000 ^| findstr LISTENING') do taskkill /PID %%a /F >nul 2>&1
)
echo ✅ 端口清理完成
echo.

echo ========================================
echo 步骤 3: 构建项目
echo ========================================
echo 正在构建解决方案...
dotnet build InfoCardSystem.VS2022.sln -c Debug --verbosity quiet
if %errorLevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
) else (
    echo ✅ 项目构建成功
)
echo.

echo ========================================
echo 步骤 4: 启动API服务
echo ========================================
echo 正在后台启动API服务...
start "InfoCard API" /MIN dotnet run --project src/InfoCardSystem.API/InfoCardSystem.API.csproj --urls "http://localhost:5000"
echo ✅ API服务启动中... (端口: 5000)
echo.

echo 等待API服务完全启动...
timeout /t 10 /nobreak >nul

echo ========================================
echo 步骤 5: 启动Web应用
echo ========================================
echo 正在后台启动Web应用...
start "InfoCard Web" /MIN dotnet run --project src/InfoCardSystem.Web/InfoCardSystem.Web.csproj --urls "http://localhost:7000"
echo ✅ Web应用启动中... (端口: 7000)
echo.

echo 等待Web应用完全启动...
timeout /t 8 /nobreak >nul

echo ========================================
echo 步骤 6: 验证服务状态
echo ========================================
echo 检查API服务...
curl -s http://localhost:5000/health >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ API服务运行正常
) else (
    echo ⚠️  API服务可能还在启动中
)

echo 检查Web应用...
curl -s -I http://localhost:7000 >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Web应用运行正常
) else (
    echo ⚠️  Web应用可能还在启动中
)
echo.

echo ========================================
echo 🎉 启动完成！
echo ========================================
echo.
echo 📱 应用访问地址:
echo    🌐 Web应用:     http://localhost:7000
echo    🔧 API文档:     http://localhost:5000/swagger
echo    💚 健康检查:    http://localhost:5000/health
echo.
echo 🛠️  开发工具:
echo    📝 在VS2022中打开: InfoCardSystem.VS2022.sln
echo    🔍 查看日志: 检查启动的命令窗口
echo.
echo 📋 常用操作:
echo    1. 按任意键打开Web应用
echo    2. 输入 's' 打开Swagger文档
echo    3. 输入 'v' 打开VS2022
echo    4. 输入 'q' 退出
echo.

set /p choice="请选择操作 (1/s/v/q): "

if /i "%choice%"=="1" (
    echo 正在打开Web应用...
    start http://localhost:7000
) else if /i "%choice%"=="s" (
    echo 正在打开API文档...
    start http://localhost:5000/swagger
) else if /i "%choice%"=="v" (
    echo 正在打开VS2022...
    start InfoCardSystem.VS2022.sln
) else if /i "%choice%"=="q" (
    echo 退出启动器...
    exit /b 0
) else (
    echo 正在打开Web应用...
    start http://localhost:7000
)

echo.
echo 🎯 开发环境已就绪！
echo 💡 提示: 关闭此窗口不会停止服务，服务将继续在后台运行
echo.
pause
