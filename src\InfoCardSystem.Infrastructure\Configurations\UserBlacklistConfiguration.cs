using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户黑名单实体配置
/// </summary>
public class UserBlacklistConfiguration : IEntityTypeConfiguration<UserBlacklist>
{
    public void Configure(EntityTypeBuilder<UserBlacklist> builder)
    {
        // 表名
        builder.ToTable("user_blacklist");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.BlockedUserId)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => new { x.UserId, x.BlockedUserId })
            .IsUnique()
            .HasDatabaseName("idx_blacklist_user_blocked");
            
        builder.HasIndex(x => x.BlockedUserId)
            .HasDatabaseName("idx_blacklist_blocked_user");
        
        // 关系配置已在AppUserConfiguration中定义
    }
}
