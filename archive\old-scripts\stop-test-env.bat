@echo off
echo ========================================
echo InfoCard Test Environment Shutdown
echo ========================================
echo.

echo Stopping InfoCard services...
echo.

REM Stop API service
echo Stopping API service (port 8001)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8001"') do (
    taskkill /PID %%a /F >nul 2>&1
    if %errorLevel% equ 0 (
        echo API service stopped (PID: %%a)
    )
)

REM Stop Web app
echo Stopping Web app (port 8002)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8002"') do (
    taskkill /PID %%a /F >nul 2>&1
    if %errorLevel% equ 0 (
        echo Web app stopped (PID: %%a)
    )
)

REM Stop all InfoCardSystem processes
echo Stopping all InfoCardSystem processes...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1

echo.
echo Verifying service status...
netstat -ano | findstr ":8001" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo Port 8001 released
) else (
    echo Port 8001 still in use
)

netstat -ano | findstr ":8002" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo Port 8002 released
) else (
    echo Port 8002 still in use
)

echo.
echo ========================================
echo Test Environment Stopped
echo ========================================
echo.

pause
