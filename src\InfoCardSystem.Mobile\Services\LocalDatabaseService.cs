using Microsoft.Extensions.Logging;
using SQLite;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 本地数据库服务实现
/// </summary>
public class LocalDatabaseService : ILocalDatabaseService
{
    private readonly ILogger<LocalDatabaseService> _logger;
    private SQLiteAsyncConnection? _database;
    private readonly string _databasePath;

    public LocalDatabaseService(ILogger<LocalDatabaseService> logger)
    {
        _logger = logger;
        _databasePath = Path.Combine(FileSystem.AppDataDirectory, "InfoCard.db3");
    }

    public async Task InitializeAsync()
    {
        try
        {
            if (_database != null)
                return;

            _logger.LogInformation("初始化本地数据库: {Path}", _databasePath);

            _database = new SQLiteAsyncConnection(_databasePath);

            // 创建表结构
            // await _database.CreateTableAsync<LocalUser>();
            // await _database.CreateTableAsync<LocalInfoCard>();
            // await _database.CreateTableAsync<LocalFriend>();

            _logger.LogInformation("本地数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化本地数据库失败");
            throw;
        }
    }

    public async Task ClearAllDataAsync()
    {
        try
        {
            if (_database == null)
                return;

            _logger.LogInformation("清除所有本地数据");

            // await _database.DeleteAllAsync<LocalUser>();
            // await _database.DeleteAllAsync<LocalInfoCard>();
            // await _database.DeleteAllAsync<LocalFriend>();

            _logger.LogInformation("本地数据清除完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除本地数据失败");
            throw;
        }
    }

    public string GetDatabasePath()
    {
        return _databasePath;
    }
}
