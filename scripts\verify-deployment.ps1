# InfoCard System - 部署验证脚本

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "InfoCard System - 部署验证" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# 检查文件部署
Write-Host "🔍 检查文件部署..." -ForegroundColor Yellow

$apiPath = "C:\Webs\ICAPI"
$webPath = "C:\Webs\ICWeb"

if (Test-Path "$apiPath\InfoCardSystem.API.dll") {
    Write-Host "✅ API文件已部署到: $apiPath" -ForegroundColor Green
} else {
    Write-Host "❌ API文件未找到: $apiPath" -ForegroundColor Red
}

if (Test-Path "$webPath\InfoCardSystem.Web.dll") {
    Write-Host "✅ Web文件已部署到: $webPath" -ForegroundColor Green
} else {
    Write-Host "❌ Web文件未找到: $webPath" -ForegroundColor Red
}

Write-Host ""

# 检查配置文件
Write-Host "🔍 检查配置文件..." -ForegroundColor Yellow

if (Test-Path "$apiPath\appsettings.IIS.json") {
    Write-Host "✅ API配置文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ API配置文件缺失" -ForegroundColor Red
}

if (Test-Path "$webPath\appsettings.IIS.json") {
    Write-Host "✅ Web配置文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ Web配置文件缺失" -ForegroundColor Red
}

Write-Host ""

# 检查数据库连接
Write-Host "🔍 检查数据库连接..." -ForegroundColor Yellow

try {
    $mysqlTest = mysql -h localhost -P 3306 -u root -p123qwe!@#QWE -e "SELECT 1" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ MySQL数据库连接正常" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL数据库连接失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法测试数据库连接: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 检查端口占用
Write-Host "🔍 检查端口状态..." -ForegroundColor Yellow

$port8081 = netstat -an | Select-String ":8081"
$port8082 = netstat -an | Select-String ":8082"

if ($port8081) {
    Write-Host "✅ 端口8081正在使用中" -ForegroundColor Green
} else {
    Write-Host "⚠️  端口8081未被占用" -ForegroundColor Yellow
}

if ($port8082) {
    Write-Host "✅ 端口8082正在使用中" -ForegroundColor Green
} else {
    Write-Host "⚠️  端口8082未被占用" -ForegroundColor Yellow
}

Write-Host ""

# 测试API连接
Write-Host "🔍 测试API连接..." -ForegroundColor Yellow

try {
    $apiResponse = Invoke-WebRequest -Uri "http://localhost:8081/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ API健康检查成功 (状态码: $($apiResponse.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ API健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试Web连接
Write-Host "🔍 测试Web应用连接..." -ForegroundColor Yellow

try {
    $webResponse = Invoke-WebRequest -Uri "http://localhost:8082/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Web应用访问成功 (状态码: $($webResponse.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Web应用访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "验证完成" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🌐 访问地址:" -ForegroundColor Cyan
Write-Host "  • API健康检查: http://localhost:8081/health" -ForegroundColor White
Write-Host "  • API文档: http://localhost:8081/swagger" -ForegroundColor White
Write-Host "  • Web应用: http://localhost:8082/" -ForegroundColor White
Write-Host ""

Write-Host "📋 下一步操作:" -ForegroundColor Cyan
Write-Host "  1. 如果服务未运行，请使用 scripts/start-services.ps1 启动" -ForegroundColor White
Write-Host "  2. 如果需要配置IIS，请参考 docs/deployment/IIS-Configuration-Guide.md" -ForegroundColor White
Write-Host "  3. 检查防火墙设置，确保端口8081和8082可访问" -ForegroundColor White
Write-Host ""

Read-Host "按Enter键退出"
