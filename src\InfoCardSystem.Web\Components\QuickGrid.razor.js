export function initializeQuickGrid(dotNetHelper, elementId) {
    const gridElement = document.getElementById(elementId);
    if (!gridElement) {
        console.error(`QuickGrid element with ID '${elementId}' not found`);
        return;
    }

    // 初始化网格逻辑
    console.log(`QuickGrid initialized for element: ${elementId}`);
    
    // 示例：添加事件监听
    gridElement.addEventListener('click', () => {
        dotNetHelper.invokeMethodAsync('HandleGridClick');
    });
}

export function dispose(elementId) {
    const gridElement = document.getElementById(elementId);
    if (gridElement) {
        // 清理逻辑
        gridElement.removeEventListener('click', null);
        console.log(`QuickGrid disposed for element: ${elementId}`);
    }
}
