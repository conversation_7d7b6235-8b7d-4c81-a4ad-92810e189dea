using InfoCardSystem.Mobile.Services;

namespace InfoCardSystem.Mobile;

public partial class App : Application
{
    private readonly IAuthenticationService? _authService;

    public App(IAuthenticationService authService)
    {
        try
        {
            InitializeComponent();
            _authService = authService;

            MainPage = new AppShell();

            // 导航到启动页面，让启动页面处理认证检查
            Dispatcher.Dispatch(async () =>
            {
                try
                {
                    await Shell.Current.GoToAsync("//splash");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Navigation to splash failed: {ex}");
                    // 如果导航失败，直接显示登录页
                    await Shell.Current.GoToAsync("//login");
                }
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"App constructor error: {ex}");
            // 创建一个简单的错误页面
            MainPage = new ContentPage
            {
                Content = new Label
                {
                    Text = "应用启动失败，请重试",
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                }
            };
        }
    }

    private async Task CheckAuthenticationStatusSafe()
    {
        try
        {
            // 等待Shell完全初始化
            await Task.Delay(100);

            if (Shell.Current == null)
            {
                System.Diagnostics.Debug.WriteLine("Shell.Current is null, retrying...");
                await Task.Delay(500);
                if (Shell.Current == null)
                {
                    System.Diagnostics.Debug.WriteLine("Shell.Current still null, skipping navigation");
                    return;
                }
            }

            var isAuthenticated = await _authService.IsAuthenticatedAsync();

            if (isAuthenticated)
            {
                // 用户已登录，导航到主页
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                // 用户未登录，导航到登录页
                await Shell.Current.GoToAsync("//login");
            }
        }
        catch (Exception ex)
        {
            // 发生错误时，默认导航到登录页
            System.Diagnostics.Debug.WriteLine($"检查认证状态失败: {ex}");
            try
            {
                if (Shell.Current != null)
                {
                    await Shell.Current.GoToAsync("//login");
                }
            }
            catch (Exception navEx)
            {
                System.Diagnostics.Debug.WriteLine($"导航失败: {navEx}");
            }
        }
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        var window = base.CreateWindow(activationState);
        
        // 设置窗口属性
        window.Title = "InfoCard";
        
        return window;
    }
}
