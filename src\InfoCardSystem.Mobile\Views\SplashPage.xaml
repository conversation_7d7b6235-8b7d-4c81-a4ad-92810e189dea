<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.SplashPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="InfoCard"
             BackgroundColor="#1976D2">
    
    <Grid>
        <StackLayout VerticalOptions="Center" HorizontalOptions="Center">
            
            <!-- Logo -->
            <Image Source="logo.png" 
                   WidthRequest="120" 
                   HeightRequest="120"
                   Margin="0,0,0,30" />
            
            <!-- App Name -->
            <Label Text="InfoCard" 
                   FontSize="32" 
                   FontAttributes="Bold"
                   TextColor="White"
                   HorizontalOptions="Center"
                   Margin="0,0,0,10" />
            
            <!-- Subtitle -->
            <Label Text="信息卡片分享平台" 
                   FontSize="16" 
                   TextColor="White"
                   HorizontalOptions="Center"
                   Opacity="0.8"
                   Margin="0,0,0,50" />
            
            <!-- Loading Indicator -->
            <ActivityIndicator IsRunning="True" 
                             Color="White"
                             WidthRequest="40"
                             HeightRequest="40"
                             Margin="0,0,0,20" />
            
            <!-- Loading Text -->
            <Label Text="正在启动..." 
                   FontSize="14" 
                   TextColor="White"
                   HorizontalOptions="Center"
                   Opacity="0.7" />
            
        </StackLayout>
    </Grid>
    
</ContentPage>
