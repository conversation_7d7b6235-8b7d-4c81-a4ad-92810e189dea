using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using FriendRequestModel = InfoCardSystem.Mobile.Models.FriendRequest;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 添加好友页面ViewModel
/// </summary>
public partial class AddFriendViewModel : BaseViewModel
{
    private readonly IInfoCardApiClient _apiClient;

    public AddFriendViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IInfoCardApiClient apiClient,
        ILogger<AddFriendViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _apiClient = apiClient;
        Title = "添加好友";
        
        SearchResults = new ObservableCollection<UserInfo>();
        FriendRequests = new ObservableCollection<FriendRequestModel>();
        
        // 初始化加载好友请求
        _ = LoadFriendRequestsAsync();
    }

    [ObservableProperty]
    private string searchQuery = string.Empty;

    [ObservableProperty]
    private ObservableCollection<UserInfo> searchResults;

    [ObservableProperty]
    private ObservableCollection<FriendRequestModel> friendRequests;

    [ObservableProperty]
    private bool isNotBusy = true;

    /// <summary>
    /// 重写IsBusy状态变化处理
    /// </summary>
    /// <param name="value">新的IsBusy值</param>
    protected override void OnIsBusyChangedCore(bool value)
    {
        IsNotBusy = !value;
    }

    /// <summary>
    /// 扫描二维码命令
    /// </summary>
    [RelayCommand]
    private async Task ScanQRCodeAsync()
    {
        await NavigateToAsync("qrscanner");
    }

    /// <summary>
    /// 我的二维码命令
    /// </summary>
    [RelayCommand]
    private async Task MyQRCodeAsync()
    {
        await NavigateToAsync("myqrcode");
    }

    /// <summary>
    /// 手机联系人命令
    /// </summary>
    [RelayCommand]
    private async Task ContactsAsync()
    {
        await NavigateToAsync("contactsmatch");
    }

    /// <summary>
    /// 搜索用户命令
    /// </summary>
    [RelayCommand]
    private async Task SearchUsersAsync()
    {
        await NavigateToAsync("searchusers");
    }

    /// <summary>
    /// 搜索命令
    /// </summary>
    [RelayCommand]
    private async Task SearchAsync()
    {
        if (string.IsNullOrWhiteSpace(SearchQuery))
        {
            SearchResults.Clear();
            return;
        }

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("搜索用户: {Query}", SearchQuery);

            // 模拟搜索结果
            var mockResults = GenerateMockSearchResults();
            
            SearchResults.Clear();
            foreach (var user in mockResults)
            {
                SearchResults.Add(user);
            }

            _logger.LogInformation("搜索完成: 找到 {Count} 个用户", SearchResults.Count);
        }, "搜索用户");
    }

    /// <summary>
    /// 添加好友命令
    /// </summary>
    [RelayCommand]
    private async Task AddFriendAsync(UserInfo user)
    {
        if (user == null) return;

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("发送好友请求: {UserId}", user.Id);

            var confirmed = await ShowConfirmAsync("添加好友", $"确定要添加 {user.DisplayName ?? user.Username} 为好友吗？");
            if (!confirmed) return;

            // 模拟发送好友请求
            await Task.Delay(1000);

            await ShowSuccessAsync("好友请求已发送");
            
            // 从搜索结果中移除
            SearchResults.Remove(user);

            _logger.LogInformation("好友请求发送成功: {UserId}", user.Id);
        }, "添加好友", false);
    }

    /// <summary>
    /// 接受好友请求命令
    /// </summary>
    [RelayCommand]
    private async Task AcceptFriendRequestAsync(FriendRequestModel request)
    {
        if (request == null) return;

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("接受好友请求: {RequestId}", request.Id);

            // 模拟接受好友请求
            await Task.Delay(500);

            await ShowSuccessAsync($"已添加 {request.Requester.DisplayName ?? request.Requester.Username} 为好友");
            
            // 从请求列表中移除
            FriendRequests.Remove(request);

            _logger.LogInformation("好友请求接受成功: {RequestId}", request.Id);
        }, "接受好友请求", false);
    }

    /// <summary>
    /// 拒绝好友请求命令
    /// </summary>
    [RelayCommand]
    private async Task RejectFriendRequestAsync(FriendRequestModel request)
    {
        if (request == null) return;

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogDebug("拒绝好友请求: {RequestId}", request.Id);

            var confirmed = await ShowConfirmAsync("拒绝好友请求", $"确定要拒绝 {request.Requester.DisplayName ?? request.Requester.Username} 的好友请求吗？");
            if (!confirmed) return;

            // 模拟拒绝好友请求
            await Task.Delay(500);

            await ShowSuccessAsync("已拒绝好友请求");
            
            // 从请求列表中移除
            FriendRequests.Remove(request);

            _logger.LogInformation("好友请求拒绝成功: {RequestId}", request.Id);
        }, "拒绝好友请求", false);
    }

    /// <summary>
    /// 查看推荐命令
    /// </summary>
    [RelayCommand]
    private async Task ViewRecommendationsAsync()
    {
        await ShowToastAsync("推荐功能即将推出");
    }

    /// <summary>
    /// 加载好友请求
    /// </summary>
    private async Task LoadFriendRequestsAsync()
    {
        try
        {
            _logger.LogDebug("加载好友请求");

            // 模拟好友请求数据
            var mockRequests = GenerateMockFriendRequests();
            
            FriendRequests.Clear();
            foreach (var request in mockRequests)
            {
                FriendRequests.Add(request);
            }

            _logger.LogInformation("好友请求加载完成: {Count} 个请求", FriendRequests.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载好友请求失败");
        }
    }

    /// <summary>
    /// 生成模拟搜索结果
    /// </summary>
    private List<UserInfo> GenerateMockSearchResults()
    {
        var results = new List<UserInfo>();
        
        if (SearchQuery.Length >= 2)
        {
            for (int i = 1; i <= 3; i++)
            {
                results.Add(new UserInfo
                {
                    Id = 100 + i,
                    Username = $"search_user_{i}",
                    DisplayName = $"搜索用户 {i}",
                    Email = $"search{i}@example.com",
                    AvatarUrl = "default_avatar.png",
                    CreatedAt = DateTime.Now.AddDays(-i * 10)
                });
            }
        }

        return results;
    }

    /// <summary>
    /// 生成模拟好友请求
    /// </summary>
    private List<FriendRequestModel> GenerateMockFriendRequests()
    {
        var requests = new List<FriendRequestModel>();

        for (int i = 1; i <= 2; i++)
        {
            requests.Add(new FriendRequestModel
            {
                Id = i,
                Requester = new UserInfo
                {
                    Id = 200 + i,
                    Username = $"requester_{i}",
                    DisplayName = $"请求者 {i}",
                    Email = $"requester{i}@example.com",
                    AvatarUrl = "default_avatar.png"
                },
                Message = $"我是通过搜索找到您的，希望能成为好友",
                CreatedAt = DateTime.Now.AddHours(-i * 2)
            });
        }

        return requests;
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        await LoadFriendRequestsAsync();
    }
}
