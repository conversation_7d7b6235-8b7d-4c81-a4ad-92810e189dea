namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 安全存储服务接口
/// </summary>
public interface ISecureStorageService
{
    /// <summary>
    /// 获取安全存储的值
    /// </summary>
    /// <param name="key">键</param>
    /// <returns>值</returns>
    Task<string?> GetAsync(string key);

    /// <summary>
    /// 设置安全存储的值
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    Task SetAsync(string key, string value);

    /// <summary>
    /// 移除安全存储的值
    /// </summary>
    /// <param name="key">键</param>
    Task<bool> RemoveAsync(string key);

    /// <summary>
    /// 检查键是否存在
    /// </summary>
    /// <param name="key">键</param>
    Task<bool> ContainsKeyAsync(string key);

    /// <summary>
    /// 清除所有安全存储的值
    /// </summary>
    Task RemoveAllAsync();
}
