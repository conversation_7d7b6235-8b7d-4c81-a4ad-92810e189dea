using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Friendship;
using InfoCardSystem.Core.DTOs.User;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 好友关系服务接口
/// </summary>
public interface IFriendshipService
{
    /// <summary>
    /// 发送好友请求
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="targetCustomUserId">目标用户自定义ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> SendFriendRequestAsync(int userId, string targetCustomUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 处理好友请求
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="friendshipId">好友关系ID</param>
    /// <param name="accept">是否接受</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> HandleFriendRequestAsync(int userId, int friendshipId, bool accept, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取好友列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>好友列表</returns>
    Task<ApiResponse<PagedResult<FriendshipDto>>> GetFriendsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取待处理的好友请求
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>好友请求列表</returns>
    Task<ApiResponse<List<FriendRequestDto>>> GetPendingFriendRequestsAsync(int userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新好友别名
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="friendshipId">好友关系ID</param>
    /// <param name="alias">新别名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UpdateFriendAliasAsync(int userId, int friendshipId, string? alias, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除好友
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="friendshipId">好友关系ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RemoveFriendAsync(int userId, int friendshipId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 添加到黑名单
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="blockedUserId">被拉黑用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> BlockUserAsync(int userId, int blockedUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 从黑名单移除
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="blockedUserId">被拉黑用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> UnblockUserAsync(int userId, int blockedUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取黑名单
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>黑名单用户列表</returns>
    Task<ApiResponse<List<UserProfileDto>>> GetBlockedUsersAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 扫码添加好友
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="request">扫码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ScanQRCodeAddFriendAsync(int userId, ScanQRCodeRequest request, CancellationToken cancellationToken = default);
}
