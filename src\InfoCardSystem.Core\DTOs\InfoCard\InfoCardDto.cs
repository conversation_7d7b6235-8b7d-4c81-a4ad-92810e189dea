using InfoCardSystem.Core.DTOs.User;
using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.InfoCard;

/// <summary>
/// 资讯卡DTO
/// </summary>
public class InfoCardDto
{
    /// <summary>
    /// 资讯卡ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 发布者信息
    /// </summary>
    public UserProfileDto Publisher { get; set; } = null!;
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 资讯卡类型
    /// </summary>
    public string InfoCardType { get; set; } = string.Empty;
    
    /// <summary>
    /// 可见性
    /// </summary>
    public string Visibility { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否允许转发
    /// </summary>
    public bool AllowForwarding { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 创建资讯卡请求
/// </summary>
public class CreateInfoCardRequest
{
    /// <summary>
    /// 标题
    /// </summary>
    [Required(ErrorMessage = "标题不能为空")]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "标题长度必须在1-200个字符之间")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 内容
    /// </summary>
    [Required(ErrorMessage = "内容不能为空")]
    [StringLength(5000, MinimumLength = 1, ErrorMessage = "内容长度必须在1-5000个字符之间")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 资讯卡类型
    /// </summary>
    [Range(1, 10, ErrorMessage = "资讯卡类型必须在1-10之间")]
    public int InfoCardType { get; set; }
    
    /// <summary>
    /// 可见性
    /// </summary>
    public int Visibility { get; set; }
    
    /// <summary>
    /// 是否允许转发
    /// </summary>
    public bool AllowForwarding { get; set; } = true;
    
    /// <summary>
    /// 过期时间（可选）
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
    
    /// <summary>
    /// 接收者用户ID列表（私人可见时必填）
    /// </summary>
    public List<int>? RecipientUserIds { get; set; }
    
    /// <summary>
    /// 接收者群组ID列表（群组可见时必填）
    /// </summary>
    public List<int>? RecipientGroupIds { get; set; }
}

/// <summary>
/// 更新资讯卡请求
/// </summary>
public class UpdateInfoCardRequest
{
    /// <summary>
    /// 标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 是否允许转发
    /// </summary>
    public bool? AllowForwarding { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// 转发资讯卡请求
/// </summary>
public class ForwardInfoCardRequest
{
    /// <summary>
    /// 接收者用户ID列表
    /// </summary>
    public List<int>? RecipientUserIds { get; set; }

    /// <summary>
    /// 接收者群组ID列表
    /// </summary>
    public List<int>? RecipientGroupIds { get; set; }
}

/// <summary>
/// 收藏操作请求
/// </summary>
public class FavoriteRequest
{
    /// <summary>
    /// 是否收藏
    /// </summary>
    public bool IsFavorite { get; set; }
}
