<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.RegisterPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:RegisterViewModel"
             Title="注册"
             Shell.NavBarIsVisible="False"
             BackgroundColor="{StaticResource Primary}">

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="24">
            
            <!-- 顶部Logo区域 -->
            <StackLayout Grid.Row="0" 
                        VerticalOptions="Center" 
                        HorizontalOptions="Center"
                        Margin="0,40,0,30">
                
                <!-- App Logo -->
                <Image Source="app_logo.png" 
                       WidthRequest="80" 
                       HeightRequest="80"
                       Margin="0,0,0,16" />
                
                <!-- 注册标题 -->
                <Label Text="创建账户" 
                       FontSize="28" 
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center" />
                
                <!-- Subtitle -->
                <Label Text="加入InfoCard，开始分享" 
                       FontSize="14" 
                       TextColor="White"
                       Opacity="0.8"
                       HorizontalOptions="Center"
                       Margin="0,8,0,0" />
                
            </StackLayout>

            <!-- 注册表单 -->
            <StackLayout Grid.Row="1" 
                        VerticalOptions="Center"
                        Spacing="16">

                <!-- 用户名输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="UsernameEntry"
                           Text="{Binding Username}"
                           Placeholder="用户名 (3-50个字符)"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 邮箱输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="EmailEntry"
                           Text="{Binding Email}"
                           Placeholder="邮箱地址"
                           Keyboard="Email"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 手机号输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="PhoneEntry"
                           Text="{Binding Phone}"
                           Placeholder="手机号"
                           Keyboard="Telephone"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 显示名称输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="DisplayNameEntry"
                           Text="{Binding DisplayName}"
                           Placeholder="显示名称 (可选)"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 密码输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="PasswordEntry"
                           Text="{Binding Password}"
                           Placeholder="密码 (至少6个字符)"
                           IsPassword="True"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 确认密码输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="ConfirmPasswordEntry"
                           Text="{Binding ConfirmPassword}"
                           Placeholder="确认密码"
                           IsPassword="True"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 服务条款同意 -->
                <StackLayout Orientation="Horizontal" 
                            Margin="8,16,8,0">
                    <CheckBox x:Name="AgreeTermsCheckBox"
                             IsChecked="{Binding AgreeToTerms}"
                             Color="White" />
                    <Label VerticalOptions="Center"
                           FontSize="14"
                           TextColor="White">
                        <Label.FormattedText>
                            <FormattedString>
                                <Span Text="我同意 " />
                                <Span Text="服务条款" 
                                      TextDecorations="Underline"
                                      FontAttributes="Bold" />
                                <Span Text=" 和 " />
                                <Span Text="隐私政策" 
                                      TextDecorations="Underline"
                                      FontAttributes="Bold" />
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </StackLayout>

                <!-- 注册按钮 -->
                <Button Text="创建账户"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       FontSize="18"
                       FontAttributes="Bold"
                       CornerRadius="12"
                       HeightRequest="50"
                       Margin="0,20,0,0"
                       Command="{Binding RegisterCommand}"
                       IsEnabled="{Binding IsNotBusy}" />

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  Color="White"
                                  WidthRequest="30"
                                  HeightRequest="30" />

                <!-- 错误消息 -->
                <Label Text="{Binding ErrorMessage}"
                      TextColor="Yellow"
                      FontSize="14"
                      HorizontalOptions="Center"
                      IsVisible="{Binding HasError}"
                      Margin="0,8,0,0" />

            </StackLayout>

            <!-- 底部登录链接 -->
            <StackLayout Grid.Row="2" 
                        VerticalOptions="End"
                        Margin="0,20,0,20">
                
                <!-- 分割线 -->
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center"
                            Margin="0,0,0,20">
                    <BoxView BackgroundColor="White" 
                            Opacity="0.3"
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                    <Label Text="或者" 
                           TextColor="White"
                           Opacity="0.8"
                           FontSize="14"
                           Margin="16,0" />
                    <BoxView BackgroundColor="White" 
                            Opacity="0.3"
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                </StackLayout>

                <!-- 快速注册方式 -->
                <Grid ColumnDefinitions="*,*" 
                      ColumnSpacing="16"
                      Margin="0,0,0,20">
                    <Button Grid.Column="0"
                           Text="📱 手机号注册"
                           BackgroundColor="Transparent"
                           BorderColor="White"
                           BorderWidth="1"
                           TextColor="White"
                           FontSize="14"
                           CornerRadius="8"
                           HeightRequest="44"
                           Command="{Binding PhoneRegisterCommand}" />
                    
                    <Button Grid.Column="1"
                           Text="📧 邮箱注册"
                           BackgroundColor="Transparent"
                           BorderColor="White"
                           BorderWidth="1"
                           TextColor="White"
                           FontSize="14"
                           CornerRadius="8"
                           HeightRequest="44"
                           Command="{Binding EmailRegisterCommand}" />
                </Grid>

                <!-- 登录链接 -->
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center">
                    <Label Text="已有账户?" 
                           TextColor="White"
                           Opacity="0.8"
                           FontSize="14" />
                    <Button Text="立即登录"
                           BackgroundColor="Transparent"
                           TextColor="White"
                           FontSize="14"
                           FontAttributes="Bold"
                           Padding="8,0"
                           Command="{Binding LoginCommand}" />
                </StackLayout>

            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
