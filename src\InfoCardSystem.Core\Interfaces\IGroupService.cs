using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Group;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 群组服务接口
/// </summary>
public interface IGroupService
{
    /// <summary>
    /// 创建群组
    /// </summary>
    /// <param name="userId">创建者用户ID</param>
    /// <param name="request">创建群组请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的群组信息</returns>
    Task<ApiResponse<GroupDto>> CreateGroupAsync(int userId, CreateGroupRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取用户的群组列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>群组列表</returns>
    Task<ApiResponse<PagedResult<GroupDto>>> GetUserGroupsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取群组详情
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>群组详情</returns>
    Task<ApiResponse<GroupDetailDto>> GetGroupDetailAsync(int userId, int groupId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新群组信息
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的群组信息</returns>
    Task<ApiResponse<GroupDto>> UpdateGroupAsync(int userId, int groupId, UpdateGroupRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除群组
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> DeleteGroupAsync(int userId, int groupId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 添加群组成员
    /// </summary>
    /// <param name="userId">当前用户ID（必须是群主）</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="request">添加成员请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> AddGroupMembersAsync(int userId, int groupId, AddGroupMembersRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取群组成员列表
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成员列表</returns>
    Task<ApiResponse<List<GroupMemberDto>>> GetGroupMembersAsync(int userId, int groupId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 移除群组成员
    /// </summary>
    /// <param name="userId">当前用户ID（必须是群主）</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="memberId">要移除的成员用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> RemoveGroupMemberAsync(int userId, int groupId, int memberId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 退出群组
    /// </summary>
    /// <param name="userId">当前用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> LeaveGroupAsync(int userId, int groupId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否为群组成员
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否为成员</returns>
    Task<bool> IsGroupMemberAsync(int userId, int groupId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查用户是否为群主
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="groupId">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否为群主</returns>
    Task<bool> IsGroupCreatorAsync(int userId, int groupId, CancellationToken cancellationToken = default);
}
