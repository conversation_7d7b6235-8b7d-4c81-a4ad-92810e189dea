@echo off
echo Creating simple IIS configuration without ASP.NET Core module...

echo Creating simple API web.config...
echo ^<?xml version="1.0" encoding="utf-8"?^> > "C:\Webs\ICAPI\web.config"
echo ^<configuration^> >> "C:\Webs\ICAPI\web.config"
echo   ^<system.webServer^> >> "C:\Webs\ICAPI\web.config"
echo     ^<defaultDocument^> >> "C:\Webs\ICAPI\web.config"
echo       ^<files^> >> "C:\Webs\ICAPI\web.config"
echo         ^<clear /^> >> "C:\Webs\ICAPI\web.config"
echo         ^<add value="index.html" /^> >> "C:\Webs\ICAPI\web.config"
echo       ^</files^> >> "C:\Webs\ICAPI\web.config"
echo     ^</defaultDocument^> >> "C:\Webs\ICAPI\web.config"
echo   ^</system.webServer^> >> "C:\Webs\ICAPI\web.config"
echo ^</configuration^> >> "C:\Webs\ICAPI\web.config"

echo Creating simple Web web.config...
echo ^<?xml version="1.0" encoding="utf-8"?^> > "C:\Webs\ICWeb\web.config"
echo ^<configuration^> >> "C:\Webs\ICWeb\web.config"
echo   ^<system.webServer^> >> "C:\Webs\ICWeb\web.config"
echo     ^<defaultDocument^> >> "C:\Webs\ICWeb\web.config"
echo       ^<files^> >> "C:\Webs\ICWeb\web.config"
echo         ^<clear /^> >> "C:\Webs\ICWeb\web.config"
echo         ^<add value="index.html" /^> >> "C:\Webs\ICWeb\web.config"
echo       ^</files^> >> "C:\Webs\ICWeb\web.config"
echo     ^</defaultDocument^> >> "C:\Webs\ICWeb\web.config"
echo   ^</system.webServer^> >> "C:\Webs\ICWeb\web.config"
echo ^</configuration^> >> "C:\Webs\ICWeb\web.config"

echo Simple IIS configuration created!
echo Now starting applications manually...
