@echo off
echo ========================================
echo InfoCard Publish to C:\Webs (Fixed)
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Checking directories...
if not exist "C:\Webs" (
    echo Creating C:\Webs directory...
    mkdir "C:\Webs"
)

if not exist "C:\Webs\ICAPI" (
    echo Creating C:\Webs\ICAPI directory...
    mkdir "C:\Webs\ICAPI"
) else (
    echo Cleaning C:\Webs\ICAPI directory...
    del /Q "C:\Webs\ICAPI\*.*" 2>nul
    for /d %%x in ("C:\Webs\ICAPI\*") do rd /s /q "%%x" 2>nul
)

if not exist "C:\Webs\ICWeb" (
    echo Creating C:\Webs\ICWeb directory...
    mkdir "C:\Webs\ICWeb"
) else (
    echo Cleaning C:\Webs\ICWeb directory...
    del /Q "C:\Webs\ICWeb\*.*" 2>nul
    for /d %%x in ("C:\Webs\ICWeb\*") do rd /s /q "%%x" 2>nul
)
echo.

echo Stopping any running services...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1
echo.

echo Cleaning previous builds...
dotnet clean --verbosity quiet
echo.

echo Restoring packages...
dotnet restore --verbosity quiet
echo.

echo Publishing API project...
dotnet publish src/InfoCardSystem.API/InfoCardSystem.API.csproj -c Release -o C:\Webs\ICAPI --verbosity minimal --no-restore
if %errorLevel% neq 0 (
    echo Failed to publish API project
    echo Check the error messages above for details
    pause
    exit /b 1
) else (
    echo API project published successfully to C:\Webs\ICAPI
)
echo.

echo Publishing Web project...
dotnet publish src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -c Release -o C:\Webs\ICWeb --verbosity minimal --no-restore
if %errorLevel% neq 0 (
    echo Failed to publish Web project
    echo Check the error messages above for details
    pause
    exit /b 1
) else (
    echo Web project published successfully to C:\Webs\ICWeb
)
echo.

echo Creating logs directories...
if not exist "C:\Webs\ICAPI\logs" mkdir "C:\Webs\ICAPI\logs"
if not exist "C:\Webs\ICWeb\logs" mkdir "C:\Webs\ICWeb\logs"

echo Setting directory permissions...
icacls "C:\Webs\ICAPI" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "C:\Webs\ICWeb" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1

echo.
echo ========================================
echo Publish Complete
echo ========================================
echo.
echo API published to: C:\Webs\ICAPI
echo Web published to: C:\Webs\ICWeb
echo.
echo Files in API directory:
dir "C:\Webs\ICAPI" /b | findstr /E ".exe .dll .json"
echo.
echo Files in Web directory:
dir "C:\Webs\ICWeb" /b | findstr /E ".exe .dll .json"
echo.
echo You can now:
echo 1. Run the applications directly from C:\Webs
echo 2. Use start-test-env.bat to start both services
echo 3. Use quick-test.bat to verify services are running
echo.
echo Note: Applications will run on:
echo - API: http://localhost:8001
echo - Web: http://localhost:8002
echo.

echo Would you like to start the applications now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo Starting applications...
    start "InfoCard API" cmd /c "cd C:\Webs\ICAPI && InfoCardSystem.API.exe"
    timeout /t 3 /nobreak >nul
    start "InfoCard Web" cmd /c "cd C:\Webs\ICWeb && InfoCardSystem.Web.exe"
    timeout /t 2 /nobreak >nul
    echo.
    echo Applications started. You can access:
    echo - API: http://localhost:8001/swagger
    echo - Web: http://localhost:8002
    echo.
    echo Open browser? (Y/N)
    set /p browser_choice=
    if /i "%browser_choice%"=="Y" (
        start "" "http://localhost:8002"
        timeout /t 2 /nobreak >nul
        start "" "http://localhost:8001/swagger"
    )
) else (
    echo You can start the applications later using start-test-env.bat
)

pause
