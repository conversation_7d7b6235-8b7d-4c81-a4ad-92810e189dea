@page "/demo"
@rendermode InteractiveServer

<PageTitle>InfoCard Web应用演示</PageTitle>

<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-id-card me-3"></i>InfoCard Web应用
                </h1>
                <p class="lead text-muted">基于Blazor Server的现代化资讯分享平台</p>
            </div>
        </div>
    </div>

    <!-- 功能特性展示 -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                    <h4 class="card-title">用户认证</h4>
                    <p class="card-text text-muted">
                        完整的用户注册、登录系统<br/>
                        JWT令牌认证<br/>
                        安全的密码管理
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-success me-2">✓ 登录页面</span>
                        <span class="badge bg-success me-2">✓ 注册页面</span>
                        <span class="badge bg-success">✓ 状态管理</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-id-card-alt fa-2x"></i>
                    </div>
                    <h4 class="card-title">资讯卡管理</h4>
                    <p class="card-text text-muted">
                        创建、编辑、分享资讯卡<br/>
                        支持图片和链接<br/>
                        收藏和转发功能
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-success me-2">✓ 卡片展示</span>
                        <span class="badge bg-success me-2">✓ 创建编辑</span>
                        <span class="badge bg-success">✓ 互动功能</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body text-center">
                    <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h4 class="card-title">社交功能</h4>
                    <p class="card-text text-muted">
                        好友管理系统<br/>
                        群组创建和管理<br/>
                        实时消息通知
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-success me-2">✓ 好友系统</span>
                        <span class="badge bg-success me-2">✓ 群组管理</span>
                        <span class="badge bg-warning">⚠ 待API连接</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 技术栈展示 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-code me-2"></i>技术栈
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <h5 class="text-primary">前端框架</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Blazor Server</li>
                                <li><i class="fas fa-check text-success me-2"></i>Bootstrap 5</li>
                                <li><i class="fas fa-check text-success me-2"></i>Font Awesome</li>
                            </ul>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 class="text-primary">状态管理</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Blazored.LocalStorage</li>
                                <li><i class="fas fa-check text-success me-2"></i>AuthStateService</li>
                                <li><i class="fas fa-check text-success me-2"></i>JWT认证</li>
                            </ul>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 class="text-primary">UI组件</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Blazored.Toast</li>
                                <li><i class="fas fa-check text-success me-2"></i>响应式设计</li>
                                <li><i class="fas fa-check text-success me-2"></i>现代化界面</li>
                            </ul>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 class="text-primary">后端集成</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>HTTP客户端</li>
                                <li><i class="fas fa-check text-success me-2"></i>API服务层</li>
                                <li><i class="fas fa-check text-success me-2"></i>错误处理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 界面预览 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-desktop me-2"></i>界面预览
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 bg-light">
                                <h5 class="text-center mb-3">登录页面</h5>
                                <div class="text-center">
                                    <a href="/login" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>查看登录页面
                                    </a>
                                </div>
                                <small class="text-muted d-block mt-2 text-center">
                                    现代化的登录界面，支持手机号登录
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3 bg-light">
                                <h5 class="text-center mb-3">注册页面</h5>
                                <div class="text-center">
                                    <a href="/register" class="btn btn-success">
                                        <i class="fas fa-user-plus me-2"></i>查看注册页面
                                    </a>
                                </div>
                                <small class="text-muted d-block mt-2 text-center">
                                    简洁的注册流程，表单验证完整
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 开发状态 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-tasks me-2"></i>开发状态
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-success">✅ 已完成</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    项目架构搭建
                                    <span class="badge bg-success rounded-pill">100%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    用户认证界面
                                    <span class="badge bg-success rounded-pill">100%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    API服务集成
                                    <span class="badge bg-success rounded-pill">100%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    响应式布局
                                    <span class="badge bg-success rounded-pill">100%</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">🚧 进行中</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    资讯卡管理页面
                                    <span class="badge bg-primary rounded-pill">80%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    好友管理功能
                                    <span class="badge bg-primary rounded-pill">60%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    群组管理功能
                                    <span class="badge bg-primary rounded-pill">40%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PWA支持
                                    <span class="badge bg-warning rounded-pill">计划中</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回首页按钮 -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="/" class="btn btn-lg btn-primary">
                <i class="fas fa-home me-2"></i>返回首页
            </a>
        </div>
    </div>
</div>

<style>
    .feature-icon {
        transition: transform 0.3s ease;
    }
    
    .feature-icon:hover {
        transform: scale(1.1);
    }
    
    .card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }
</style>
