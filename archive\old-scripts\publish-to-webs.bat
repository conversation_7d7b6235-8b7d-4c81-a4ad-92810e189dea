@echo off
echo ========================================
echo InfoCard Publish to C:\Webs
echo ========================================
echo.

echo Checking directories...
if not exist "C:\Webs" (
    echo Creating C:\Webs directory...
    mkdir "C:\Webs"
)

if not exist "C:\Webs\ICAPI" (
    echo Creating C:\Webs\ICAPI directory...
    mkdir "C:\Webs\ICAPI"
) else (
    echo C:\Webs\ICAPI already exists
)

if not exist "C:\Webs\ICWeb" (
    echo Creating C:\Webs\ICWeb directory...
    mkdir "C:\Webs\ICWeb"
) else (
    echo C:\Webs\ICWeb already exists
)
echo.

echo Stopping any running services...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1
echo.

echo Cleaning previous builds...
dotnet clean --verbosity quiet
echo.

echo Publishing API project...
dotnet publish src/InfoCardSystem.API/InfoCardSystem.API.csproj -c Release -o C:\Webs\ICAPI --verbosity minimal
if %errorLevel% neq 0 (
    echo Failed to publish API project
    pause
    exit /b 1
) else (
    echo API project published successfully to C:\Webs\ICAPI
)
echo.

echo Publishing Web project...
dotnet publish src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -c Release -o C:\Webs\ICWeb --verbosity minimal
if %errorLevel% neq 0 (
    echo Failed to publish Web project
    pause
    exit /b 1
) else (
    echo Web project published successfully to C:\Webs\ICWeb
)
echo.

echo Updating API configuration...
echo {                                                                > C:\Webs\ICAPI\appsettings.Production.json
echo   "ConnectionStrings": {                                        >> C:\Webs\ICAPI\appsettings.Production.json
echo     "DefaultConnection": "Server=localhost;Port=3306;Database=infinitycircle;Uid=root;Pwd=************;CharSet=utf8mb4;" >> C:\Webs\ICAPI\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICAPI\appsettings.Production.json
echo   "Jwt": {                                                      >> C:\Webs\ICAPI\appsettings.Production.json
echo     "Key": "InfoCardSystem_JWT_Secret_Key_2024_Very_Long_And_Secure_Key_For_Production_Use_IIS_Deployment", >> C:\Webs\ICAPI\appsettings.Production.json
echo     "Issuer": "InfoCardSystem",                                 >> C:\Webs\ICAPI\appsettings.Production.json
echo     "Audience": "InfoCardSystem.Client",                        >> C:\Webs\ICAPI\appsettings.Production.json
echo     "ExpirationMinutes": 1440                                   >> C:\Webs\ICAPI\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICAPI\appsettings.Production.json
echo   "App": {                                                      >> C:\Webs\ICAPI\appsettings.Production.json
echo     "BaseUrl": "http://localhost:8001"                          >> C:\Webs\ICAPI\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICAPI\appsettings.Production.json
echo   "Logging": {                                                  >> C:\Webs\ICAPI\appsettings.Production.json
echo     "LogLevel": {                                               >> C:\Webs\ICAPI\appsettings.Production.json
echo       "Default": "Information",                                 >> C:\Webs\ICAPI\appsettings.Production.json
echo       "Microsoft.AspNetCore": "Warning"                         >> C:\Webs\ICAPI\appsettings.Production.json
echo     }                                                           >> C:\Webs\ICAPI\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICAPI\appsettings.Production.json
echo   "AllowedHosts": "*",                                          >> C:\Webs\ICAPI\appsettings.Production.json
echo   "Urls": "http://localhost:8001"                               >> C:\Webs\ICAPI\appsettings.Production.json
echo }                                                               >> C:\Webs\ICAPI\appsettings.Production.json

echo Updating Web configuration...
echo {                                                                > C:\Webs\ICWeb\appsettings.Production.json
echo   "Logging": {                                                  >> C:\Webs\ICWeb\appsettings.Production.json
echo     "LogLevel": {                                               >> C:\Webs\ICWeb\appsettings.Production.json
echo       "Default": "Information",                                 >> C:\Webs\ICWeb\appsettings.Production.json
echo       "Microsoft.AspNetCore": "Warning"                         >> C:\Webs\ICWeb\appsettings.Production.json
echo     }                                                           >> C:\Webs\ICWeb\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICWeb\appsettings.Production.json
echo   "AllowedHosts": "*",                                          >> C:\Webs\ICWeb\appsettings.Production.json
echo   "ApiSettings": {                                              >> C:\Webs\ICWeb\appsettings.Production.json
echo     "BaseUrl": "http://localhost:8001/"                         >> C:\Webs\ICWeb\appsettings.Production.json
echo   },                                                            >> C:\Webs\ICWeb\appsettings.Production.json
echo   "Urls": "http://localhost:8002"                               >> C:\Webs\ICWeb\appsettings.Production.json
echo }                                                               >> C:\Webs\ICWeb\appsettings.Production.json

echo Creating logs directories...
if not exist "C:\Webs\ICAPI\logs" mkdir "C:\Webs\ICAPI\logs"
if not exist "C:\Webs\ICWeb\logs" mkdir "C:\Webs\ICWeb\logs"

echo.
echo ========================================
echo Publish Complete
echo ========================================
echo.
echo API published to: C:\Webs\ICAPI
echo Web published to: C:\Webs\ICWeb
echo.
echo You can now:
echo 1. Run the applications directly from C:\Webs
echo 2. Use start-test-env.bat to start both services
echo 3. Use quick-test.bat to verify services are running
echo.
echo Note: Applications will run on:
echo - API: http://localhost:8001
echo - Web: http://localhost:8002
echo.

echo Would you like to start the applications now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    call start-test-env.bat
) else (
    echo You can start the applications later using start-test-env.bat
)

pause
