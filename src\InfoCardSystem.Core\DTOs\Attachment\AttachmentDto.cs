using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.Attachment;

/// <summary>
/// 附件信息DTO
/// </summary>
public class AttachmentDto
{
    /// <summary>
    /// 附件ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始文件名
    /// </summary>
    public string OriginalFileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// 文件大小（格式化）
    /// </summary>
    public string FormattedFileSize { get; set; } = string.Empty;
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string MimeType { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;
    
    /// <summary>
    /// 上传者ID
    /// </summary>
    public int UploaderId { get; set; }
    
    /// <summary>
    /// 下载URL
    /// </summary>
    public string DownloadUrl { get; set; } = string.Empty;
    
    /// <summary>
    /// 预览URL（仅图片）
    /// </summary>
    public string? PreviewUrl { get; set; }
    
    /// <summary>
    /// 是否为图片
    /// </summary>
    public bool IsImage { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 文件上传响应DTO
/// </summary>
public class FileUploadResponse
{
    /// <summary>
    /// 附件信息
    /// </summary>
    public AttachmentDto Attachment { get; set; } = null!;
    
    /// <summary>
    /// 上传是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 批量文件上传响应DTO
/// </summary>
public class BatchFileUploadResponse
{
    /// <summary>
    /// 成功上传的附件列表
    /// </summary>
    public List<AttachmentDto> SuccessfulUploads { get; set; } = new();
    
    /// <summary>
    /// 失败的上传信息
    /// </summary>
    public List<FileUploadError> FailedUploads { get; set; } = new();
    
    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }
    
    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }
    
    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }
}

/// <summary>
/// 文件上传错误信息
/// </summary>
public class FileUploadError
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;
}

/// <summary>
/// 文件下载信息DTO
/// </summary>
public class FileDownloadInfo
{
    /// <summary>
    /// 文件流
    /// </summary>
    public Stream FileStream { get; set; } = null!;
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string MimeType { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
}

/// <summary>
/// 附件关联到资讯卡请求DTO
/// </summary>
public class AttachToInfoCardRequest
{
    /// <summary>
    /// 附件ID列表
    /// </summary>
    [Required(ErrorMessage = "附件ID列表不能为空")]
    [MinLength(1, ErrorMessage = "至少需要选择一个附件")]
    public List<int> AttachmentIds { get; set; } = new();
}

/// <summary>
/// 附件查询参数DTO
/// </summary>
public class AttachmentQueryParams
{
    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;
    
    /// <summary>
    /// 每页大小
    /// </summary>
    public int PageSize { get; set; } = 20;
    
    /// <summary>
    /// 文件类型过滤
    /// </summary>
    public string? FileType { get; set; }
    
    /// <summary>
    /// 文件名搜索
    /// </summary>
    public string? FileName { get; set; }
    
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }
    
    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }
}
