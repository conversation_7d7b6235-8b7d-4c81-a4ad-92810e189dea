# InfoCard 完整功能测试指南

## 🚀 测试环境状态

**✅ API服务已启动** - 运行在 https://localhost:7001  
**✅ Web应用已启动** - 运行在 https://localhost:5001  
**✅ 数据库服务** - MySQL正在运行  
**✅ 浏览器已打开** - 可以开始测试  

## 📋 测试清单

### 1. API服务测试

#### 1.1 基础连接测试
- **Swagger文档**: https://localhost:7001/swagger
- **健康检查**: https://localhost:7001/health
- **预期结果**: 
  - ✅ Swagger UI正常显示
  - ✅ 所有API端点列出
  - ✅ 健康检查返回200状态

#### 1.2 认证API测试
**用户注册测试**:
```json
POST /api/auth/register
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Test123!@#",
  "confirmPassword": "Test123!@#",
  "phoneNumber": "13800138000"
}
```

**用户登录测试**:
```json
POST /api/auth/login
{
  "phoneNumber": "13800138000",
  "password": "Test123!@#"
}
```

#### 1.3 资讯卡API测试
**获取资讯卡列表**:
```
GET /api/infocards?page=1&pageSize=10
```

**创建资讯卡**:
```json
POST /api/infocards
{
  "title": "测试资讯卡",
  "content": "这是一个测试资讯卡的内容",
  "type": "Information",
  "tags": ["测试", "演示"]
}
```

### 2. Web应用测试

#### 2.1 页面导航测试
- **主页**: https://localhost:5001
- **登录页**: https://localhost:5001/login
- **注册页**: https://localhost:5001/register
- **演示页**: https://localhost:5001/demo

#### 2.2 用户界面测试
**响应式设计验证**:
1. 调整浏览器窗口大小
2. 使用开发者工具模拟移动设备
3. 检查导航栏在不同屏幕尺寸下的表现

**样式和交互验证**:
- ✅ Bootstrap 5样式正确应用
- ✅ Font Awesome图标正常显示
- ✅ 按钮悬停效果正常
- ✅ 表单验证提示正确

#### 2.3 Blazor Server功能测试
**SignalR连接验证**:
1. 打开浏览器开发者工具 (F12)
2. 切换到"网络"标签
3. 查找WebSocket连接 (wss://localhost:5001/_blazor)
4. 确认连接状态为"已建立"

**组件交互测试**:
- ✅ 页面间导航无刷新
- ✅ 表单提交响应正常
- ✅ 动态内容更新正常

### 3. 端到端集成测试

#### 3.1 用户注册流程
1. **访问注册页面**: https://localhost:5001/register
2. **填写注册信息**:
   - 用户名: testuser
   - 邮箱: <EMAIL>
   - 手机号: 13800138000
   - 密码: Test123!@#
   - 确认密码: Test123!@#
3. **提交注册**: 点击"注册"按钮
4. **验证结果**: 
   - 成功: 显示成功消息，跳转到登录页
   - 失败: 显示错误消息

#### 3.2 用户登录流程
1. **访问登录页面**: https://localhost:5001/login
2. **填写登录信息**:
   - 手机号: 13800138000
   - 密码: Test123!@#
3. **提交登录**: 点击"登录"按钮
4. **验证结果**:
   - 成功: 跳转到主页，显示用户信息
   - 失败: 显示错误消息

#### 3.3 资讯卡管理流程
1. **查看资讯卡列表**: 主页显示资讯卡
2. **分页导航**: 测试分页功能
3. **搜索功能**: 测试搜索和过滤
4. **详情查看**: 点击资讯卡查看详情

### 4. 错误处理测试

#### 4.1 网络错误模拟
1. **断开API连接**: 停止API服务
2. **测试Web应用响应**: 应显示友好错误消息
3. **重新连接**: 启动API服务，验证恢复

#### 4.2 输入验证测试
**注册表单验证**:
- 空字段验证
- 邮箱格式验证
- 密码强度验证
- 密码确认匹配验证

**登录表单验证**:
- 手机号格式验证
- 密码长度验证

### 5. 性能测试

#### 5.1 页面加载性能
1. **打开开发者工具** → 网络标签
2. **刷新页面** (Ctrl+F5)
3. **检查加载时间**:
   - 首次加载 < 3秒
   - 后续导航 < 1秒

#### 5.2 API响应性能
1. **在Swagger中测试API**
2. **检查响应时间**:
   - 简单查询 < 500ms
   - 复杂查询 < 2秒

### 6. 安全测试

#### 6.1 认证安全
1. **未登录访问保护页面**: 应重定向到登录页
2. **JWT令牌验证**: 检查令牌在请求头中正确传递
3. **令牌过期处理**: 测试令牌过期后的处理

#### 6.2 输入安全
1. **SQL注入防护**: 在输入框中尝试SQL语句
2. **XSS防护**: 在输入框中尝试JavaScript代码
3. **CSRF防护**: 验证跨站请求防护

## 🔧 测试工具使用

### 浏览器开发者工具
1. **按F12打开开发者工具**
2. **网络标签**: 监控HTTP请求和响应
3. **控制台标签**: 查看JavaScript错误和日志
4. **应用程序标签**: 检查本地存储和Cookie

### Swagger UI测试
1. **访问**: https://localhost:7001/swagger
2. **展开API端点**: 点击要测试的API
3. **点击"Try it out"**: 启用测试功能
4. **填写参数**: 输入测试数据
5. **点击"Execute"**: 执行API调用
6. **查看响应**: 检查返回结果

### Postman测试 (可选)
如果安装了Postman，可以导入API文档进行更详细的测试。

## 📊 测试结果记录

### API测试结果
- [ ] Swagger文档正常显示
- [ ] 健康检查通过
- [ ] 用户注册API正常
- [ ] 用户登录API正常
- [ ] 资讯卡API正常

### Web应用测试结果
- [ ] 所有页面正常加载
- [ ] 响应式设计正常
- [ ] SignalR连接正常
- [ ] 表单验证正常

### 集成测试结果
- [ ] 用户注册流程完整
- [ ] 用户登录流程完整
- [ ] 数据同步正常
- [ ] 错误处理正确

### 性能测试结果
- [ ] 页面加载时间满足要求
- [ ] API响应时间满足要求
- [ ] 内存使用正常
- [ ] CPU使用正常

## 🚨 常见问题排查

### 1. 页面无法访问
- 检查应用是否正在运行
- 确认端口没有被占用
- 验证防火墙设置

### 2. API调用失败
- 检查API服务状态
- 验证CORS配置
- 确认认证令牌有效

### 3. 数据库连接失败
- 检查MySQL服务状态
- 验证连接字符串
- 确认数据库存在

### 4. SignalR连接失败
- 检查WebSocket支持
- 验证HTTPS证书
- 确认代理设置

## 🎯 测试完成标准

### 基础功能 ✅
- 所有页面正常加载
- API端点正常响应
- 数据库连接正常

### 核心功能 ✅
- 用户注册登录正常
- 资讯卡CRUD操作正常
- 实时通信正常

### 用户体验 ✅
- 界面美观易用
- 响应速度快
- 错误处理友好

### 安全性 ✅
- 认证授权正常
- 输入验证完善
- 数据传输安全

---

## 🎉 开始测试

**现在您可以开始完整的功能测试了！**

1. **API测试**: 访问 https://localhost:7001/swagger
2. **Web测试**: 访问 https://localhost:5001
3. **按照上述清单逐项测试**
4. **记录测试结果**

> **提示**: 建议先测试API功能，确认后端正常，再测试Web应用的集成功能。
