@echo off
echo ========================================
echo InfoCard Quick Test Script
echo ========================================
echo.

echo Testing API service (port 7001)...
echo.

REM Test API health check
echo 1. Testing API health check...
curl -k https://localhost:7001/health -s -o nul -w "HTTP Status: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo API service not running or inaccessible
    echo Please confirm API service is started: src\InfoCardSystem.API\publish\InfoCardSystem.API.exe
) else (
    echo API service running normally
)
echo.

REM Test Swagger page
echo 2. Testing Swagger documentation...
curl -k https://localhost:7001/swagger -s -o nul -w "HTTP Status: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo Swagger page inaccessible
) else (
    echo Swagger page normal
)
echo.

echo ========================================
echo.

echo Testing Web application (port 5001)...
echo.

REM Test Web app homepage
echo 1. Testing Web app homepage...
curl -k https://localhost:5001 -s -o nul -w "HTTP Status: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo Web app not running or inaccessible
    echo Please confirm Web app is started: src\InfoCardSystem.Web\publish\InfoCardSystem.Web.exe
) else (
    echo Web app running normally
)
echo.

REM 测试登录页面
echo 2. 测试登录页面...
curl -k https://localhost:5001/login -s -o nul -w "HTTP状态码: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo ❌ 登录页面无法访问
) else (
    echo ✓ 登录页面正常
)
echo.

REM 测试注册页面
echo 3. 测试注册页面...
curl -k https://localhost:5001/register -s -o nul -w "HTTP状态码: %%{http_code}\n" 2>nul
if %errorLevel% neq 0 (
    echo ❌ 注册页面无法访问
) else (
    echo ✓ 注册页面正常
)
echo.

echo ========================================
echo.

echo 检查端口监听状态...
echo.

echo API端口 (7001):
netstat -ano | findstr ":7001" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ❌ 端口7001未被监听
) else (
    echo ✓ 端口7001正在监听
)

echo Web端口 (5001):
netstat -ano | findstr ":5001" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ❌ 端口5001未被监听
) else (
    echo ✓ 端口5001正在监听
)
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果所有测试都通过，您可以：
echo.
echo 1. 访问Web应用: https://localhost:5001
echo 2. 访问API文档: https://localhost:7001/swagger
echo 3. 按照"完整功能测试指南.md"进行详细测试
echo.
echo 如果测试失败，请：
echo.
echo 1. 启动API服务:
echo    cd src\InfoCardSystem.API\publish
echo    InfoCardSystem.API.exe
echo.
echo 2. 启动Web应用:
echo    cd src\InfoCardSystem.Web\publish
echo    InfoCardSystem.Web.exe
echo.
echo 3. 检查MySQL服务是否运行:
echo    sc query mysql80
echo.

pause
