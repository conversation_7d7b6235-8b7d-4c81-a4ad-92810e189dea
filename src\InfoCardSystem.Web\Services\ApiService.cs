using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using InfoCardSystem.Shared.DTOs;
using Blazored.LocalStorage;

namespace InfoCardSystem.Web.Services;

/// <summary>
/// API服务实现�?/// </summary>
/// <remarks>
/// 负责处理与后端API的HTTP通信，包括：
/// - 用户认证和授�?/// - 资讯卡管�?/// - 好友关系管理
/// - 群组管理
/// - 文件上传
///
/// 特性：
/// - 自动JWT令牌管理
/// - 统一错误处理
/// - JSON序列化配�?/// - 请求超时控制
/// </remarks>
public class ApiService : IApiService
{
    #region 私有字段

    /// <summary>
    /// HTTP客户端实例，用于发送HTTP请求
    /// </summary>
    private readonly HttpClient _httpClient;

    /// <summary>
    /// 本地存储服务，用于管理JWT令牌等持久化数据
    /// </summary>
    private readonly ILocalStorageService _localStorage;

    /// <summary>
    /// JSON序列化选项配置
    /// </summary>
    private readonly JsonSerializerOptions _jsonOptions;

    #endregion

    #region 构造函�?
    /// <summary>
    /// 初始化ApiService实例
    /// </summary>
    /// <param name="httpClient">HTTP客户端实�?/param>
    /// <param name="localStorage">本地存储服务实例</param>
    public ApiService(HttpClient httpClient, ILocalStorageService localStorage)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _localStorage = localStorage ?? throw new ArgumentNullException(nameof(localStorage));

        // 配置JSON序列化选项
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    #endregion

    #region 认证相关

    public async Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginRequestDto request)
    {
        try
        {
            var response = await PostAsync<LoginResponseDto>("api/v1/auth/login", request);
            if (response.IsSuccess && response.Data?.Token != null)
            {
                await _localStorage.SetItemAsync("authToken", response.Data.Token);
                SetAuthorizationHeader(response.Data.Token);
            }
            return response;
        }
        catch (Exception ex)
        {
            return ApiResponse<LoginResponseDto>.ErrorResult($"登录失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<RegisterResponseDto>> RegisterAsync(RegisterRequestDto request)
    {
        try
        {
            return await PostAsync<RegisterResponseDto>("api/v1/auth/register", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<RegisterResponseDto>.ErrorResult($"注册失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> LogoutAsync()
    {
        try
        {
            await _localStorage.RemoveItemAsync("authToken");
            _httpClient.DefaultRequestHeaders.Authorization = null;
            return ApiResponse<object>.SuccessResult(new object());
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"登出失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserProfileDto>> GetCurrentUserAsync()
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<UserProfileDto>("api/users/profile");
        }
        catch (Exception ex)
        {
            return ApiResponse<UserProfileDto>.ErrorResult($"获取用户信息失败: {ex.Message}");
        }
    }

    #endregion

    #region 用户管理

    public async Task<ApiResponse<UserProfileDto>> GetUserProfileAsync(int userId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<UserProfileDto>($"api/users/{userId}");
        }
        catch (Exception ex)
        {
            return ApiResponse<UserProfileDto>.ErrorResult($"获取用户资料失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<UserProfileDto>> UpdateUserProfileAsync(UpdateUserProfileDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PutAsync<UserProfileDto>("api/users/profile", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<UserProfileDto>.ErrorResult($"更新用户资料失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> ChangePasswordAsync(ChangePasswordDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>("api/users/change-password", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"修改密码失败: {ex.Message}");
        }
    }

    #endregion

    #region 好友管理

    public async Task<ApiResponse<List<FriendDto>>> GetFriendsAsync()
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<FriendDto>>("api/friends");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<FriendDto>>.ErrorResult($"获取好友列表失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<FriendRequestDto>>> GetFriendRequestsAsync()
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<FriendRequestDto>>("api/friends/requests");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<FriendRequestDto>>.ErrorResult($"获取好友请求失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> SendFriendRequestAsync(SendFriendRequestDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>("api/friends/request", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"发送好友请求失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> AcceptFriendRequestAsync(int requestId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/friends/requests/{requestId}/accept", null);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"接受好友请求失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> RejectFriendRequestAsync(int requestId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/friends/requests/{requestId}/reject", null);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"拒绝好友请求失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> RemoveFriendAsync(int friendId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await DeleteAsync<object>($"api/friends/{friendId}");
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"删除好友失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<UserSearchResultDto>>> SearchUsersAsync(string query)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<UserSearchResultDto>>($"api/users/search?q={Uri.EscapeDataString(query)}");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<UserSearchResultDto>>.ErrorResult($"搜索用户失败: {ex.Message}");
        }
    }

    #endregion

    #region 资讯卡管�?
    public async Task<ApiResponse<List<InfoCardDto>>> GetInfoCardsAsync(int page = 1, int pageSize = 20)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<InfoCardDto>>($"api/infocards?page={page}&pageSize={pageSize}");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<InfoCardDto>>.ErrorResult($"获取资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<InfoCardDto>> GetInfoCardAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<InfoCardDto>($"api/infocards/{id}");
        }
        catch (Exception ex)
        {
            return ApiResponse<InfoCardDto>.ErrorResult($"获取资讯卡详情失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<InfoCardDto>> CreateInfoCardAsync(CreateInfoCardDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<InfoCardDto>("api/infocards", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<InfoCardDto>.ErrorResult($"创建资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<InfoCardDto>> UpdateInfoCardAsync(int id, UpdateInfoCardDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PutAsync<InfoCardDto>($"api/infocards/{id}", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<InfoCardDto>.ErrorResult($"更新资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> DeleteInfoCardAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await DeleteAsync<object>($"api/infocards/{id}");
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"删除资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> ForwardInfoCardAsync(int id, ForwardInfoCardDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/infocards/{id}/forward", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"转发资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> FavoriteInfoCardAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/infocards/{id}/favorite", null);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"收藏资讯卡失�? {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> UnfavoriteInfoCardAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await DeleteAsync<object>($"api/infocards/{id}/favorite");
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"取消收藏失败: {ex.Message}");
        }
    }

    #endregion

    #region 群组管理

    public async Task<ApiResponse<List<GroupDto>>> GetGroupsAsync()
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<GroupDto>>("api/groups");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<GroupDto>>.ErrorResult($"获取群组列表失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<GroupDto>> GetGroupAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<GroupDto>($"api/groups/{id}");
        }
        catch (Exception ex)
        {
            return ApiResponse<GroupDto>.ErrorResult($"获取群组详情失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<GroupDto>> CreateGroupAsync(CreateGroupDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<GroupDto>("api/groups", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<GroupDto>.ErrorResult($"创建群组失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<GroupDto>> UpdateGroupAsync(int id, UpdateGroupDto request)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PutAsync<GroupDto>($"api/groups/{id}", request);
        }
        catch (Exception ex)
        {
            return ApiResponse<GroupDto>.ErrorResult($"更新群组失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> DeleteGroupAsync(int id)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await DeleteAsync<object>($"api/groups/{id}");
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"删除群组失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> JoinGroupAsync(int groupId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/groups/{groupId}/join", null);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"加入群组失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<object>> LeaveGroupAsync(int groupId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await PostAsync<object>($"api/groups/{groupId}/leave", null);
        }
        catch (Exception ex)
        {
            return ApiResponse<object>.ErrorResult($"离开群组失败: {ex.Message}");
        }
    }

    public async Task<ApiResponse<List<GroupMemberDto>>> GetGroupMembersAsync(int groupId)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            return await GetAsync<List<GroupMemberDto>>($"api/groups/{groupId}/members");
        }
        catch (Exception ex)
        {
            return ApiResponse<List<GroupMemberDto>>.ErrorResult($"获取群组成员失败: {ex.Message}");
        }
    }

    #endregion

    #region 文件上传

    public async Task<ApiResponse<FileUploadResponseDto>> UploadFileAsync(Stream fileStream, string fileName, string contentType)
    {
        try
        {
            await EnsureAuthenticatedAsync();
            
            using var content = new MultipartFormDataContent();
            using var streamContent = new StreamContent(fileStream);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
            content.Add(streamContent, "file", fileName);

            var response = await _httpClient.PostAsync("api/files/upload", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<FileUploadResponseDto>(responseContent, _jsonOptions);
                return ApiResponse<FileUploadResponseDto>.SuccessResult(result!);
            }
            else
            {
                return ApiResponse<FileUploadResponseDto>.ErrorResult($"文件上传失败: {responseContent}", $"HTTP_{(int)response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            return ApiResponse<FileUploadResponseDto>.ErrorResult($"文件上传失败: {ex.Message}");
        }
    }

    #endregion

    #region 私有辅助方法

    private async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            return await ProcessResponseAsync<T>(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"请求失败: {ex.Message}");
        }
    }

    private async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object? data)
    {
        try
        {
            var json = data != null ? JsonSerializer.Serialize(data, _jsonOptions) : string.Empty;
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(endpoint, content);
            return await ProcessResponseAsync<T>(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"请求失败: {ex.Message}");
        }
    }

    private async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync(endpoint, content);
            return await ProcessResponseAsync<T>(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"请求失败: {ex.Message}");
        }
    }

    private async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            return await ProcessResponseAsync<T>(response);
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"请求失败: {ex.Message}");
        }
    }

    private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            if (typeof(T) == typeof(object) && string.IsNullOrEmpty(content))
            {
                return ApiResponse<T>.SuccessResult(default(T)!);
            }

            var result = JsonSerializer.Deserialize<T>(content, _jsonOptions);
            return ApiResponse<T>.SuccessResult(result!);
        }
        else
        {
            return ApiResponse<T>.ErrorResult(content, $"HTTP_{(int)response.StatusCode}");
        }
    }

    private async Task EnsureAuthenticatedAsync()
    {
        if (_httpClient.DefaultRequestHeaders.Authorization == null)
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (!string.IsNullOrEmpty(token))
            {
                SetAuthorizationHeader(token);
            }
        }
    }

    private void SetAuthorizationHeader(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    #endregion
}

