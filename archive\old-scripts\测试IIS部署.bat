@echo off
chcp 65001 >nul
echo ========================================
echo InfoCard IIS部署测试脚本
echo ========================================
echo.

echo 正在测试API服务 (端口8081)...
echo.

REM 测试API基础连接
echo 1. 测试API基础连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8081/ 2>nul
if %errorLevel% neq 0 (
    echo ❌ API基础连接失败
    echo    请检查ICAPI网站是否正在运行
) else (
    echo ✓ API基础连接正常
)
echo.

REM 测试Swagger页面
echo 2. 测试Swagger文档页面...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8081/swagger 2>nul
if %errorLevel% neq 0 (
    echo ❌ Swagger页面访问失败
) else (
    echo ✓ Swagger页面访问正常
)
echo.

REM 测试健康检查
echo 3. 测试API健康检查...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8081/health 2>nul
if %errorLevel% neq 0 (
    echo ❌ 健康检查失败
) else (
    echo ✓ 健康检查正常
)
echo.

echo ========================================
echo.

echo 正在测试Web应用 (端口8082)...
echo.

REM 测试Web应用基础连接
echo 1. 测试Web应用基础连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8082/ 2>nul
if %errorLevel% neq 0 (
    echo ❌ Web应用基础连接失败
    echo    请检查ICWeb网站是否正在运行
) else (
    echo ✓ Web应用基础连接正常
)
echo.

REM 测试登录页面
echo 2. 测试登录页面...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8082/login 2>nul
if %errorLevel% neq 0 (
    echo ❌ 登录页面访问失败
) else (
    echo ✓ 登录页面访问正常
)
echo.

REM 测试注册页面
echo 3. 测试注册页面...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8082/register 2>nul
if %errorLevel% neq 0 (
    echo ❌ 注册页面访问失败
) else (
    echo ✓ 注册页面访问正常
)
echo.

REM 测试演示页面
echo 4. 测试演示页面...
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8082/demo 2>nul
if %errorLevel% neq 0 (
    echo ❌ 演示页面访问失败
) else (
    echo ✓ 演示页面访问正常
)
echo.

echo ========================================
echo.

echo 检查IIS网站状态...
echo.

REM 检查ICAPI网站状态
echo ICAPI网站状态:
%windir%\system32\inetsrv\appcmd list site "ICAPI" 2>nul | findstr "state:Started" >nul
if %errorLevel% neq 0 (
    echo ❌ ICAPI网站未启动
    %windir%\system32\inetsrv\appcmd list site "ICAPI" 2>nul
) else (
    echo ✓ ICAPI网站正在运行
)
echo.

REM 检查ICWeb网站状态
echo ICWeb网站状态:
%windir%\system32\inetsrv\appcmd list site "ICWeb" 2>nul | findstr "state:Started" >nul
if %errorLevel% neq 0 (
    echo ❌ ICWeb网站未启动
    %windir%\system32\inetsrv\appcmd list site "ICWeb" 2>nul
) else (
    echo ✓ ICWeb网站正在运行
)
echo.

echo ========================================
echo.

echo 检查端口占用情况...
echo.
netstat -ano | findstr ":8081" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ❌ 端口8081未被监听
) else (
    echo ✓ 端口8081正在监听
)

netstat -ano | findstr ":8082" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ❌ 端口8082未被监听
) else (
    echo ✓ 端口8082正在监听
)
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果测试失败，请检查:
echo.
echo 1. 网站配置:
echo    - 物理路径是否正确
echo    - 端口绑定是否正确
echo    - 应用程序池是否启动
echo.
echo 2. 运行时环境:
echo    - .NET 9.0 Runtime是否已安装
echo    - ASP.NET Core Hosting Bundle是否已安装
echo.
echo 3. 权限设置:
echo    - IIS_IUSRS是否有适当权限
echo    - 日志目录是否可写
echo.
echo 4. 网络配置:
echo    - 防火墙是否允许端口8081和8082
echo    - 端口是否被其他程序占用
echo.
echo 5. 应用程序配置:
echo    - 数据库连接字符串是否正确
echo    - API基础URL配置是否正确
echo.
echo 查看详细日志:
echo API日志: C:\inetpub\wwwroot\ICAPI\logs\
echo Web日志: C:\inetpub\wwwroot\ICWeb\logs\
echo IIS日志: C:\inetpub\logs\LogFiles\
echo.

pause
