using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 资讯卡时间线ViewModel
/// </summary>
public partial class InfoCardsViewModel : BaseViewModel
{
    private readonly IInfoCardApiClient _apiClient;
    private readonly IAuthenticationService _authService;

    public InfoCardsViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IInfoCardApiClient apiClient,
        IAuthenticationService authService,
        ILogger<InfoCardsViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _apiClient = apiClient;
        _authService = authService;
        Title = "首页";
        
        InfoCards = new ObservableCollection<InfoCardViewModel>();
        
        // 初始化欢迎消息
        InitializeWelcomeMessage();
    }

    [ObservableProperty]
    private ObservableCollection<InfoCardViewModel> infoCards;

    [ObservableProperty]
    private string welcomeMessage = "欢迎使用InfoCard";

    [ObservableProperty]
    private bool hasMoreItems = true;

    private int _currentPage = 1;
    private const int PageSize = 20;

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await ExecuteSafelyAsync(async () =>
        {
            IsRefreshing = true;
            _currentPage = 1;
            HasMoreItems = true;
            
            await LoadInfoCardsAsync(true);
            
        }, "刷新", false);
    }

    /// <summary>
    /// 搜索命令
    /// </summary>
    [RelayCommand]
    private async Task SearchAsync()
    {
        await NavigateToAsync("search");
    }

    /// <summary>
    /// 添加好友命令
    /// </summary>
    [RelayCommand]
    private async Task AddFriendAsync()
    {
        await NavigateToAsync("addfriend");
    }

    /// <summary>
    /// 创建资讯卡命令
    /// </summary>
    [RelayCommand]
    private async Task CreateInfoCardAsync()
    {
        await NavigateToAsync("createinfocard");
    }

    /// <summary>
    /// 查看好友命令
    /// </summary>
    [RelayCommand]
    private async Task ViewFriendsAsync()
    {
        await NavigateToAsync("friends");
    }

    /// <summary>
    /// 查看收藏命令
    /// </summary>
    [RelayCommand]
    private async Task ViewFavoritesAsync()
    {
        await NavigateToAsync("favorites");
    }

    /// <summary>
    /// 加载资讯卡
    /// </summary>
    private async Task LoadInfoCardsAsync(bool isRefresh = false)
    {
        try
        {
            _logger.LogDebug("加载资讯卡: 页码={Page}, 刷新={IsRefresh}", _currentPage, isRefresh);

            // 模拟数据 - 实际应该调用API
            var mockData = GenerateMockInfoCards();
            
            if (isRefresh)
            {
                InfoCards.Clear();
            }

            foreach (var item in mockData)
            {
                InfoCards.Add(item);
            }

            // 模拟分页
            if (_currentPage >= 3)
            {
                HasMoreItems = false;
            }
            else
            {
                _currentPage++;
            }

            _logger.LogInformation("资讯卡加载完成: 当前数量={Count}", InfoCards.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载资讯卡失败");
            await ShowErrorAsync("加载失败", "无法加载资讯卡，请重试");
        }
        finally
        {
            IsRefreshing = false;
        }
    }

    /// <summary>
    /// 初始化欢迎消息
    /// </summary>
    private async void InitializeWelcomeMessage()
    {
        try
        {
            var currentUser = await _authService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                var displayName = !string.IsNullOrEmpty(currentUser.DisplayName) 
                    ? currentUser.DisplayName 
                    : currentUser.Username;
                WelcomeMessage = $"你好，{displayName}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化欢迎消息失败");
        }
    }

    /// <summary>
    /// 生成模拟数据
    /// </summary>
    private List<InfoCardViewModel> GenerateMockInfoCards()
    {
        var mockCards = new List<InfoCardViewModel>();
        
        for (int i = 1; i <= 5; i++)
        {
            mockCards.Add(new InfoCardViewModel
            {
                Id = i,
                Title = $"资讯卡标题 {i}",
                Content = $"这是第 {i} 张资讯卡的内容。InfoCard让分享变得更简单，您可以轻松地与好友分享各种信息和想法。",
                Publisher = new PublisherViewModel
                {
                    Id = i,
                    Username = $"user{i}",
                    DisplayName = $"用户 {i}",
                    AvatarUrl = "default_avatar.png"
                },
                CreatedAt = DateTime.Now.AddHours(-i),
                IsFavorited = i % 2 == 0,
                Attachments = new List<AttachmentViewModel>()
            });
        }

        return mockCards;
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        
        if (!InfoCards.Any())
        {
            await LoadInfoCardsAsync(true);
        }
    }
}

/// <summary>
/// 资讯卡ViewModel
/// </summary>
public class InfoCardViewModel
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public PublisherViewModel Publisher { get; set; } = new();
    public List<AttachmentViewModel> Attachments { get; set; } = new();
    public bool IsFavorited { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 发布者ViewModel
/// </summary>
public class PublisherViewModel
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string AvatarUrl { get; set; } = string.Empty;
}

/// <summary>
/// 附件ViewModel
/// </summary>
public class AttachmentViewModel
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
}
