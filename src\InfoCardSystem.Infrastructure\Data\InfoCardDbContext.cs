using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;

namespace InfoCardSystem.Infrastructure.Data;

/// <summary>
/// 数据库上下文
/// </summary>
public class InfoCardDbContext : DbContext
{
    public InfoCardDbContext(DbContextOptions<InfoCardDbContext> options) : base(options)
    {
    }
    
    // DbSet 属性
    public DbSet<AppUser> AppUsers { get; set; }
    public DbSet<UserFriendship> UserFriendships { get; set; }
    public DbSet<UserBlacklist> UserBlacklists { get; set; }
    public DbSet<UserGroup> UserGroups { get; set; }
    public DbSet<UserGroupMember> UserGroupMembers { get; set; }
    public DbSet<UserInfoCard> UserInfoCards { get; set; }
    public DbSet<UserInfoCardRecipient> UserInfoCardRecipients { get; set; }
    public DbSet<UserInfoCardFavorite> UserInfoCardFavorites { get; set; }
    public DbSet<UserAttachment> UserAttachments { get; set; }
    public DbSet<UserInfoCardAttachment> UserInfoCardAttachments { get; set; }
    public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
    public DbSet<UserQRCode> UserQRCodes { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // 应用所有配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(InfoCardDbContext).Assembly);
        
        // 设置表名前缀和命名约定
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var tableName = entityType.GetTableName();
            if (tableName != null)
            {
                // 将表名转换为小写并添加下划线分隔
                var newTableName = ConvertToSnakeCase(tableName);
                entityType.SetTableName(newTableName);
            }
        }
    }
    
    /// <summary>
    /// 转换为蛇形命名法
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>蛇形命名法字符串</returns>
    private static string ConvertToSnakeCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;
        
        var result = new System.Text.StringBuilder();
        
        for (int i = 0; i < input.Length; i++)
        {
            if (char.IsUpper(input[i]))
            {
                if (i > 0)
                    result.Append('_');
                result.Append(char.ToLower(input[i]));
            }
            else
            {
                result.Append(input[i]);
            }
        }
        
        return result.ToString();
    }
    
    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }
    
    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return base.SaveChangesAsync(cancellationToken);
    }
    
    /// <summary>
    /// 更新时间戳
    /// </summary>
    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();
        
        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }
    }
}
