<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:LoginViewModel"
             Title="登录"
             Shell.NavBarIsVisible="False"
             BackgroundColor="{StaticResource Primary}">

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="24">
            
            <!-- 顶部Logo区域 -->
            <StackLayout Grid.Row="0" 
                        VerticalOptions="Center" 
                        HorizontalOptions="Center"
                        Margin="0,60,0,40">
                
                <!-- App Logo -->
                <Image Source="app_logo.png" 
                       WidthRequest="120" 
                       HeightRequest="120"
                       Margin="0,0,0,20" />
                
                <!-- App Name -->
                <Label Text="InfoCard" 
                       FontSize="32" 
                       FontAttributes="Bold"
                       TextColor="White"
                       HorizontalOptions="Center" />
                
                <!-- Subtitle -->
                <Label Text="好友资讯分享" 
                       FontSize="16" 
                       TextColor="White"
                       Opacity="0.8"
                       HorizontalOptions="Center"
                       Margin="0,8,0,0" />
                
            </StackLayout>

            <!-- 登录表单 -->
            <StackLayout Grid.Row="1" 
                        VerticalOptions="Center"
                        Spacing="20">

                <!-- 欢迎文字 -->
                <StackLayout Margin="0,0,0,20">
                    <Label Text="欢迎回来" 
                           FontSize="24" 
                           FontAttributes="Bold"
                           TextColor="White"
                           HorizontalOptions="Center" />
                    <Label Text="登录您的InfoCard账户" 
                           FontSize="14" 
                           TextColor="White"
                           Opacity="0.8"
                           HorizontalOptions="Center"
                           Margin="0,8,0,0" />
                </StackLayout>

                <!-- 用户名输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="UsernameEntry"
                           Text="{Binding Username}"
                           Placeholder="用户名/邮箱/手机号"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 密码输入框 -->
                <Frame BackgroundColor="White" 
                       CornerRadius="12" 
                       Padding="0" 
                       HasShadow="False">
                    <Entry x:Name="PasswordEntry"
                           Text="{Binding Password}"
                           Placeholder="密码"
                           IsPassword="True"
                           FontSize="16"
                           Margin="16,12"
                           BackgroundColor="Transparent"
                           TextColor="Black"
                           PlaceholderColor="Gray" />
                </Frame>

                <!-- 记住我和忘记密码 -->
                <Grid ColumnDefinitions="*,Auto" Margin="8,0">
                    <StackLayout Grid.Column="0" 
                                Orientation="Horizontal">
                        <CheckBox x:Name="RememberMeCheckBox"
                                 IsChecked="{Binding RememberMe}"
                                 Color="White" />
                        <Label Text="记住我" 
                               TextColor="White"
                               VerticalOptions="Center"
                               FontSize="14" />
                    </StackLayout>
                    
                    <Button Grid.Column="1"
                           Text="忘记密码?"
                           BackgroundColor="Transparent"
                           TextColor="White"
                           FontSize="14"
                           Command="{Binding ForgotPasswordCommand}" />
                </Grid>

                <!-- 登录按钮 -->
                <Button Text="登 录"
                       BackgroundColor="White"
                       TextColor="{StaticResource Primary}"
                       FontSize="18"
                       FontAttributes="Bold"
                       CornerRadius="12"
                       HeightRequest="50"
                       Command="{Binding LoginCommand}"
                       IsEnabled="{Binding IsNotBusy}" />

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  Color="White"
                                  WidthRequest="30"
                                  HeightRequest="30" />

                <!-- 错误消息 -->
                <Label Text="{Binding ErrorMessage}"
                      TextColor="Yellow"
                      FontSize="14"
                      HorizontalOptions="Center"
                      IsVisible="{Binding HasError}" />

            </StackLayout>

            <!-- 底部注册链接 -->
            <StackLayout Grid.Row="2" 
                        VerticalOptions="End"
                        Margin="0,40,0,20">
                
                <!-- 分割线 -->
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center"
                            Margin="0,0,0,20">
                    <BoxView BackgroundColor="White" 
                            Opacity="0.3"
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                    <Label Text="或者" 
                           TextColor="White"
                           Opacity="0.8"
                           FontSize="14"
                           Margin="16,0" />
                    <BoxView BackgroundColor="White" 
                            Opacity="0.3"
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                </StackLayout>

                <!-- 其他登录方式 -->
                <Grid ColumnDefinitions="*,*" 
                      ColumnSpacing="16"
                      Margin="0,0,0,20">
                    <Button Grid.Column="0"
                           Text="📱 手机号登录"
                           BackgroundColor="Transparent"
                           BorderColor="White"
                           BorderWidth="1"
                           TextColor="White"
                           FontSize="14"
                           CornerRadius="8"
                           HeightRequest="44"
                           Command="{Binding PhoneLoginCommand}" />
                    
                    <Button Grid.Column="1"
                           Text="🔐 生物识别"
                           BackgroundColor="Transparent"
                           BorderColor="White"
                           BorderWidth="1"
                           TextColor="White"
                           FontSize="14"
                           CornerRadius="8"
                           HeightRequest="44"
                           Command="{Binding BiometricLoginCommand}" />
                </Grid>

                <!-- 注册链接 -->
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center">
                    <Label Text="还没有账户?" 
                           TextColor="White"
                           Opacity="0.8"
                           FontSize="14" />
                    <Button Text="立即注册"
                           BackgroundColor="Transparent"
                           TextColor="White"
                           FontSize="14"
                           FontAttributes="Bold"
                           Padding="8,0"
                           Command="{Binding RegisterCommand}" />
                </StackLayout>

            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
