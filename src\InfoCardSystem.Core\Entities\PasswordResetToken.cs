using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 密码重置令牌实体
/// </summary>
[Table("PasswordResetTokens")]
public class PasswordResetToken
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// 重置令牌
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱或手机号
    /// </summary>
    [Required]
    [StringLength(100)]
    public string EmailOrPhone { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [Required]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    [Required]
    public bool IsUsed { get; set; }

    /// <summary>
    /// 使用时间
    /// </summary>
    public DateTime? UsedAt { get; set; }

    /// <summary>
    /// 用户导航属性
    /// </summary>
    [ForeignKey(nameof(UserId))]
    public virtual AppUser User { get; set; } = null!;
}
