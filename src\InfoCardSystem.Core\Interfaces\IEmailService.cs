namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 邮件服务接口
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// 发送密码重置邮件
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="resetToken">重置令牌</param>
    /// <param name="userName">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendPasswordResetEmailAsync(string email, string resetToken, string userName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送验证邮件
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="verificationCode">验证码</param>
    /// <param name="userName">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendVerificationEmailAsync(string email, string verificationCode, string userName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送通知邮件
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="subject">邮件主题</param>
    /// <param name="content">邮件内容</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendNotificationEmailAsync(string email, string subject, string content, CancellationToken cancellationToken = default);
}
