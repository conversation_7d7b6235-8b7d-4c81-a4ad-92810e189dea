<?xml version="1.0" encoding="UTF-8"?>
<svg width="456" height="456" viewBox="0 0 456 456" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Background Circle -->
    <circle cx="228" cy="228" r="228" fill="#6750A4"/>
    
    <!-- InfoCard Icon -->
    <g transform="translate(128, 128)">
        <!-- Card Background -->
        <rect x="0" y="0" width="200" height="140" rx="16" fill="white" opacity="0.95"/>
        
        <!-- Card Header -->
        <rect x="16" y="16" width="168" height="4" rx="2" fill="#6750A4" opacity="0.8"/>
        <rect x="16" y="28" width="120" height="4" rx="2" fill="#6750A4" opacity="0.6"/>
        
        <!-- Card Content Lines -->
        <rect x="16" y="48" width="168" height="3" rx="1.5" fill="#6750A4" opacity="0.4"/>
        <rect x="16" y="58" width="140" height="3" rx="1.5" fill="#6750A4" opacity="0.4"/>
        <rect x="16" y="68" width="160" height="3" rx="1.5" fill="#6750A4" opacity="0.4"/>
        <rect x="16" y="78" width="100" height="3" rx="1.5" fill="#6750A4" opacity="0.4"/>
        
        <!-- Share Icon -->
        <g transform="translate(150, 100)">
            <circle cx="12" cy="12" r="12" fill="#6750A4"/>
            <path d="M8 12L16 8V10H20V14H16V16L8 12Z" fill="white"/>
        </g>
        
        <!-- Friend Icons -->
        <g transform="translate(16, 100)">
            <circle cx="8" cy="8" r="8" fill="#625B71"/>
            <circle cx="20" cy="8" r="8" fill="#625B71"/>
            <circle cx="32" cy="8" r="8" fill="#625B71"/>
        </g>
    </g>
    
    <!-- App Name -->
    <text x="228" y="380" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32" font-weight="bold">InfoCard</text>
</svg>
