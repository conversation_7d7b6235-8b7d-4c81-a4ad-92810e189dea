using InfoCardSystem.Mobile.Services;
using InfoCardSystem.Mobile.Models;

namespace InfoCardSystem.Mobile.Views;

// 简单的Mock认证服务，避免依赖注入问题
public class MockAuthenticationService : IAuthenticationService
{
    public Task<bool> IsAuthenticatedAsync() => Task.FromResult(false);

    public Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request) =>
        Task.FromResult(new ApiResponse<LoginResponse> { Success = false, Data = null });

    public Task<ApiResponse<RegisterResponse>> RegisterAsync(RegisterRequest request) =>
        Task.FromResult(new ApiResponse<RegisterResponse> { Success = false, Data = null });

    public Task LogoutAsync() => Task.CompletedTask;

    public Task<ApiResponse<TokenResponse>> RefreshTokenAsync() =>
        Task.FromResult(new ApiResponse<TokenResponse> { Success = false, Data = null });

    public Task<UserInfo?> GetCurrentUserAsync() => Task.FromResult<UserInfo?>(null);

    public Task<string?> GetAccessTokenAsync() => Task.FromResult<string?>(null);

    public Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request) =>
        Task.FromResult(new ApiResponse<bool> { Success = false, Data = false });

    public Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request) =>
        Task.FromResult(new ApiResponse<bool> { Success = false, Data = false });
}

public partial class SplashPage : ContentPage
{
    private readonly IAuthenticationService _authService;

    public SplashPage()
    {
        InitializeComponent();

        // 使用空实现避免依赖注入问题
        _authService = new MockAuthenticationService();
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        try
        {
            // 显示启动画面2秒
            await Task.Delay(2000);
            
            // 检查认证状态
            await CheckAuthenticationAndNavigate();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SplashPage error: {ex}");
            // 出错时导航到登录页
            await NavigateToLogin();
        }
    }

    private async Task CheckAuthenticationAndNavigate()
    {
        try
        {
            var isAuthenticated = await _authService.IsAuthenticatedAsync();
            
            if (isAuthenticated)
            {
                await Shell.Current.GoToAsync("//main");
            }
            else
            {
                await NavigateToLogin();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Authentication check failed: {ex}");
            await NavigateToLogin();
        }
    }

    private async Task NavigateToLogin()
    {
        try
        {
            await Shell.Current.GoToAsync("//login");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Navigation to login failed: {ex}");
            // 如果导航失败，创建简单的登录页面
            Application.Current.MainPage = new ContentPage
            {
                Content = new StackLayout
                {
                    Children =
                    {
                        new Label { Text = "InfoCard", FontSize = 24, HorizontalOptions = LayoutOptions.Center },
                        new Label { Text = "请重新启动应用", HorizontalOptions = LayoutOptions.Center }
                    },
                    VerticalOptions = LayoutOptions.Center
                }
            };
        }
    }
}
