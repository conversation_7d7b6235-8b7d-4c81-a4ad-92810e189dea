using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Blazored.LocalStorage;
using InfoCardSystem.Shared.DTOs;

namespace InfoCardSystem.Web.Services;

/// <summary>
/// 认证状态管理服务
/// </summary>
/// <remarks>
/// 负责管理用户的认证状态，包括：
/// - JWT令牌的存储和验证
/// - 用户登录状态的维护
/// - 认证状态变化的通知
/// - 用户信息的缓存和更新
///
/// 该服务是应用程序认证系统的核心组件，确保用户状态在整个应用中的一致性。
/// </remarks>
public class AuthStateService
{
    #region 私有字段

    /// <summary>
    /// 本地存储服务，用于持久化JWT令牌
    /// </summary>
    private readonly ILocalStorageService _localStorage;

    /// <summary>
    /// API服务，用于与后端进行认证相关的通信
    /// </summary>
    private readonly IApiService _apiService;

    #endregion

    #region 公共事件

    /// <summary>
    /// 认证状态变化事件
    /// </summary>
    /// <remarks>
    /// 当用户登录、登出或认证状态发生变化时触发此事件。
    /// 订阅此事件的组件可以及时响应认证状态的变化。
    /// </remarks>
    public event Action<bool>? AuthStateChanged;

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取当前用户是否已认证
    /// </summary>
    /// <value>如果用户已登录且令牌有效则返回true，否则返回false</value>
    public bool IsAuthenticated { get; private set; }

    /// <summary>
    /// 获取当前登录用户的详细信息
    /// </summary>
    /// <value>已登录用户的资料信息，未登录时为null</value>
    public UserProfileDto? CurrentUser { get; private set; }

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化认证状态服务实例
    /// </summary>
    /// <param name="localStorage">本地存储服务</param>
    /// <param name="apiService">API服务</param>
    /// <exception cref="ArgumentNullException">当参数为null时抛出</exception>
    public AuthStateService(ILocalStorageService localStorage, IApiService apiService)
    {
        _localStorage = localStorage ?? throw new ArgumentNullException(nameof(localStorage));
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
    }

    #endregion

    /// <summary>
    /// 初始化认证状态
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (!string.IsNullOrEmpty(token) && IsTokenValid(token))
            {
                // 验证token并获取用户信息
                var userResponse = await _apiService.GetCurrentUserAsync();
                if (userResponse.IsSuccess && userResponse.Data != null)
                {
                    CurrentUser = userResponse.Data;
                    IsAuthenticated = true;
                }
                else
                {
                    // Token无效，清除本地存储
                    await LogoutAsync();
                }
            }
            else
            {
                IsAuthenticated = false;
                CurrentUser = null;
            }
            
            NotifyAuthStateChanged();
        }
        catch
        {
            IsAuthenticated = false;
            CurrentUser = null;
            NotifyAuthStateChanged();
        }
    }

    /// <summary>
    /// 登录
    /// </summary>
    public async Task<bool> LoginAsync(LoginRequestDto request)
    {
        try
        {
            var response = await _apiService.LoginAsync(request);
            if (response.IsSuccess && response.Data != null)
            {
                // 获取用户信息
                var userResponse = await _apiService.GetCurrentUserAsync();
                if (userResponse.IsSuccess && userResponse.Data != null)
                {
                    CurrentUser = userResponse.Data;
                    IsAuthenticated = true;
                    NotifyAuthStateChanged();
                    return true;
                }
            }

            // 记录登录失败的原因
            LastError = response.ErrorMessage ?? "登录失败，请检查用户名和密码";
            return false;
        }
        catch (Exception ex)
        {
            LastError = $"登录时发生错误：{ex.Message}";
            return false;
        }
    }

    /// <summary>
    /// 最后一次操作的错误信息
    /// </summary>
    public string? LastError { get; private set; }

    /// <summary>
    /// 登出
    /// </summary>
    public async Task LogoutAsync()
    {
        try
        {
            await _apiService.LogoutAsync();
        }
        finally
        {
            IsAuthenticated = false;
            CurrentUser = null;
            NotifyAuthStateChanged();
        }
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    public async Task<bool> UpdateUserProfileAsync(UpdateUserProfileDto request)
    {
        try
        {
            var response = await _apiService.UpdateUserProfileAsync(request);
            if (response.IsSuccess && response.Data != null)
            {
                CurrentUser = response.Data;
                NotifyAuthStateChanged();
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 刷新用户信息
    /// </summary>
    public async Task RefreshUserAsync()
    {
        if (!IsAuthenticated) return;

        try
        {
            var response = await _apiService.GetCurrentUserAsync();
            if (response.IsSuccess && response.Data != null)
            {
                CurrentUser = response.Data;
                NotifyAuthStateChanged();
            }
        }
        catch
        {
            // 忽略错误，保持当前状态
        }
    }

    /// <summary>
    /// 检查Token是否有效
    /// </summary>
    private bool IsTokenValid(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);
            
            // 检查是否过期
            return jsonToken.ValidTo > DateTime.UtcNow;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    public int? GetCurrentUserId()
    {
        return CurrentUser?.Id;
    }

    /// <summary>
    /// 获取当前用户名
    /// </summary>
    public string? GetCurrentUsername()
    {
        return CurrentUser?.Username;
    }

    /// <summary>
    /// 检查是否有特定权限
    /// </summary>
    public bool HasPermission(string permission)
    {
        // 这里可以根据用户角色或权限进行检查
        // 目前简单返回是否已认证
        return IsAuthenticated;
    }

    /// <summary>
    /// 通知认证状态变化
    /// </summary>
    private void NotifyAuthStateChanged()
    {
        AuthStateChanged?.Invoke(IsAuthenticated);
    }
}

/// <summary>
/// 认证状态变化事件参数
/// </summary>
public class AuthStateChangedEventArgs : EventArgs
{
    public bool IsAuthenticated { get; set; }
    public UserProfileDto? User { get; set; }

    public AuthStateChangedEventArgs(bool isAuthenticated, UserProfileDto? user = null)
    {
        IsAuthenticated = isAuthenticated;
        User = user;
    }
}
