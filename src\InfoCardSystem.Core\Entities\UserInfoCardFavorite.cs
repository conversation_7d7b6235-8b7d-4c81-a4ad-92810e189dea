namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户资讯卡收藏实体
/// </summary>
public class UserInfoCardFavorite : BaseEntity
{
    /// <summary>
    /// 资讯卡ID
    /// </summary>
    public int InfoCardId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 资讯卡
    /// </summary>
    public virtual UserInfoCard InfoCard { get; set; } = null!;
    
    /// <summary>
    /// 用户
    /// </summary>
    public virtual AppUser User { get; set; } = null!;
}
