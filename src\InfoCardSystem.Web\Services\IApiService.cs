using InfoCardSystem.Shared.DTOs;

namespace InfoCardSystem.Web.Services;

/// <summary>
/// API服务接口，定义与后端API的通信方法
/// </summary>
public interface IApiService
{
    // 认证相关
    Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginRequestDto request);
    Task<ApiResponse<RegisterResponseDto>> RegisterAsync(RegisterRequestDto request);
    Task<ApiResponse<object>> LogoutAsync();
    Task<ApiResponse<UserProfileDto>> GetCurrentUserAsync();

    // 用户管理
    Task<ApiResponse<UserProfileDto>> GetUserProfileAsync(int userId);
    Task<ApiResponse<UserProfileDto>> UpdateUserProfileAsync(UpdateUserProfileDto request);
    Task<ApiResponse<object>> ChangePasswordAsync(ChangePasswordDto request);

    // 好友管理
    Task<ApiResponse<List<FriendDto>>> GetFriendsAsync();
    Task<ApiResponse<List<FriendRequestDto>>> GetFriendRequestsAsync();
    Task<ApiResponse<object>> SendFriendRequestAsync(SendFriendRequestDto request);
    Task<ApiResponse<object>> AcceptFriendRequestAsync(int requestId);
    Task<ApiResponse<object>> RejectFriendRequestAsync(int requestId);
    Task<ApiResponse<object>> RemoveFriendAsync(int friendId);
    Task<ApiResponse<List<UserSearchResultDto>>> SearchUsersAsync(string query);

    // 资讯卡管理
    Task<ApiResponse<List<InfoCardDto>>> GetInfoCardsAsync(int page = 1, int pageSize = 20);
    Task<ApiResponse<InfoCardDto>> GetInfoCardAsync(int id);
    Task<ApiResponse<InfoCardDto>> CreateInfoCardAsync(CreateInfoCardDto request);
    Task<ApiResponse<InfoCardDto>> UpdateInfoCardAsync(int id, UpdateInfoCardDto request);
    Task<ApiResponse<object>> DeleteInfoCardAsync(int id);
    Task<ApiResponse<object>> ForwardInfoCardAsync(int id, ForwardInfoCardDto request);
    Task<ApiResponse<object>> FavoriteInfoCardAsync(int id);
    Task<ApiResponse<object>> UnfavoriteInfoCardAsync(int id);

    // 群组管理
    Task<ApiResponse<List<GroupDto>>> GetGroupsAsync();
    Task<ApiResponse<GroupDto>> GetGroupAsync(int id);
    Task<ApiResponse<GroupDto>> CreateGroupAsync(CreateGroupDto request);
    Task<ApiResponse<GroupDto>> UpdateGroupAsync(int id, UpdateGroupDto request);
    Task<ApiResponse<object>> DeleteGroupAsync(int id);
    Task<ApiResponse<object>> JoinGroupAsync(int groupId);
    Task<ApiResponse<object>> LeaveGroupAsync(int groupId);
    Task<ApiResponse<List<GroupMemberDto>>> GetGroupMembersAsync(int groupId);

    // 文件上传
    Task<ApiResponse<FileUploadResponseDto>> UploadFileAsync(Stream fileStream, string fileName, string contentType);
}


