// InfoCard系统 - API服务入口点
// 这是InfoCard信息卡片分享系统的后端API服务
// 提供用户管理、好友系统、资讯卡管理等核心功能的RESTful API接口
//
// 主要功能：
// - 用户认证和授权 (JWT)
// - 资讯卡CRUD操作
// - 好友关系管理
// - 文件上传和管理
// - 实时通信支持
// - 数据库操作和缓存
//
// 技术栈：
// - ASP.NET Core 8.0
// - Entity Framework Core
// - MySQL数据库
// - JWT认证
// - Swagger API文档

using System.Text;
using InfoCardSystem.API.Extensions;
using InfoCardSystem.API.Middleware;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Core.Services;
using InfoCardSystem.Infrastructure.Data;
using InfoCardSystem.Infrastructure.Repositories;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

// 创建Web应用程序构建器
var builder = WebApplication.CreateBuilder(args);

// ========================================
// 服务容器配置 - 依赖注入和服务注册
// ========================================

// 数据库配置 - 使用MySQL作为主数据库
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddDbContext<InfoCardDbContext>(options =>
{
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString),
        mySqlOptions =>
        {
            mySqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(5),
                errorNumbersToAdd: null);
        });

    // 开发环境启用敏感数据日志
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// 注册仓储和工作单元
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// 注册业务服务
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IFriendshipService, FriendshipService>();
builder.Services.AddScoped<IInfoCardService, InfoCardService>();
builder.Services.AddScoped<IGroupService, GroupService>();
builder.Services.AddScoped<IAttachmentService, AttachmentService>();
builder.Services.AddScoped<IQRCodeService, QRCodeService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddSingleton<ICacheService, MemoryCacheService>();

// JWT认证配置
var jwtKey = builder.Configuration["Jwt:Key"];
var jwtIssuer = builder.Configuration["Jwt:Issuer"];
var jwtAudience = builder.Configuration["Jwt:Audience"];

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtIssuer,
        ValidAudience = jwtAudience,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey ?? throw new InvalidOperationException("JWT密钥未配置"))),
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// 控制器
builder.Services.AddControllers();

// API文档
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "InfoCard System API",
        Version = "v1.0.0",
        Description = @"
# InfoCard 好友资讯卡分享系统 API

这是一个现代化的社交信息分享平台API，支持用户注册、好友管理、信息卡发布等功能。

## 主要功能
- 🔐 用户认证与授权 (JWT)
- 👥 好友关系管理
- 📝 信息卡发布与管理
- 📁 文件上传与管理
- 👤 用户资料管理

## 认证说明
大部分API需要JWT Bearer Token认证。请先通过登录接口获取token，然后在请求头中添加：
```
Authorization: Bearer {your_token}
```

## 错误码说明
- AUTH_001: 认证失败
- USER_001: 用户不存在
- USER_002: 用户已存在
- VALID_001: 参数验证失败
- SERVER_001: 服务器内部错误

## 联系信息
- 开发团队: InfoCard Team
- 技术支持: 本地开发环境
",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "InfoCard Team",
            Email = "dev@localhost",
            Url = new Uri("http://localhost:8082")
        },
        License = new Microsoft.OpenApi.Models.OpenApiLicense
        {
            Name = "MIT License",
            Url = new Uri("https://opensource.org/licenses/MIT")
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme.
                      Enter 'Bearer' [space] and then your token in the text input below.
                      Example: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement()
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = Microsoft.OpenApi.Models.ParameterLocation.Header,
            },
            new List<string>()
        }
    });

    // 包含XML注释
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // 添加示例
    c.EnableAnnotations();
});

// CORS配置
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 添加健康检查
builder.Services.AddApplicationHealthChecks(builder.Configuration);

// 添加内存缓存
builder.Services.AddMemoryCache();

// 添加响应缓存
builder.Services.AddResponseCaching();

// 添加响应压缩
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<Microsoft.AspNetCore.ResponseCompression.BrotliCompressionProvider>();
    options.Providers.Add<Microsoft.AspNetCore.ResponseCompression.GzipCompressionProvider>();
});

var app = builder.Build();

// Configure the HTTP request pipeline.

// 安全头中间件（最先执行）
app.UseSecurityHeaders();

// 响应压缩
app.UseResponseCompression();

// 响应缓存
app.UseResponseCaching();

// 全局异常处理
app.UseGlobalExceptionHandling();

// 请求日志记录
app.UseRequestLogging();

// 性能监控
app.UsePerformanceMonitoring();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "InfoCard System API v1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}
else
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "InfoCard System API v1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
    });
}

    app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

// 配置健康检查端点
app.UseApplicationHealthChecks();

app.MapControllers();

// 健康检查端点
app.MapGet("/health", () => Results.Ok(new {
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Version = "1.0.0"
}));

app.Run();
