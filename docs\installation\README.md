# InfoCard 系统安装指南

本指南将帮助您完成 InfoCard 系统的完整安装和配置。

## 📋 安装概述

InfoCard 系统支持多种安装方式：
- **快速安装**: 适合快速体验和开发环境
- **标准安装**: 适合生产环境部署
- **Docker安装**: 适合容器化部署
- **开发环境**: 适合开发人员本地开发

## 🔧 环境要求

### 基础环境
- **操作系统**: Windows 10/11, Windows Server 2019/2022, Linux (Ubuntu 20.04+)
- **.NET Runtime**: .NET 8.0 或更高版本
- **数据库**: MySQL 8.0+ 或 SQL Server 2019+
- **Web服务器**: IIS 10+ 或 Nginx 1.18+

### 硬件要求
| 环境类型 | CPU | 内存 | 存储 | 网络 |
|----------|-----|------|------|------|
| 开发环境 | 2核+ | 4GB+ | 20GB+ | 10Mbps+ |
| 测试环境 | 2核+ | 8GB+ | 50GB+ | 100Mbps+ |
| 生产环境 | 4核+ | 16GB+ | 100GB+ | 1Gbps+ |

### 软件依赖
- **Node.js**: 18.0+ (前端构建)
- **Git**: 2.30+ (代码管理)
- **Visual Studio**: 2022 17.8+ (开发环境)

## 🚀 快速安装

### 1. 下载源码
```bash
# 克隆项目
git clone https://github.com/your-org/InfoCardSystem.git
cd InfoCardSystem
```

### 2. 配置数据库
```sql
-- 创建数据库
CREATE DATABASE InfoCardDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'infocard'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON InfoCardDB.* TO 'infocard'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 配置连接字符串
编辑 `src/InfoCardSystem.API/appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=InfoCardDB;Uid=infocard;Pwd=your_password;CharSet=utf8mb4;"
  }
}
```

### 4. 运行数据库迁移
```bash
cd src/InfoCardSystem.API
dotnet ef database update
```

### 5. 启动服务
```bash
# 启动API服务
cd src/InfoCardSystem.API
dotnet run

# 启动Web应用 (新终端)
cd src/InfoCardSystem.Web
dotnet run
```

### 6. 访问系统
- **Web应用**: http://localhost:5000
- **API文档**: http://localhost:5001/swagger
- **健康检查**: http://localhost:5001/health

## 📦 标准安装

### 1. 环境准备
```bash
# 安装 .NET 8.0 SDK
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-sdk-8.0

# 安装 MySQL
sudo apt-get install -y mysql-server
sudo mysql_secure_installation
```

### 2. 配置IIS (Windows)
```powershell
# 启用IIS功能
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-ASPNET45

# 安装 .NET Core Hosting Bundle
# 下载并安装: https://dotnet.microsoft.com/download/dotnet/8.0
```

### 3. 部署应用
```bash
# 发布API
cd src/InfoCardSystem.API
dotnet publish -c Release -o ./publish

# 发布Web应用
cd src/InfoCardSystem.Web
dotnet publish -c Release -o ./publish
```

### 4. 配置IIS站点
```xml
<!-- web.config -->
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
    </handlers>
    <aspNetCore processPath="dotnet" arguments=".\InfoCardSystem.API.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" />
  </system.webServer>
</configuration>
```

## 🐳 Docker安装

### 1. 准备Docker环境
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: InfoCardDB
      MYSQL_USER: infocard
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  api:
    build:
      context: ./src/InfoCardSystem.API
      dockerfile: Dockerfile
    ports:
      - "5001:80"
    depends_on:
      - mysql
    environment:
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=InfoCardDB;Uid=infocard;Pwd=password;

  web:
    build:
      context: ./src/InfoCardSystem.Web
      dockerfile: Dockerfile
    ports:
      - "5000:80"
    depends_on:
      - api

volumes:
  mysql_data:
```

### 3. 启动服务
```bash
# 构建并启动
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🛠️ 开发环境安装

### 1. 安装开发工具
- **Visual Studio 2022**: 包含 .NET MAUI 工作负载
- **Visual Studio Code**: 包含 C# 扩展
- **MySQL Workbench**: 数据库管理工具
- **Postman**: API测试工具

### 2. 配置开发环境
```bash
# 克隆项目
git clone https://github.com/your-org/InfoCardSystem.git
cd InfoCardSystem

# 还原NuGet包
dotnet restore

# 构建解决方案
dotnet build
```

### 3. 配置调试环境
```json
// launchSettings.json
{
  "profiles": {
    "InfoCardSystem.API": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

## ✅ 安装验证

### 1. 系统健康检查
```bash
# 检查API服务
curl http://localhost:5001/health

# 检查数据库连接
curl http://localhost:5001/health/db

# 检查依赖服务
curl http://localhost:5001/health/dependencies
```

### 2. 功能测试
```bash
# 用户注册测试
curl -X POST http://localhost:5001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"Test123!"}'

# 用户登录测试
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

### 3. 性能测试
```bash
# 使用Apache Bench进行简单性能测试
ab -n 1000 -c 10 http://localhost:5001/api/health
```

## 🔧 配置说明

### 应用配置
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=InfoCardDB;Uid=infocard;Pwd=password;"
  },
  "JwtSettings": {
    "SecretKey": "your-secret-key-here",
    "Issuer": "InfoCardSystem",
    "Audience": "InfoCardUsers",
    "ExpirationMinutes": 60
  },
  "FileUpload": {
    "MaxFileSize": 10485760,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"],
    "UploadPath": "wwwroot/uploads"
  }
}
```

### 数据库配置
```sql
-- 优化配置
SET GLOBAL innodb_buffer_pool_size = **********;
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;
```

## 🚨 常见问题

### 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep :3306

# 测试连接
mysql -h localhost -u infocard -p InfoCardDB
```

### 端口冲突
```bash
# 查看端口占用
netstat -tlnp | grep :5000
netstat -tlnp | grep :5001

# 修改端口配置
# 编辑 launchSettings.json 或 appsettings.json
```

### 权限问题
```bash
# 设置文件权限
sudo chown -R www-data:www-data /var/www/infocard
sudo chmod -R 755 /var/www/infocard

# 设置上传目录权限
sudo chmod -R 777 /var/www/infocard/wwwroot/uploads
```

## 📞 获取帮助

如果在安装过程中遇到问题，请：

1. 查看 [故障排除指南](troubleshooting.md)
2. 检查 [常见问题](../user-guide/faq.md)
3. 提交 [GitHub Issue](https://github.com/your-org/InfoCardSystem/issues)
4. 联系技术支持: <EMAIL>

## 📚 下一步

安装完成后，您可以：
- 阅读 [用户指南](../user-guide/README.md) 了解系统功能
- 查看 [API文档](../api/README.md) 进行二次开发
- 参考 [部署指南](../deployment/README.md) 部署到生产环境
