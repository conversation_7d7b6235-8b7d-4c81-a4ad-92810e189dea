{"version": "2.0.0", "tasks": [{"label": "build-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/InfoCardSystem.API/InfoCardSystem.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-web", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/InfoCardSystem.Web/InfoCardSystem.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-all", "dependsOrder": "parallel", "dependsOn": ["build-api", "build-web"]}, {"label": "launch-web", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/src/InfoCardSystem.Web/InfoCardSystem.Web.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:8002;http://localhost:8002"}}, "isBackground": true, "problemMatcher": {"owner": "dotnet", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error)\\s+(\\w+):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "code": 5, "message": 6}, "background": {"activeOnStart": true, "beginsPattern": "^.*Now listening on:.*$", "endsPattern": "^.*Application started.*$"}}}, {"label": "watch-api", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/InfoCardSystem.API/InfoCardSystem.API.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "isBackground": true}, {"label": "watch-web", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/InfoCardSystem.Web/InfoCardSystem.Web.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "isBackground": true}]}