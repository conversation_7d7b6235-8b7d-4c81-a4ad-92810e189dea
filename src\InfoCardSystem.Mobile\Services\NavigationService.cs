using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 导航服务实现
/// </summary>
public class NavigationService : INavigationService
{
    private readonly ILogger<NavigationService> _logger;

    public NavigationService(ILogger<NavigationService> logger)
    {
        _logger = logger;
    }

    public async Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null)
    {
        try
        {
            _logger.LogDebug("导航到: {Route}", route);
            
            if (parameters != null && parameters.Any())
            {
                await Shell.Current.GoToAsync(route, parameters);
            }
            else
            {
                await Shell.Current.GoToAsync(route);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导航失败: {Route}", route);
            throw;
        }
    }

    public async Task GoBackAsync()
    {
        try
        {
            _logger.LogDebug("返回上一页");
            await Shell.Current.GoToAsync("..");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "返回失败");
            throw;
        }
    }

    public async Task NavigateToRootAsync()
    {
        try
        {
            _logger.LogDebug("导航到根页面");
            await Shell.Current.GoToAsync("//");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导航到根页面失败");
            throw;
        }
    }

    public async Task PopToRootAsync()
    {
        try
        {
            _logger.LogDebug("弹出到根页面");
            await Shell.Current.Navigation.PopToRootAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "弹出到根页面失败");
            throw;
        }
    }

    public string GetCurrentRoute()
    {
        try
        {
            return Shell.Current.CurrentState.Location.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前路由失败");
            return string.Empty;
        }
    }
}
