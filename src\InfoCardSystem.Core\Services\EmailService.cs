using InfoCardSystem.Core.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 邮件服务实现
/// </summary>
public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<bool> SendPasswordResetEmailAsync(string email, string resetToken, string userName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("发送密码重置邮件: {Email}", email);

            var subject = "InfoCard - 密码重置";
            var resetUrl = $"{_configuration["App:BaseUrl"]}/reset-password?token={resetToken}";
            
            var content = $@"
                <html>
                <body>
                    <h2>密码重置</h2>
                    <p>亲爱的 {userName}，</p>
                    <p>您请求重置InfoCard账户的密码。请点击下面的链接重置您的密码：</p>
                    <p><a href=""{resetUrl}"" style=""background-color: #1976D2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;"">重置密码</a></p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p>{resetUrl}</p>
                    <p>此链接将在1小时后失效。</p>
                    <p>如果您没有请求重置密码，请忽略此邮件。</p>
                    <br>
                    <p>InfoCard团队</p>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, content, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送密码重置邮件失败: {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendVerificationEmailAsync(string email, string verificationCode, string userName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("发送验证邮件: {Email}", email);

            var subject = "InfoCard - 邮箱验证";
            var content = $@"
                <html>
                <body>
                    <h2>邮箱验证</h2>
                    <p>亲爱的 {userName}，</p>
                    <p>感谢您注册InfoCard！请使用以下验证码完成邮箱验证：</p>
                    <h3 style=""background-color: #f5f5f5; padding: 10px; text-align: center; letter-spacing: 5px;"">{verificationCode}</h3>
                    <p>验证码将在10分钟后失效。</p>
                    <p>如果您没有注册InfoCard账户，请忽略此邮件。</p>
                    <br>
                    <p>InfoCard团队</p>
                </body>
                </html>";

            return await SendEmailAsync(email, subject, content, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送验证邮件失败: {Email}", email);
            return false;
        }
    }

    public async Task<bool> SendNotificationEmailAsync(string email, string subject, string content, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("发送通知邮件: {Email}, Subject: {Subject}", email, subject);
            return await SendEmailAsync(email, subject, content, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送通知邮件失败: {Email}", email);
            return false;
        }
    }

    private async Task<bool> SendEmailAsync(string toEmail, string subject, string content, CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取SMTP配置
            var smtpHost = _configuration["Email:SmtpHost"];
            var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
            var smtpUsername = _configuration["Email:Username"];
            var smtpPassword = _configuration["Email:Password"];
            var fromEmail = _configuration["Email:FromEmail"];
            var fromName = _configuration["Email:FromName"] ?? "InfoCard";

            if (string.IsNullOrEmpty(smtpHost) || string.IsNullOrEmpty(smtpUsername) || 
                string.IsNullOrEmpty(smtpPassword) || string.IsNullOrEmpty(fromEmail))
            {
                _logger.LogWarning("邮件配置不完整，跳过发送邮件");
                return false;
            }

            using var client = new SmtpClient(smtpHost, smtpPort);
            client.EnableSsl = true;
            client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);

            var message = new MailMessage();
            message.From = new MailAddress(fromEmail, fromName);
            message.To.Add(toEmail);
            message.Subject = subject;
            message.Body = content;
            message.IsBodyHtml = true;

            await client.SendMailAsync(message, cancellationToken);
            
            _logger.LogInformation("邮件发送成功: {Email}", toEmail);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送邮件失败: {Email}", toEmail);
            return false;
        }
    }
}
