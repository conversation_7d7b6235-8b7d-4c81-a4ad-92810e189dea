using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 用户登录请求
/// </summary>
public class LoginRequestDto
{
    /// <summary>
    /// 登录类型
    /// </summary>
    [Required(ErrorMessage = "登录类型不能为空")]
    public LoginType LoginType { get; set; } = LoginType.Email;

    /// <summary>
    /// 邮箱（当LoginType为Email时必填）
    /// </summary>
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    public string? Email { get; set; }

    /// <summary>
    /// 手机号（当LoginType为Phone时必填）
    /// </summary>
    [Phone(ErrorMessage = "手机号格式不正确")]
    public string? Phone { get; set; }

    /// <summary>
    /// 自定义用户ID（当LoginType为UserId时必填）
    /// </summary>
    public string? CustomUserId { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 记住我
    /// </summary>
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// 登录类型枚举
/// </summary>
public enum LoginType
{
    /// <summary>
    /// 邮箱登录
    /// </summary>
    Email = 1,

    /// <summary>
    /// 手机号登录
    /// </summary>
    Phone = 2,

    /// <summary>
    /// 自定义用户ID登录
    /// </summary>
    UserId = 3
}

/// <summary>
/// 登录响应
/// </summary>
public class LoginResponseDto
{
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string Token { get; set; } = string.Empty;
    
    /// <summary>
    /// 令牌类型
    /// </summary>
    public string TokenType { get; set; } = "Bearer";
    
    /// <summary>
    /// 过期时间（秒）
    /// </summary>
    public int ExpiresIn { get; set; }
    
    /// <summary>
    /// 用户信息
    /// </summary>
    public UserProfileDto User { get; set; } = null!;
}

/// <summary>
/// 注册请求
/// </summary>
public class RegisterRequestDto
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "用户名长度必须在2-100个字符之间")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    [Required(ErrorMessage = "邮箱不能为空")]
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    [StringLength(255, ErrorMessage = "邮箱长度不能超过255个字符")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 确认密码
    /// </summary>
    [Required(ErrorMessage = "确认密码不能为空")]
    [Compare("Password", ErrorMessage = "两次输入的密码不一致")]
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// 手机号（可选）
    /// </summary>
    [Phone(ErrorMessage = "手机号格式不正确")]
    [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
    public string? Phone { get; set; }

    /// <summary>
    /// 自定义用户ID（可选）
    /// </summary>
    [StringLength(50, MinimumLength = 3, ErrorMessage = "自定义用户ID长度必须在3-50个字符之间")]
    [RegularExpression(@"^[a-zA-Z0-9_-]+$", ErrorMessage = "自定义用户ID只能包含字母、数字、下划线和连字符")]
    public string? CustomUserId { get; set; }

    /// <summary>
    /// 显示名称（可选）
    /// </summary>
    [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
    public string? DisplayName { get; set; }

    /// <summary>
    /// 个人简介（可选）
    /// </summary>
    [StringLength(1000, ErrorMessage = "个人简介长度不能超过1000个字符")]
    public string? Bio { get; set; }
}

/// <summary>
/// 注册响应
/// </summary>
public class RegisterResponseDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
