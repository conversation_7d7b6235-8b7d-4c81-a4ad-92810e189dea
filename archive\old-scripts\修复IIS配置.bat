@echo off
title IIS配置修复工具
color 0E

echo ========================================
echo   InfoCard IIS配置修复工具
echo ========================================
echo.

echo [INFO] 诊断IIS配置问题...
echo.

echo ========================================
echo 步骤 1: 检查ASP.NET Core模块
echo ========================================
if exist "C:\Windows\System32\inetsrv\aspnetcorev2.dll" (
    echo ✅ ASP.NET Core模块V2已安装
) else (
    echo ❌ ASP.NET Core模块V2未安装
    echo.
    echo 🔧 解决方案选项:
    echo    1. 下载并安装 .NET 8.0 Hosting Bundle
    echo    2. 使用反向代理模式 (推荐)
    echo.
    set /p choice="选择解决方案 (1=安装模块, 2=反向代理): "
    
    if "!choice!"=="1" (
        echo.
        echo 📥 请手动下载并安装 .NET 8.0 Hosting Bundle:
        echo    https://dotnet.microsoft.com/download/dotnet/8.0
        echo    选择 "Hosting Bundle" 下载
        echo.
        echo 安装完成后重新运行此脚本
        pause
        exit /b 1
    ) else (
        echo.
        echo 🔄 配置反向代理模式...
        goto :configure_reverse_proxy
    )
)

echo ========================================
echo 步骤 2: 配置标准ASP.NET Core模式
echo ========================================
goto :configure_aspnetcore

:configure_reverse_proxy
echo 创建反向代理配置...

echo 创建API反向代理web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<rewrite^>
echo       ^<rules^>
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^>
echo           ^<match url="(.*)" /^>
echo           ^<action type="Rewrite" url="http://localhost:5000/{R:1}" /^>
echo         ^</rule^>
echo       ^</rules^>
echo     ^</rewrite^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo 创建Web反向代理web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<rewrite^>
echo       ^<rules^>
echo         ^<rule name="ReverseProxyInboundRule1" stopProcessing="true"^>
echo           ^<match url="(.*)" /^>
echo           ^<action type="Rewrite" url="http://localhost:7000/{R:1}" /^>
echo         ^</rule^>
echo       ^</rules^>
echo     ^</rewrite^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo ✅ 反向代理配置完成
echo.
echo 📋 注意事项:
echo    - 需要确保开发环境服务正在运行
echo    - API服务: http://localhost:5000
echo    - Web服务: http://localhost:7000
echo    - IIS将请求转发到这些端口
echo.
goto :test_configuration

:configure_aspnetcore
echo 创建标准ASP.NET Core配置...

echo 创建API标准web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath="dotnet" 
echo                   arguments=".\InfoCardSystem.API.dll" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo 创建Web标准web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath="dotnet" 
echo                   arguments=".\InfoCardSystem.Web.dll" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo ✅ 标准ASP.NET Core配置完成

:test_configuration
echo.
echo ========================================
echo 步骤 3: 创建日志目录和设置权限
echo ========================================
if not exist "C:\Webs\ICAPI\logs" mkdir "C:\Webs\ICAPI\logs"
if not exist "C:\Webs\ICWeb\logs" mkdir "C:\Webs\ICWeb\logs"

echo 设置目录权限...
icacls "C:\Webs\ICAPI" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "C:\Webs\ICWeb" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "C:\Webs\ICAPI\logs" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1
icacls "C:\Webs\ICWeb\logs" /grant "Everyone:(OI)(CI)F" /T >nul 2>&1

echo ✅ 权限设置完成

echo.
echo ========================================
echo 步骤 4: 测试配置
echo ========================================
echo 等待5秒后测试...
timeout /t 5 /nobreak >nul

echo 测试API (8081端口)...
curl -I http://localhost:8081 2>nul | findstr "HTTP" >nul
if %errorLevel% equ 0 (
    echo ✅ API响应正常
) else (
    echo ⚠️  API可能需要更多时间启动
)

echo 测试Web (8082端口)...
curl -I http://localhost:8082 2>nul | findstr "HTTP" >nul
if %errorLevel% equ 0 (
    echo ✅ Web响应正常
) else (
    echo ⚠️  Web可能需要更多时间启动
)

echo.
echo ========================================
echo 🎉 配置完成！
echo ========================================
echo.
echo 📱 访问地址:
echo    🔧 API: http://localhost:8081
echo    🌐 Web: http://localhost:8082
echo.
echo 🔍 故障排除:
echo    📁 日志位置: C:\Webs\ICAPI\logs 和 C:\Webs\ICWeb\logs
echo    🔄 如果仍有问题，请检查开发环境是否运行 (反向代理模式)
echo    📋 或安装 .NET 8.0 Hosting Bundle (标准模式)
echo.

set /p choice="按任意键打开测试页面..."
start http://localhost:8081
start http://localhost:8082

echo.
pause
