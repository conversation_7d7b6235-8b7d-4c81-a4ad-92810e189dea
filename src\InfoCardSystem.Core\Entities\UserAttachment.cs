namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户附件实体
/// </summary>
public class UserAttachment : BaseEntity
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 原始文件名
    /// </summary>
    public string OriginalFileName { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }
    
    /// <summary>
    /// MIME类型
    /// </summary>
    public string MimeType { get; set; } = string.Empty;
    
    /// <summary>
    /// 上传者ID
    /// </summary>
    public int UploaderId { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 上传者
    /// </summary>
    public virtual AppUser Uploader { get; set; } = null!;
    
    /// <summary>
    /// 资讯卡附件关联
    /// </summary>
    public virtual ICollection<UserInfoCardAttachment> InfoCardAttachments { get; set; } = new List<UserInfoCardAttachment>();
}
