#!/bin/bash

# InfoCard系统构建脚本
# 用于构建所有项目组件，包括API、Web、移动端等
# 
# 使用方法:
#   ./scripts/build.sh [选项]
#
# 选项:
#   --clean     清理构建输出
#   --release   发布模式构建
#   --test      运行测试
#   --all       构建所有项目

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    # 检查 .NET SDK
    if ! command -v dotnet &> /dev/null; then
        log_error ".NET SDK 未安装，请先安装 .NET 8.0 SDK"
        exit 1
    fi
    
    # 检查 Node.js (用于前端构建)
    if ! command -v node &> /dev/null; then
        log_warning "Node.js 未安装，将跳过前端构建"
    fi
    
    log_success "依赖检查完成"
}

# 清理构建输出
clean_build() {
    log_info "清理构建输出..."
    
    # 清理 .NET 项目
    find . -name "bin" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "obj" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "publish" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # 清理前端项目
    find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "清理完成"
}

# 还原NuGet包
restore_packages() {
    log_info "还原NuGet包..."
    dotnet restore src/InfoCardSystem.sln
    log_success "包还原完成"
}

# 构建API项目
build_api() {
    log_info "构建API项目..."
    
    cd src/InfoCardSystem.API
    
    if [ "$BUILD_MODE" = "Release" ]; then
        dotnet build -c Release --no-restore
        log_info "发布API项目..."
        dotnet publish -c Release -o ../../publish/api --no-build
    else
        dotnet build -c Debug --no-restore
    fi
    
    cd ../..
    log_success "API项目构建完成"
}

# 构建Web项目
build_web() {
    log_info "构建Web项目..."
    
    cd src/InfoCardSystem.Web
    
    if [ "$BUILD_MODE" = "Release" ]; then
        dotnet build -c Release --no-restore
        log_info "发布Web项目..."
        dotnet publish -c Release -o ../../publish/web --no-build
    else
        dotnet build -c Debug --no-restore
    fi
    
    cd ../..
    log_success "Web项目构建完成"
}

# 构建移动端项目
build_mobile() {
    log_info "构建移动端项目..."
    
    cd src/InfoCardSystem.Mobile
    
    if [ "$BUILD_MODE" = "Release" ]; then
        # Android构建
        if command -v dotnet &> /dev/null; then
            log_info "构建Android应用..."
            dotnet build -c Release -f net8.0-android --no-restore
        fi
    else
        dotnet build -c Debug --no-restore
    fi
    
    cd ../..
    log_success "移动端项目构建完成"
}

# 构建微信小程序
build_miniprogram() {
    log_info "构建微信小程序..."
    
    cd src/InfoCardSystem.MiniProgram
    
    # 检查是否有微信开发者工具CLI
    if command -v cli &> /dev/null; then
        cli build --project ./ --output ../../publish/miniprogram
    else
        log_warning "微信开发者工具CLI未安装，请手动构建小程序"
        # 复制源码到发布目录
        mkdir -p ../../publish/miniprogram
        cp -r ./* ../../publish/miniprogram/
    fi
    
    cd ../..
    log_success "微信小程序构建完成"
}

# 构建快应用
build_quickapp() {
    log_info "构建快应用..."
    
    cd src/InfoCardSystem.QuickApp
    
    # 检查是否有快应用工具链
    if command -v hap &> /dev/null; then
        hap build
        mkdir -p ../../publish/quickapp
        cp -r ./dist/* ../../publish/quickapp/
    else
        log_warning "快应用工具链未安装，请手动构建快应用"
        # 复制源码到发布目录
        mkdir -p ../../publish/quickapp
        cp -r ./* ../../publish/quickapp/
    fi
    
    cd ../..
    log_success "快应用构建完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 运行单元测试
    if [ -d "tests" ]; then
        dotnet test tests/ --no-build --verbosity normal
    else
        log_warning "未找到测试项目"
    fi
    
    log_success "测试完成"
}

# 生成构建报告
generate_report() {
    log_info "生成构建报告..."
    
    REPORT_FILE="build-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
InfoCard系统构建报告
==================

构建时间: $(date)
构建模式: $BUILD_MODE
构建版本: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

构建组件:
- API项目: ✓
- Web项目: ✓
- 移动端项目: ✓
- 微信小程序: ✓
- 快应用: ✓

发布目录:
$(ls -la publish/ 2>/dev/null || echo "无发布输出")

构建日志已保存到: $REPORT_FILE
EOF

    log_success "构建报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始构建InfoCard系统..."
    
    # 默认参数
    BUILD_MODE="Debug"
    CLEAN_BUILD=false
    RUN_TESTS=false
    BUILD_ALL=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            --release)
                BUILD_MODE="Release"
                shift
                ;;
            --test)
                RUN_TESTS=true
                shift
                ;;
            --all)
                BUILD_ALL=true
                shift
                ;;
            -h|--help)
                echo "InfoCard系统构建脚本"
                echo ""
                echo "使用方法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --clean     清理构建输出"
                echo "  --release   发布模式构建"
                echo "  --test      运行测试"
                echo "  --all       构建所有项目"
                echo "  -h, --help  显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行构建流程
    check_dependencies
    
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    restore_packages
    
    # 构建项目
    build_api
    build_web
    
    if [ "$BUILD_ALL" = true ]; then
        build_mobile
        build_miniprogram
        build_quickapp
    fi
    
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi
    
    if [ "$BUILD_MODE" = "Release" ]; then
        generate_report
    fi
    
    log_success "InfoCard系统构建完成！"
    
    if [ "$BUILD_MODE" = "Release" ]; then
        log_info "发布文件位置: ./publish/"
        log_info "可以使用 ./scripts/deploy.sh 进行部署"
    fi
}

# 执行主函数
main "$@"
