using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户实体配置
/// </summary>
public class AppUserConfiguration : IEntityTypeConfiguration<AppUser>
{
    public void Configure(EntityTypeBuilder<AppUser> builder)
    {
        // 表名
        builder.ToTable("app_users");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.CustomUserId)
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(x => x.Username)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(x => x.Email)
            .HasMaxLength(255)
            .IsRequired();
            
        builder.Property(x => x.Phone)
            .HasMaxLength(20);
            
        builder.Property(x => x.PasswordHash)
            .HasMaxLength(255)
            .IsRequired();
            
        builder.Property(x => x.AvatarUrl)
            .HasMaxLength(500);
            
        builder.Property(x => x.Bio)
            .HasMaxLength(1000);
            
        builder.Property(x => x.UserStatus)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => x.CustomUserId)
            .IsUnique()
            .HasDatabaseName("idx_users_custom_user_id");
            
        builder.HasIndex(x => x.Email)
            .IsUnique()
            .HasDatabaseName("idx_users_email");
            
        builder.HasIndex(x => x.Phone)
            .IsUnique()
            .HasDatabaseName("idx_users_phone");
            
        builder.HasIndex(x => x.Username)
            .HasDatabaseName("idx_users_username");
        
        // 关系配置
        builder.HasMany(x => x.InitiatedFriendships)
            .WithOne(x => x.User)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.ReceivedFriendships)
            .WithOne(x => x.Friend)
            .HasForeignKey(x => x.FriendId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasMany(x => x.CreatedGroups)
            .WithOne(x => x.Creator)
            .HasForeignKey(x => x.CreatorId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.GroupMemberships)
            .WithOne(x => x.User)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.OriginalInfoCards)
            .WithOne(x => x.OriginalPublisher)
            .HasForeignKey(x => x.OriginalPublisherId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.DirectInfoCards)
            .WithOne(x => x.DirectPublisher)
            .HasForeignKey(x => x.DirectPublisherId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasMany(x => x.FavoriteInfoCards)
            .WithOne(x => x.User)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.UploadedAttachments)
            .WithOne(x => x.Uploader)
            .HasForeignKey(x => x.UploaderId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.BlockedUsers)
            .WithOne(x => x.User)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.BlockedByUsers)
            .WithOne(x => x.BlockedUser)
            .HasForeignKey(x => x.BlockedUserId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
