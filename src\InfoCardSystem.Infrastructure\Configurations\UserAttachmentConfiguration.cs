using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户附件实体配置
/// </summary>
public class UserAttachmentConfiguration : IEntityTypeConfiguration<UserAttachment>
{
    public void Configure(EntityTypeBuilder<UserAttachment> builder)
    {
        // 表名
        builder.ToTable("user_attachments");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.FileName)
            .HasMaxLength(255)
            .IsRequired();
            
        builder.Property(x => x.OriginalFileName)
            .HasMaxLength(255)
            .IsRequired();
            
        builder.Property(x => x.FilePath)
            .HasMaxLength(500)
            .IsRequired();
            
        builder.Property(x => x.FileSize)
            .IsRequired();
            
        builder.Property(x => x.MimeType)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(x => x.UploaderId)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => x.UploaderId)
            .HasDatabaseName("idx_attachments_uploader");
            
        builder.HasIndex(x => x.FileName)
            .HasDatabaseName("idx_attachments_filename");
            
        builder.HasIndex(x => x.CreatedAt)
            .HasDatabaseName("idx_attachments_created");
        
        // 关系配置
        builder.HasMany(x => x.InfoCardAttachments)
            .WithOne(x => x.Attachment)
            .HasForeignKey(x => x.AttachmentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
