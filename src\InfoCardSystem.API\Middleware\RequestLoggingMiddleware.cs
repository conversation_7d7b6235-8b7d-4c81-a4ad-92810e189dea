using System.Diagnostics;
using System.Text;

namespace InfoCardSystem.API.Middleware;

/// <summary>
/// 请求日志中间件
/// </summary>
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString("N")[..8];
        
        // 添加请求ID到响应头
        context.Response.Headers["X-Request-ID"] = requestId;
        
        // 记录请求开始
        await LogRequestAsync(context, requestId);
        
        // 保存原始响应流
        var originalBodyStream = context.Response.Body;
        
        try
        {
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;
            
            await _next(context);
            
            stopwatch.Stop();
            
            // 记录响应
            await LogResponseAsync(context, requestId, stopwatch.ElapsedMilliseconds);
            
            // 复制响应到原始流
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        finally
        {
            context.Response.Body = originalBodyStream;
        }
    }

    private async Task LogRequestAsync(HttpContext context, string requestId)
    {
        var request = context.Request;
        var logData = new
        {
            RequestId = requestId,
            Method = request.Method,
            Path = request.Path.Value,
            QueryString = request.QueryString.Value,
            UserAgent = request.Headers.UserAgent.ToString(),
            RemoteIP = GetClientIP(context),
            UserId = GetUserId(context),
            Timestamp = DateTime.UtcNow
        };

        _logger.LogInformation("请求开始: {@RequestData}", logData);

        // 记录请求体（仅对POST/PUT请求，且非文件上传）
        if ((request.Method == "POST" || request.Method == "PUT") && 
            request.ContentType?.Contains("application/json") == true)
        {
            request.EnableBuffering();
            var body = await ReadRequestBodyAsync(request);
            if (!string.IsNullOrEmpty(body))
            {
                _logger.LogDebug("请求体 [{RequestId}]: {RequestBody}", requestId, body);
            }
            request.Body.Position = 0;
        }
    }

    private async Task LogResponseAsync(HttpContext context, string requestId, long elapsedMs)
    {
        var response = context.Response;
        var logData = new
        {
            RequestId = requestId,
            StatusCode = response.StatusCode,
            ElapsedMs = elapsedMs,
            ContentType = response.ContentType,
            ContentLength = response.ContentLength,
            Timestamp = DateTime.UtcNow
        };

        var logLevel = response.StatusCode >= 400 ? LogLevel.Warning : LogLevel.Information;
        _logger.Log(logLevel, "请求完成: {@ResponseData}", logData);

        // 记录响应体（仅对错误响应）
        if (response.StatusCode >= 400)
        {
            var responseBody = await ReadResponseBodyAsync(context.Response);
            if (!string.IsNullOrEmpty(responseBody))
            {
                _logger.LogWarning("错误响应体 [{RequestId}]: {ResponseBody}", requestId, responseBody);
            }
        }
    }

    private static async Task<string> ReadRequestBodyAsync(HttpRequest request)
    {
        try
        {
            using var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true);
            return await reader.ReadToEndAsync();
        }
        catch
        {
            return string.Empty;
        }
    }

    private static async Task<string> ReadResponseBodyAsync(HttpResponse response)
    {
        try
        {
            response.Body.Seek(0, SeekOrigin.Begin);
            using var reader = new StreamReader(response.Body, Encoding.UTF8, leaveOpen: true);
            var content = await reader.ReadToEndAsync();
            response.Body.Seek(0, SeekOrigin.Begin);
            return content;
        }
        catch
        {
            return string.Empty;
        }
    }

    private static string GetClientIP(HttpContext context)
    {
        var ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (string.IsNullOrEmpty(ip))
        {
            ip = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        }
        if (string.IsNullOrEmpty(ip))
        {
            ip = context.Connection.RemoteIpAddress?.ToString();
        }
        return ip ?? "Unknown";
    }

    private static string? GetUserId(HttpContext context)
    {
        return context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
    }
}

/// <summary>
/// 请求日志中间件扩展
/// </summary>
public static class RequestLoggingMiddlewareExtensions
{
    public static IApplicationBuilder UseRequestLogging(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RequestLoggingMiddleware>();
    }
}
