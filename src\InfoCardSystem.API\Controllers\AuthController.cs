using InfoCardSystem.Core.DTOs.Auth;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 认证控制器 - 处理用户注册、登录等认证相关操作
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[SwaggerTag("用户认证相关接口，包括注册、登录、令牌刷新等功能")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    /// <remarks>
    /// 创建新用户账户。注册成功后会自动返回JWT令牌，可直接用于后续API调用。
    ///
    /// 示例请求：
    /// ```json
    /// {
    ///   "username": "johndoe",
    ///   "email": "<EMAIL>",
    ///   "password": "Password123!",
    ///   "confirmPassword": "Password123!",
    ///   "phone": "13800138000",
    ///   "displayName": "John Doe",
    ///   "bio": "Hello, I'm John!"
    /// }
    /// ```
    ///
    /// 密码要求：
    /// - 长度6-100个字符
    /// - 包含大小写字母、数字和特殊字符
    ///
    /// 注意事项：
    /// - 用户名和邮箱必须唯一
    /// - 手机号可选但如果提供也必须唯一
    /// - 自定义用户ID可选，如不提供会自动生成
    /// </remarks>
    /// <response code="200">注册成功，返回用户信息和JWT令牌</response>
    /// <response code="400">请求参数验证失败或用户已存在</response>
    /// <response code="500">服务器内部错误</response>
    [HttpPost("register")]
    [SwaggerOperation(
        Summary = "用户注册",
        Description = "创建新用户账户，注册成功后返回JWT令牌",
        OperationId = "RegisterUser",
        Tags = new[] { "Authentication" }
    )]
    [SwaggerResponse(200, "注册成功", typeof(ApiResponse<AuthResponse>))]
    [SwaggerResponse(400, "请求参数验证失败或用户已存在", typeof(ApiResponse<AuthResponse>))]
    [SwaggerResponse(500, "服务器内部错误", typeof(ApiResponse<AuthResponse>))]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Register(
        [FromBody] RegisterRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _authService.RegisterAsync(request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证响应</returns>
    /// <remarks>
    /// 用户身份验证，支持多种登录方式。登录成功后返回JWT令牌。
    ///
    /// 支持的登录类型：
    /// - 1: 邮箱登录
    /// - 2: 手机号登录
    /// - 3: 自定义用户ID登录
    ///
    /// 示例请求（邮箱登录）：
    /// ```json
    /// {
    ///   "loginType": 1,
    ///   "email": "<EMAIL>",
    ///   "password": "Password123!"
    /// }
    /// ```
    ///
    /// 示例请求（手机号登录）：
    /// ```json
    /// {
    ///   "loginType": 2,
    ///   "phone": "13800138000",
    ///   "password": "Password123!"
    /// }
    /// ```
    ///
    /// 返回的JWT令牌有效期为24小时，请妥善保存。
    /// </remarks>
    /// <response code="200">登录成功，返回用户信息和JWT令牌</response>
    /// <response code="400">登录失败，用户名或密码错误</response>
    /// <response code="500">服务器内部错误</response>
    [HttpPost("login")]
    [SwaggerOperation(
        Summary = "用户登录",
        Description = "用户身份验证，支持邮箱、手机号、用户ID多种登录方式",
        OperationId = "LoginUser",
        Tags = new[] { "Authentication" }
    )]
    [SwaggerResponse(200, "登录成功", typeof(ApiResponse<AuthResponse>))]
    [SwaggerResponse(400, "登录失败，用户名或密码错误", typeof(ApiResponse<AuthResponse>))]
    [SwaggerResponse(500, "服务器内部错误", typeof(ApiResponse<AuthResponse>))]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Login(
        [FromBody] LoginRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _authService.LoginAsync(request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新的认证响应</returns>
    [HttpPost("refresh")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> RefreshToken(
        [FromBody] string refreshToken,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(refreshToken))
        {
            return BadRequest(ApiResponse<AuthResponse>.ErrorResult("刷新令牌不能为空", "INVALID_REFRESH_TOKEN"));
        }

        var result = await _authService.RefreshTokenAsync(refreshToken, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 检查用户名是否可用
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    [HttpGet("check-username")]
    public async Task<ActionResult<ApiResponse<bool>>> CheckUsername(
        [FromQuery] string username,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(username))
        {
            return BadRequest(ApiResponse<bool>.ErrorResult("用户名不能为空", "INVALID_USERNAME"));
        }

        var isAvailable = await _authService.IsUsernameAvailableAsync(username, cancellationToken);
        return Ok(ApiResponse<bool>.SuccessResult(isAvailable, isAvailable ? "用户名可用" : "用户名已存在"));
    }

    /// <summary>
    /// 检查邮箱是否可用
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    [HttpGet("check-email")]
    public async Task<ActionResult<ApiResponse<bool>>> CheckEmail(
        [FromQuery] string email,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(email))
        {
            return BadRequest(ApiResponse<bool>.ErrorResult("邮箱不能为空", "INVALID_EMAIL"));
        }

        var isAvailable = await _authService.IsEmailAvailableAsync(email, cancellationToken);
        return Ok(ApiResponse<bool>.SuccessResult(isAvailable, isAvailable ? "邮箱可用" : "邮箱已存在"));
    }

    /// <summary>
    /// 检查自定义用户ID是否可用
    /// </summary>
    /// <param name="customUserId">自定义用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    [HttpGet("check-custom-user-id")]
    public async Task<ActionResult<ApiResponse<bool>>> CheckCustomUserId(
        [FromQuery] string customUserId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(customUserId))
        {
            return BadRequest(ApiResponse<bool>.ErrorResult("自定义用户ID不能为空", "INVALID_CUSTOM_USER_ID"));
        }

        var isAvailable = await _authService.IsCustomUserIdAvailableAsync(customUserId, cancellationToken);
        return Ok(ApiResponse<bool>.SuccessResult(isAvailable, isAvailable ? "用户ID可用" : "用户ID已存在"));
    }

    /// <summary>
    /// 检查手机号是否可用
    /// </summary>
    /// <param name="phone">手机号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可用</returns>
    [HttpGet("check-phone")]
    public async Task<ActionResult<ApiResponse<bool>>> CheckPhone(
        [FromQuery] string phone,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(phone))
        {
            return BadRequest(ApiResponse<bool>.ErrorResult("手机号不能为空", "INVALID_PHONE"));
        }

        var isAvailable = await _authService.IsPhoneAvailableAsync(phone, cancellationToken);
        return Ok(ApiResponse<bool>.SuccessResult(isAvailable, isAvailable ? "手机号可用" : "手机号已存在"));
    }

    /// <summary>
    /// 忘记密码
    /// </summary>
    /// <param name="request">忘记密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("forgot-password")]
    public async Task<ActionResult<ApiResponse<bool>>> ForgotPassword(
        [FromBody] ForgotPasswordRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<bool>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _authService.ForgotPasswordAsync(request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("reset-password")]
    public async Task<ActionResult<ApiResponse<bool>>> ResetPassword(
        [FromBody] ResetPasswordRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<bool>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _authService.ResetPasswordAsync(request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }
}
