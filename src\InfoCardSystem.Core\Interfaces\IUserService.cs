using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.User;

namespace InfoCardSystem.Core.Interfaces;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 获取用户资料
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户资料</returns>
    Task<ApiResponse<UserProfileDto>> GetUserProfileAsync(int userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 根据自定义用户ID获取用户资料
    /// </summary>
    /// <param name="customUserId">自定义用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户资料</returns>
    Task<ApiResponse<UserProfileDto>> GetUserProfileByCustomIdAsync(string customUserId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新用户资料
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的用户资料</returns>
    Task<ApiResponse<UserProfileDto>> UpdateUserProfileAsync(int userId, UpdateUserProfileRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">修改密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<ApiResponse<bool>> ChangePasswordAsync(int userId, ChangePasswordRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="request">搜索请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    Task<ApiResponse<PagedResult<UserProfileDto>>> SearchUsersAsync(SearchUsersRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 上传头像
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="avatarFile">头像文件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>头像URL</returns>
    Task<ApiResponse<string>> UploadAvatarAsync(int userId, Stream avatarFile, string fileName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户二维码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户二维码</returns>
    Task<ApiResponse<UserQRCodeDto>> GetUserQRCodeAsync(int userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 匹配联系人
    /// </summary>
    /// <param name="request">联系人匹配请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的用户列表</returns>
    Task<ApiResponse<List<UserProfileDto>>> MatchContactsAsync(ContactsMatchRequest request, CancellationToken cancellationToken = default);
}
