<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.ProfilePage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:ProfileViewModel"
             Title="我的"
             BackgroundColor="{StaticResource Background}">

    <ScrollView>
        <StackLayout Padding="0" Spacing="0">

            <!-- 顶部用户信息区域 -->
            <Frame BackgroundColor="{StaticResource Primary}"
                   CornerRadius="0"
                   Padding="16,20,16,24"
                   HasShadow="True">

                <Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto">

                    <!-- 头像 -->
                    <Frame Grid.Row="0" Grid.Column="0" Grid.RowSpan="2"
                           WidthRequest="80"
                           HeightRequest="80"
                           CornerRadius="40"
                           Padding="0"
                           BackgroundColor="White"
                           VerticalOptions="Center">
                        <Image Source="{Binding UserInfo.AvatarUrl, FallbackValue='default_avatar.png'}"
                               Aspect="AspectFill" />
                    </Frame>

                    <!-- 用户信息 -->
                    <StackLayout Grid.Row="0" Grid.Column="1"
                                Margin="16,0,0,0"
                                VerticalOptions="Center">
                        <Label Text="{Binding UserInfo.DisplayName, FallbackValue={Binding UserInfo.Username}}"
                               FontSize="20"
                               FontAttributes="Bold"
                               TextColor="White" />
                        <Label Text="{Binding UserInfo.Username}"
                               FontSize="14"
                               TextColor="White"
                               Opacity="0.8"
                               Margin="0,4,0,0" />
                    </StackLayout>

                    <!-- 编辑按钮 -->
                    <Button Grid.Row="0" Grid.Column="2"
                           Text="编辑"
                           FontSize="14"
                           TextColor="{StaticResource Primary}"
                           BackgroundColor="White"
                           WidthRequest="60"
                           HeightRequest="32"
                           CornerRadius="16"
                           Command="{Binding EditProfileCommand}" />

                    <!-- 统计信息 -->
                    <Grid Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                          ColumnDefinitions="*,*,*"
                          Margin="16,16,0,0">

                        <StackLayout Grid.Column="0" HorizontalOptions="Center">
                            <Label Text="{Binding InfoCardCount}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center" />
                            <Label Text="资讯卡"
                                   FontSize="12"
                                   TextColor="White"
                                   Opacity="0.8"
                                   HorizontalOptions="Center" />
                        </StackLayout>

                        <StackLayout Grid.Column="1" HorizontalOptions="Center">
                            <Label Text="{Binding FriendCount}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center" />
                            <Label Text="好友"
                                   FontSize="12"
                                   TextColor="White"
                                   Opacity="0.8"
                                   HorizontalOptions="Center" />
                        </StackLayout>

                        <StackLayout Grid.Column="2" HorizontalOptions="Center">
                            <Label Text="{Binding GroupCount}"
                                   FontSize="18"
                                   FontAttributes="Bold"
                                   TextColor="White"
                                   HorizontalOptions="Center" />
                            <Label Text="群组"
                                   FontSize="12"
                                   TextColor="White"
                                   Opacity="0.8"
                                   HorizontalOptions="Center" />
                        </StackLayout>

                    </Grid>

                </Grid>

            </Frame>

            <!-- 功能菜单 -->
            <StackLayout Padding="16" Spacing="12">

                <!-- 我的资讯卡 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="📋 我的资讯卡"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding MyInfoCardsCommand}" />
                </Frame>

                <!-- 收藏夹 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="⭐ 收藏夹"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding FavoritesCommand}" />
                </Frame>

                <!-- 我的二维码 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="🔲 我的二维码"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding MyQRCodeCommand}" />
                </Frame>

                <!-- 设置 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="⚙️ 设置"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding SettingsCommand}" />
                </Frame>

                <!-- 帮助与反馈 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="❓ 帮助与反馈"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding HelpCommand}" />
                </Frame>

                <!-- 关于 -->
                <Frame Style="{StaticResource CardFrame}" Padding="0">
                    <Button Text="ℹ️ 关于InfoCard"
                           FontSize="16"
                           TextColor="{StaticResource TextPrimary}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding AboutCommand}" />
                </Frame>

                <!-- 退出登录 -->
                <Frame Style="{StaticResource CardFrame}"
                       Padding="0"
                       Margin="0,20,0,0">
                    <Button Text="🚪 退出登录"
                           FontSize="16"
                           TextColor="{StaticResource Error}"
                           BackgroundColor="Transparent"
                           HorizontalOptions="Start"
                           Padding="16,12"
                           Command="{Binding LogoutCommand}" />
                </Frame>

            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
