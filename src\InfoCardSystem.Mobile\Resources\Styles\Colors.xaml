<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-stylesheet href="../Styles/Styles.xaml" ?>
<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!-- Primary Colors -->
    <Color x:Key="Primary">#1976D2</Color>
    <Color x:Key="PrimaryDark">#1565C0</Color>
    <Color x:Key="PrimaryLight">#42A5F5</Color>
    <Color x:Key="OnPrimary">#FFFFFF</Color>

    <!-- Secondary Colors -->
    <Color x:Key="Secondary">#03DAC6</Color>
    <Color x:Key="SecondaryDark">#018786</Color>
    <Color x:Key="SecondaryLight">#4DB6AC</Color>
    <Color x:Key="OnSecondary">#000000</Color>

    <!-- Background Colors -->
    <Color x:Key="Background">#FAFAFA</Color>
    <Color x:Key="Surface">#FFFFFF</Color>
    <Color x:Key="SurfaceVariant">#F5F5F5</Color>
    <Color x:Key="OnBackground">#000000</Color>
    <Color x:Key="OnSurface">#000000</Color>

    <!-- Semantic Colors -->
    <Color x:Key="Success">#4CAF50</Color>
    <Color x:Key="Warning">#FF9800</Color>
    <Color x:Key="Error">#F44336</Color>
    <Color x:Key="Info">#2196F3</Color>

    <!-- Text Colors -->
    <Color x:Key="TextPrimary">#212121</Color>
    <Color x:Key="TextSecondary">#757575</Color>
    <Color x:Key="TextHint">#BDBDBD</Color>
    <Color x:Key="TextDisabled">#E0E0E0</Color>

    <!-- Outline and Divider -->
    <Color x:Key="Outline">#E0E0E0</Color>
    <Color x:Key="Divider">#F0F0F0</Color>

    <!-- Shadow -->
    <Color x:Key="Shadow">#1A000000</Color>

    <!-- Transparent -->
    <Color x:Key="Transparent">Transparent</Color>

    <!-- Gray Scale -->
    <Color x:Key="Gray50">#FAFAFA</Color>
    <Color x:Key="Gray100">#F5F5F5</Color>
    <Color x:Key="Gray200">#EEEEEE</Color>
    <Color x:Key="Gray300">#E0E0E0</Color>
    <Color x:Key="Gray400">#BDBDBD</Color>
    <Color x:Key="Gray500">#9E9E9E</Color>
    <Color x:Key="Gray600">#757575</Color>
    <Color x:Key="Gray700">#616161</Color>
    <Color x:Key="Gray800">#424242</Color>
    <Color x:Key="Gray900">#212121</Color>

    <!-- White and Black -->
    <Color x:Key="White">#FFFFFF</Color>
    <Color x:Key="Black">#000000</Color>

</ResourceDictionary>
