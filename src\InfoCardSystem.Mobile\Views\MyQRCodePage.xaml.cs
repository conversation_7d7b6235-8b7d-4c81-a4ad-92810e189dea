using InfoCardSystem.Mobile.ViewModels;

namespace InfoCardSystem.Mobile.Views;

public partial class MyQRCodePage : ContentPage
{
    private readonly MyQRCodeViewModel _viewModel;

    public MyQRCodePage(MyQRCodeViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await _viewModel.OnAppearingAsync();
    }

    protected override async void OnDisappearing()
    {
        base.OnDisappearing();
        await _viewModel.OnDisappearingAsync();
    }
}
