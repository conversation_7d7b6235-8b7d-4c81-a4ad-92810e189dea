using InfoCardSystem.Shared.Enums;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户实体
/// </summary>
public class AppUser : BaseEntity
{
    /// <summary>
    /// 自定义用户ID
    /// </summary>
    public string CustomUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 密码哈希
    /// </summary>
    public string PasswordHash { get; set; } = string.Empty;
    
    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 个人简介
    /// </summary>
    public string? Bio { get; set; }
    
    /// <summary>
    /// 用户状态
    /// </summary>
    public UserStatus UserStatus { get; set; } = UserStatus.Active;
    
    // 导航属性
    
    /// <summary>
    /// 用户发起的好友关系
    /// </summary>
    public virtual ICollection<UserFriendship> InitiatedFriendships { get; set; } = new List<UserFriendship>();
    
    /// <summary>
    /// 用户接收的好友关系
    /// </summary>
    public virtual ICollection<UserFriendship> ReceivedFriendships { get; set; } = new List<UserFriendship>();
    
    /// <summary>
    /// 用户创建的群组
    /// </summary>
    public virtual ICollection<UserGroup> CreatedGroups { get; set; } = new List<UserGroup>();
    
    /// <summary>
    /// 用户参与的群组成员关系
    /// </summary>
    public virtual ICollection<UserGroupMember> GroupMemberships { get; set; } = new List<UserGroupMember>();
    
    /// <summary>
    /// 用户发布的资讯卡（原始发布者）
    /// </summary>
    public virtual ICollection<UserInfoCard> OriginalInfoCards { get; set; } = new List<UserInfoCard>();
    
    /// <summary>
    /// 用户直接发布的资讯卡（包括转发）
    /// </summary>
    public virtual ICollection<UserInfoCard> DirectInfoCards { get; set; } = new List<UserInfoCard>();
    
    /// <summary>
    /// 用户收藏的资讯卡
    /// </summary>
    public virtual ICollection<UserInfoCardFavorite> FavoriteInfoCards { get; set; } = new List<UserInfoCardFavorite>();
    
    /// <summary>
    /// 用户上传的附件
    /// </summary>
    public virtual ICollection<UserAttachment> UploadedAttachments { get; set; } = new List<UserAttachment>();
    
    /// <summary>
    /// 用户的黑名单（拉黑别人）
    /// </summary>
    public virtual ICollection<UserBlacklist> BlockedUsers { get; set; } = new List<UserBlacklist>();
    
    /// <summary>
    /// 被其他用户拉黑的记录
    /// </summary>
    public virtual ICollection<UserBlacklist> BlockedByUsers { get; set; } = new List<UserBlacklist>();
}
