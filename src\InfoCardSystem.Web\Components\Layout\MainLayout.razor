﻿@*
    主布局组件

    功能说明：
    - 提供应用程序的整体布局结构
    - 包含顶部导航栏、主内容区域和页脚
    - 根据认证状态显示不同的导航选项
    - 响应式设计，适配各种屏幕尺寸

    布局结构：
    - 顶部导航栏：品牌Logo、用户菜单、登录/注册链接
    - 主内容区域：页面内容的容器
    - 页脚：版权信息和链接

    认证集成：
    - 自动检测用户登录状态
    - 显示用户头像和下拉菜单
    - 提供登出功能
*@

@inherits LayoutComponentBase
@using InfoCardSystem.Web.Services
@inject AuthStateService AuthState
@inject NavigationManager Navigation

<div class="d-flex flex-column min-vh-100">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/">
                <i class="fas fa-id-card me-2"></i>InfoCard
            </a>

            @if (AuthState.IsAuthenticated)
            {
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="@(AuthState.CurrentUser?.AvatarUrl ?? "/images/default-avatar.png")"
                                 alt="头像"
                                 class="rounded-circle me-2"
                                 style="width: 32px; height: 32px;">
                            @AuthState.CurrentUser?.DisplayName
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><button class="dropdown-item" @onclick="HandleLogout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</button></li>
                        </ul>
                    </div>
                </div>
            }
            else
            {
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/login">登录</a>
                    <a class="nav-link" href="/register">注册</a>
                </div>
            }
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="flex-grow-1">
        @Body
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-auto">
        <div class="container">
            <span class="text-muted">&copy; 2024 InfoCard. 保留所有权利。</span>
        </div>
    </footer>
</div>

<div id="blazor-error-ui" data-nosnippet>
    系统发生错误，请刷新页面重试。
    <a href="." class="reload">刷新</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        await AuthState.InitializeAsync();
        AuthState.AuthStateChanged += OnAuthStateChanged;
    }

    private void OnAuthStateChanged(bool isAuthenticated)
    {
        InvokeAsync(StateHasChanged);
    }

    private async Task HandleLogout()
    {
        await AuthState.LogoutAsync();
        Navigation.NavigateTo("/");
    }

    public void Dispose()
    {
        AuthState.AuthStateChanged -= OnAuthStateChanged;
    }
}
