using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 资讯卡DTO
/// </summary>
public class InfoCardDto
{
    /// <summary>
    /// 资讯卡ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 资讯卡类型
    /// </summary>
    public string InfoCardType { get; set; } = string.Empty;
    
    /// <summary>
    /// 图片URL
    /// </summary>
    public string? ImageUrl { get; set; }
    
    /// <summary>
    /// 链接URL
    /// </summary>
    public string? LinkUrl { get; set; }
    
    /// <summary>
    /// 发布者ID
    /// </summary>
    public int PublisherId { get; set; }
    
    /// <summary>
    /// 发布者名称
    /// </summary>
    public string PublisherName { get; set; } = string.Empty;
    
    /// <summary>
    /// 发布者头像
    /// </summary>
    public string? PublisherAvatar { get; set; }
    
    /// <summary>
    /// 是否已收藏
    /// </summary>
    public bool IsFavorited { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 创建资讯卡请求
/// </summary>
public class CreateInfoCardDto
{
    /// <summary>
    /// 标题
    /// </summary>
    [Required(ErrorMessage = "标题不能为空")]
    [StringLength(200, ErrorMessage = "标题长度不能超过200个字符")]
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// 内容
    /// </summary>
    [Required(ErrorMessage = "内容不能为空")]
    [StringLength(5000, ErrorMessage = "内容长度不能超过5000个字符")]
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 资讯卡类型
    /// </summary>
    [Required(ErrorMessage = "资讯卡类型不能为空")]
    public string InfoCardType { get; set; } = string.Empty;
    
    /// <summary>
    /// 图片URL
    /// </summary>
    public string? ImageUrl { get; set; }
    
    /// <summary>
    /// 链接URL
    /// </summary>
    public string? LinkUrl { get; set; }
    
    /// <summary>
    /// 接收者列表
    /// </summary>
    public List<InfoCardRecipientDto> Recipients { get; set; } = new();
}

/// <summary>
/// 更新资讯卡请求
/// </summary>
public class UpdateInfoCardDto
{
    /// <summary>
    /// 标题
    /// </summary>
    [StringLength(200, ErrorMessage = "标题长度不能超过200个字符")]
    public string? Title { get; set; }
    
    /// <summary>
    /// 内容
    /// </summary>
    [StringLength(5000, ErrorMessage = "内容长度不能超过5000个字符")]
    public string? Content { get; set; }
    
    /// <summary>
    /// 图片URL
    /// </summary>
    public string? ImageUrl { get; set; }
    
    /// <summary>
    /// 链接URL
    /// </summary>
    public string? LinkUrl { get; set; }
}

/// <summary>
/// 转发资讯卡请求
/// </summary>
public class ForwardInfoCardDto
{
    /// <summary>
    /// 接收者列表
    /// </summary>
    [Required(ErrorMessage = "接收者列表不能为空")]
    public List<InfoCardRecipientDto> Recipients { get; set; } = new();
    
    /// <summary>
    /// 转发消息
    /// </summary>
    [StringLength(500, ErrorMessage = "转发消息长度不能超过500个字符")]
    public string? ForwardMessage { get; set; }
}

/// <summary>
/// 资讯卡接收者DTO
/// </summary>
public class InfoCardRecipientDto
{
    /// <summary>
    /// 接收者类型（User/Group）
    /// </summary>
    [Required(ErrorMessage = "接收者类型不能为空")]
    public string RecipientType { get; set; } = string.Empty;
    
    /// <summary>
    /// 接收者ID
    /// </summary>
    [Required(ErrorMessage = "接收者ID不能为空")]
    public int RecipientId { get; set; }
}
