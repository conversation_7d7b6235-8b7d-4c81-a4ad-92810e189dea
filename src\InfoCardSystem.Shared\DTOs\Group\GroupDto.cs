using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 群组DTO
/// </summary>
public class GroupDto
{
    /// <summary>
    /// 群组ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 群组名称
    /// </summary>
    public string GroupName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 创建者ID
    /// </summary>
    public int CreatedBy { get; set; }
    
    /// <summary>
    /// 创建者名称
    /// </summary>
    public string CreatorName { get; set; } = string.Empty;
    
    /// <summary>
    /// 成员数量
    /// </summary>
    public int MemberCount { get; set; }
    
    /// <summary>
    /// 是否为成员
    /// </summary>
    public bool IsMember { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 创建群组请求
/// </summary>
public class CreateGroupDto
{
    /// <summary>
    /// 群组名称
    /// </summary>
    [Required(ErrorMessage = "群组名称不能为空")]
    [StringLength(100, ErrorMessage = "群组名称长度不能超过100个字符")]
    public string GroupName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组描述
    /// </summary>
    [StringLength(500, ErrorMessage = "群组描述长度不能超过500个字符")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// 更新群组请求
/// </summary>
public class UpdateGroupDto
{
    /// <summary>
    /// 群组名称
    /// </summary>
    [StringLength(100, ErrorMessage = "群组名称长度不能超过100个字符")]
    public string? GroupName { get; set; }
    
    /// <summary>
    /// 群组描述
    /// </summary>
    [StringLength(500, ErrorMessage = "群组描述长度不能超过500个字符")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
}

/// <summary>
/// 群组成员DTO
/// </summary>
public class GroupMemberDto
{
    /// <summary>
    /// 成员ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户信息
    /// </summary>
    public UserProfileDto User { get; set; } = null!;
    
    /// <summary>
    /// 角色
    /// </summary>
    public string Role { get; set; } = string.Empty;
    
    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinedAt { get; set; }
}
