@echo off
chcp 65001 >nul
echo ========================================
echo InfoCard 快速部署到IIS脚本
echo ========================================
echo.

echo 检查管理员权限...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限运行此脚本
    echo 请右键点击此文件并选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✓ 管理员权限验证通过
echo.

echo 步骤1: 检查发布文件...
if not exist "src\InfoCardSystem.API\publish\InfoCardSystem.API.dll" (
    echo 错误: API发布文件不存在
    echo 请先运行: dotnet publish src/InfoCardSystem.API/InfoCardSystem.API.csproj -c Release -o src/InfoCardSystem.API/publish
    pause
    exit /b 1
)

if not exist "src\InfoCardSystem.Web\publish\InfoCardSystem.Web.dll" (
    echo 错误: Web发布文件不存在
    echo 请先运行: dotnet publish src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -c Release -o src/InfoCardSystem.Web/publish
    pause
    exit /b 1
)

echo ✓ 发布文件检查通过
echo.

echo 步骤2: 停止IIS网站...
%windir%\system32\inetsrv\appcmd stop site "ICAPI" 2>nul
%windir%\system32\inetsrv\appcmd stop site "ICWeb" 2>nul
echo ✓ IIS网站已停止
echo.

echo 步骤3: 创建IIS目录...
if not exist "C:\inetpub\wwwroot\ICAPI" mkdir "C:\inetpub\wwwroot\ICAPI"
if not exist "C:\inetpub\wwwroot\ICWeb" mkdir "C:\inetpub\wwwroot\ICWeb"
echo ✓ IIS目录已创建
echo.

echo 步骤4: 复制API文件...
xcopy /E /I /Y "src\InfoCardSystem.API\publish\*" "C:\inetpub\wwwroot\ICAPI\" >nul
if %errorLevel% neq 0 (
    echo 错误: 复制API文件失败
    pause
    exit /b 1
)
echo ✓ API文件复制完成
echo.

echo 步骤5: 复制Web文件...
xcopy /E /I /Y "src\InfoCardSystem.Web\publish\*" "C:\inetpub\wwwroot\ICWeb\" >nul
if %errorLevel% neq 0 (
    echo 错误: 复制Web文件失败
    pause
    exit /b 1
)
echo ✓ Web文件复制完成
echo.

echo 步骤6: 设置目录权限...
icacls "C:\inetpub\wwwroot\ICAPI" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "C:\inetpub\wwwroot\ICWeb" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
echo ✓ 目录权限设置完成
echo.

echo 步骤7: 创建IIS网站...

REM 创建ICAPI网站
%windir%\system32\inetsrv\appcmd add site /name:"ICAPI" /physicalPath:"C:\inetpub\wwwroot\ICAPI" /bindings:"http/*:8081:" 2>nul
if %errorLevel% neq 0 (
    echo 注意: ICAPI网站可能已存在，正在更新配置...
    %windir%\system32\inetsrv\appcmd set site "ICAPI" /physicalPath:"C:\inetpub\wwwroot\ICAPI" >nul 2>&1
)

REM 创建ICWeb网站
%windir%\system32\inetsrv\appcmd add site /name:"ICWeb" /physicalPath:"C:\inetpub\wwwroot\ICWeb" /bindings:"http/*:8082:" 2>nul
if %errorLevel% neq 0 (
    echo 注意: ICWeb网站可能已存在，正在更新配置...
    %windir%\system32\inetsrv\appcmd set site "ICWeb" /physicalPath:"C:\inetpub\wwwroot\ICWeb" >nul 2>&1
)

echo ✓ IIS网站配置完成
echo.

echo 步骤8: 配置应用程序池...
%windir%\system32\inetsrv\appcmd set apppool "ICAPI" /managedRuntimeVersion:"" >nul 2>&1
%windir%\system32\inetsrv\appcmd set apppool "ICWeb" /managedRuntimeVersion:"" >nul 2>&1
echo ✓ 应用程序池配置完成
echo.

echo 步骤9: 启动IIS网站...
%windir%\system32\inetsrv\appcmd start site "ICAPI" >nul 2>&1
%windir%\system32\inetsrv\appcmd start site "ICWeb" >nul 2>&1
echo ✓ IIS网站已启动
echo.

echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 测试URL:
echo API (Swagger): http://localhost:8081/swagger
echo Web应用:      http://localhost:8082
echo.
echo 如果遇到问题，请检查:
echo 1. .NET 9.0 Runtime是否已安装
echo 2. ASP.NET Core Hosting Bundle是否已安装
echo 3. 数据库连接是否正常
echo 4. 防火墙是否允许端口8081和8082
echo.
echo 日志文件位置:
echo API日志: C:\inetpub\wwwroot\ICAPI\logs\
echo Web日志: C:\inetpub\wwwroot\ICWeb\logs\
echo.

pause
