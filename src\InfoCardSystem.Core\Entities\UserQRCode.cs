using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户二维码实体
/// </summary>
[Table("UserQRCodes")]
public class UserQRCode
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [Required]
    public int UserId { get; set; }

    /// <summary>
    /// 二维码数据
    /// </summary>
    [Required]
    [StringLength(500)]
    public string QRCodeData { get; set; } = string.Empty;

    /// <summary>
    /// 二维码图片URL
    /// </summary>
    [StringLength(500)]
    public string? QRCodeUrl { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [Required]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    [Required]
    public bool IsActive { get; set; }

    /// <summary>
    /// 用户导航属性
    /// </summary>
    [ForeignKey(nameof(UserId))]
    public virtual AppUser User { get; set; } = null!;
}
