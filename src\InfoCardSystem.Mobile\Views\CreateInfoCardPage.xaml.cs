using InfoCardSystem.Mobile.ViewModels;

namespace InfoCardSystem.Mobile.Views;

public partial class CreateInfoCardPage : ContentPage
{
    private readonly CreateInfoCardViewModel _viewModel;

    public CreateInfoCardPage(CreateInfoCardViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await _viewModel.OnAppearingAsync();
    }

    protected override async void OnDisappearing()
    {
        base.OnDisappearing();
        await _viewModel.OnDisappearingAsync();
    }
}
