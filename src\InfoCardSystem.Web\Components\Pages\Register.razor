@*
    用户注册页面组件

    功能说明：
    - 用户名、手机号、邮箱注册
    - 密码强度验证
    - 密码确认验证
    - 服务条款同意确认
    - 实时表单验证

    验证规则：
    - 用户名：2-100字符
    - 手机号：有效的手机号格式
    - 邮箱：可选，有效的邮箱格式
    - 密码：6-100字符
    - 确认密码：必须与密码一致
*@

@page "/register"
@using InfoCardSystem.Shared.DTOs
@using InfoCardSystem.Web.Services
@using System.Linq
@inject IApiService ApiService
@inject NavigationManager Navigation
@inject IToastService ToastService
@rendermode InteractiveServer

<PageTitle>注册 - InfoCard</PageTitle>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- 左侧背景图片区域 -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center bg-success">
            <div class="text-center text-white">
                <h1 class="display-4 mb-4">加入InfoCard</h1>
                <p class="lead">开始您的资讯分享之旅</p>
                <p>与朋友分享精彩内容，发现更多可能</p>
            </div>
        </div>
        
        <!-- 右侧注册表单区域 -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 450px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">创建账户</h2>
                        <p class="text-muted">填写信息完成注册</p>
                    </div>

                    <EditForm Model="registerRequest" OnValidSubmit="HandleRegister" FormName="RegisterForm">
                        <DataAnnotationsValidator />
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <InputText @bind-Value="registerRequest.Username" 
                                      class="form-control" 
                                      id="username" 
                                      placeholder="请输入用户名" 
                                      disabled="@isLoading" />
                            <ValidationMessage For="() => registerRequest.Username" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱</label>
                            <InputText @bind-Value="registerRequest.Email"
                                      class="form-control"
                                      id="email"
                                      placeholder="请输入有效的邮箱地址"
                                      disabled="@isLoading" />
                            <ValidationMessage For="() => registerRequest.Email" class="text-danger" />
                            <small class="form-text text-muted">邮箱将用于登录和接收重要通知</small>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">手机号（可选）</label>
                            <InputText @bind-Value="registerRequest.Phone"
                                      class="form-control"
                                      id="phone"
                                      placeholder="请输入手机号"
                                      disabled="@isLoading" />
                            <ValidationMessage For="() => registerRequest.Phone" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <InputText @bind-Value="registerRequest.Password" 
                                          type="@(showPassword ? "text" : "password")"
                                          class="form-control" 
                                          id="password" 
                                          placeholder="请输入密码" 
                                          disabled="@isLoading" />
                                <button type="button" 
                                        class="btn btn-outline-secondary" 
                                        @onclick="TogglePasswordVisibility"
                                        disabled="@isLoading">
                                    <i class="@(showPassword ? "fas fa-eye-slash" : "fas fa-eye")"></i>
                                </button>
                            </div>
                            <ValidationMessage For="() => registerRequest.Password" class="text-danger" />
                            <small class="form-text text-muted">密码长度至少6个字符，建议包含大小写字母、数字和特殊字符</small>
                        </div>

                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">确认密码</label>
                            <div class="input-group">
                                <InputText @bind-Value="registerRequest.ConfirmPassword"
                                          type="@(showConfirmPassword ? "text" : "password")"
                                          class="form-control"
                                          id="confirmPassword"
                                          placeholder="请再次输入密码"
                                          disabled="@isLoading" />
                                <button type="button"
                                        class="btn btn-outline-secondary"
                                        @onclick="ToggleConfirmPasswordVisibility"
                                        disabled="@isLoading">
                                    <i class="@(showConfirmPassword ? "fas fa-eye-slash" : "fas fa-eye")"></i>
                                </button>
                            </div>
                            <ValidationMessage For="() => registerRequest.ConfirmPassword" class="text-danger" />
                        </div>

                        <div class="mb-3 form-check">
                            <InputCheckbox @bind-Value="agreeToTerms" class="form-check-input" id="agreeToTerms" disabled="@isLoading" />
                            <label class="form-check-label" for="agreeToTerms">
                                我同意 <a href="/terms" target="_blank" class="text-decoration-none">服务条款</a> 和 <a href="/privacy" target="_blank" class="text-decoration-none">隐私政策</a>
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" 
                                    class="btn btn-success btn-lg" 
                                    disabled="@(isLoading || !IsFormValid())">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>正在创建账户...</span>
                                }
                                else
                                {
                                    <i class="fas fa-user-plus me-2"></i>
                                    <span>创建账户</span>
                                }
                            </button>
                        </div>

                        <ValidationSummary class="text-danger" />
                    </EditForm>

                    <div class="text-center">
                        <p class="mb-0">
                            已有账户？
                            <a href="/login" class="text-decoration-none">立即登录</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterRequestDto registerRequest = new();
    private string confirmPassword = string.Empty;
    private bool isLoading = false;
    private bool showPassword = false;
    private bool showConfirmPassword = false;
    private bool agreeToTerms = false;

    private async Task HandleRegister()
    {
        if (isLoading || !IsFormValid()) return;

        isLoading = true;
        try
        {
            var response = await ApiService.RegisterAsync(registerRequest);
            if (response.IsSuccess)
            {
                ToastService.ShowSuccess("注册成功！请登录您的账户");
                Navigation.NavigateTo("/login");
            }
            else
            {
                ToastService.ShowError(response.ErrorMessage ?? "注册失败，请重试");
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"注册时发生错误：{ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private bool IsFormValid()
    {
        return !string.IsNullOrWhiteSpace(registerRequest.Username) &&
               !string.IsNullOrWhiteSpace(registerRequest.Email) &&
               !string.IsNullOrWhiteSpace(registerRequest.Password) &&
               !string.IsNullOrWhiteSpace(registerRequest.ConfirmPassword) &&
               registerRequest.Password == registerRequest.ConfirmPassword &&
               agreeToTerms;
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        showConfirmPassword = !showConfirmPassword;
    }


}
