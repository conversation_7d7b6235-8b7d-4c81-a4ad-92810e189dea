using System.Globalization;

namespace InfoCardSystem.Mobile.Converters;

/// <summary>
/// 在线状态到颜色转换器
/// </summary>
public class OnlineStatusColorConverter : IValueConverter
{
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isOnline)
        {
            return isOnline ? Colors.Green : Colors.Gray;
        }
        
        return Colors.Gray;
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
