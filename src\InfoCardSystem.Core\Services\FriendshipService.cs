using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Friendship;
using InfoCardSystem.Core.DTOs.User;
using InfoCardSystem.Core.Entities;
using InfoCardSystem.Core.Interfaces;
using InfoCardSystem.Shared.Constants;
using InfoCardSystem.Shared.Enums;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 好友关系服务实现
/// </summary>
public class FriendshipService : IFriendshipService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IQRCodeService _qrCodeService;
    private readonly ILogger<FriendshipService> _logger;

    public FriendshipService(
        IUnitOfWork unitOfWork,
        IQRCodeService qrCodeService,
        ILogger<FriendshipService> logger)
    {
        _unitOfWork = unitOfWork;
        _qrCodeService = qrCodeService;
        _logger = logger;
    }

    /// <summary>
    /// 发送好友请求
    /// </summary>
    public async Task<ApiResponse<bool>> SendFriendRequestAsync(int userId, string targetCustomUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            // 查找目标用户
            var targetUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.CustomUserId == targetCustomUserId, cancellationToken);
            if (targetUser == null)
            {
                return ApiResponse<bool>.ErrorResult("目标用户不存在", ErrorCodes.USER_001);
            }

            if (targetUser.Id == userId)
            {
                return ApiResponse<bool>.ErrorResult("不能添加自己为好友", ErrorCodes.FRIEND_001);
            }

            // 检查是否已经是好友或已有请求
            var existingFriendship = await _unitOfWork.Friendships.FirstOrDefaultAsync(
                f => (f.UserId == userId && f.FriendId == targetUser.Id) ||
                     (f.UserId == targetUser.Id && f.FriendId == userId),
                cancellationToken);

            if (existingFriendship != null)
            {
                return existingFriendship.FriendshipStatus switch
                {
                    FriendshipStatus.Accepted => ApiResponse<bool>.ErrorResult("已经是好友关系", ErrorCodes.FRIEND_002),
                    FriendshipStatus.Pending => ApiResponse<bool>.ErrorResult("好友请求已发送，请等待对方处理", ErrorCodes.FRIEND_003),
                    _ => ApiResponse<bool>.ErrorResult("好友关系状态异常", ErrorCodes.FRIEND_004)
                };
            }

            // 检查是否被拉黑
            var isBlocked = await _unitOfWork.Blacklists.ExistsAsync(
                b => (b.UserId == userId && b.BlockedUserId == targetUser.Id) ||
                     (b.UserId == targetUser.Id && b.BlockedUserId == userId),
                cancellationToken);

            if (isBlocked)
            {
                return ApiResponse<bool>.ErrorResult("无法发送好友请求", ErrorCodes.FRIEND_005);
            }

            // 创建好友请求
            var friendship = new UserFriendship
            {
                UserId = userId,
                FriendId = targetUser.Id,
                FriendshipStatus = FriendshipStatus.Pending
            };

            await _unitOfWork.Friendships.AddAsync(friendship, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("好友请求发送成功: UserId={UserId}, TargetUserId={TargetUserId}", userId, targetUser.Id);
            return ApiResponse<bool>.SuccessResult(true, "好友请求发送成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送好友请求失败: UserId={UserId}, TargetCustomUserId={TargetCustomUserId}", userId, targetCustomUserId);
            return ApiResponse<bool>.ErrorResult("发送好友请求失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 处理好友请求
    /// </summary>
    public async Task<ApiResponse<bool>> HandleFriendRequestAsync(int userId, int friendshipId, bool accept, CancellationToken cancellationToken = default)
    {
        try
        {
            var friendship = await _unitOfWork.Friendships.GetByIdAsync(friendshipId, cancellationToken);
            if (friendship == null)
            {
                return ApiResponse<bool>.ErrorResult("好友请求不存在", ErrorCodes.FRIEND_006);
            }

            // 只有接收方可以处理请求
            if (friendship.FriendId != userId)
            {
                return ApiResponse<bool>.ErrorResult("无权处理此好友请求", ErrorCodes.FRIEND_007);
            }

            if (friendship.FriendshipStatus != FriendshipStatus.Pending)
            {
                return ApiResponse<bool>.ErrorResult("好友请求已被处理", ErrorCodes.FRIEND_008);
            }

            if (accept)
            {
                // 接受请求
                friendship.FriendshipStatus = FriendshipStatus.Accepted;
                
                // 创建反向好友关系
                var reverseFriendship = new UserFriendship
                {
                    UserId = friendship.FriendId,
                    FriendId = friendship.UserId,
                    FriendshipStatus = FriendshipStatus.Accepted
                };
                
                await _unitOfWork.Friendships.AddAsync(reverseFriendship, cancellationToken);
                _logger.LogInformation("好友请求已接受: FriendshipId={FriendshipId}", friendshipId);
            }
            else
            {
                // 拒绝请求
                friendship.FriendshipStatus = FriendshipStatus.Rejected;
                _logger.LogInformation("好友请求已拒绝: FriendshipId={FriendshipId}", friendshipId);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            var message = accept ? "好友请求已接受" : "好友请求已拒绝";
            return ApiResponse<bool>.SuccessResult(true, message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理好友请求失败: UserId={UserId}, FriendshipId={FriendshipId}", userId, friendshipId);
            return ApiResponse<bool>.ErrorResult("处理好友请求失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取好友列表
    /// </summary>
    public async Task<ApiResponse<PagedResult<FriendshipDto>>> GetFriendsAsync(int userId, int page = 1, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _unitOfWork.Friendships.GetQueryable()
                .Where(f => f.UserId == userId && f.FriendshipStatus == FriendshipStatus.Accepted);

            var totalCount = await _unitOfWork.Friendships.CountAsync(query, cancellationToken);
            var friendships = await _unitOfWork.Friendships.GetPagedAsync(query, page, pageSize, cancellationToken);

            var friendshipDtos = new List<FriendshipDto>();
            
            foreach (var friendship in friendships)
            {
                var friend = await _unitOfWork.Users.GetByIdAsync(friendship.FriendId, cancellationToken);
                if (friend != null)
                {
                    friendshipDtos.Add(new FriendshipDto
                    {
                        Id = friendship.Id,
                        Friend = new UserProfileDto
                        {
                            Id = friend.Id,
                            CustomUserId = friend.CustomUserId,
                            Username = friend.Username,
                            Email = friend.Email,
                            Phone = friend.Phone,
                            AvatarUrl = friend.AvatarUrl,
                            Bio = friend.Bio,
                            UserStatus = friend.UserStatus.ToString(),
                            CreatedAt = friend.CreatedAt,
                            UpdatedAt = friend.UpdatedAt
                        },
                        FriendAlias = friendship.FriendAlias,
                        FriendshipStatus = friendship.FriendshipStatus.ToString(),
                        CreatedAt = friendship.CreatedAt,
                        UpdatedAt = friendship.UpdatedAt
                    });
                }
            }

            var pagedResult = new PagedResult<FriendshipDto>
            {
                Items = friendshipDtos,
                Pagination = new PaginationInfo
                {
                    CurrentPage = page,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    TotalItems = totalCount,
                    ItemsPerPage = pageSize
                }
            };

            return ApiResponse<PagedResult<FriendshipDto>>.SuccessResult(pagedResult, "获取好友列表成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取好友列表失败: UserId={UserId}", userId);
            return ApiResponse<PagedResult<FriendshipDto>>.ErrorResult("获取好友列表失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取待处理的好友请求
    /// </summary>
    public async Task<ApiResponse<List<FriendRequestDto>>> GetPendingFriendRequestsAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var pendingRequests = await _unitOfWork.Friendships.GetAllAsync(
                f => f.FriendId == userId && f.FriendshipStatus == FriendshipStatus.Pending,
                cancellationToken);

            var requestDtos = new List<FriendRequestDto>();
            
            foreach (var request in pendingRequests)
            {
                var requester = await _unitOfWork.Users.GetByIdAsync(request.UserId, cancellationToken);
                if (requester != null)
                {
                    requestDtos.Add(new FriendRequestDto
                    {
                        Id = request.Id,
                        Requester = new UserProfileDto
                        {
                            Id = requester.Id,
                            CustomUserId = requester.CustomUserId,
                            Username = requester.Username,
                            Email = requester.Email,
                            Phone = requester.Phone,
                            AvatarUrl = requester.AvatarUrl,
                            Bio = requester.Bio,
                            UserStatus = requester.UserStatus.ToString(),
                            CreatedAt = requester.CreatedAt,
                            UpdatedAt = requester.UpdatedAt
                        },
                        Status = request.FriendshipStatus.ToString(),
                        CreatedAt = request.CreatedAt
                    });
                }
            }

            return ApiResponse<List<FriendRequestDto>>.SuccessResult(requestDtos, "获取好友请求成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取好友请求失败: UserId={UserId}", userId);
            return ApiResponse<List<FriendRequestDto>>.ErrorResult("获取好友请求失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 更新好友别名
    /// </summary>
    public async Task<ApiResponse<bool>> UpdateFriendAliasAsync(int userId, int friendshipId, string? alias, CancellationToken cancellationToken = default)
    {
        try
        {
            var friendship = await _unitOfWork.Friendships.FirstOrDefaultAsync(
                f => f.Id == friendshipId && f.UserId == userId && f.FriendshipStatus == FriendshipStatus.Accepted,
                cancellationToken);

            if (friendship == null)
            {
                return ApiResponse<bool>.ErrorResult("好友关系不存在", ErrorCodes.FRIEND_006);
            }

            friendship.FriendAlias = alias;
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("好友别名更新成功: FriendshipId={FriendshipId}, Alias={Alias}", friendshipId, alias);
            return ApiResponse<bool>.SuccessResult(true, "好友别名更新成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新好友别名失败: UserId={UserId}, FriendshipId={FriendshipId}", userId, friendshipId);
            return ApiResponse<bool>.ErrorResult("更新好友别名失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 删除好友
    /// </summary>
    public async Task<ApiResponse<bool>> RemoveFriendAsync(int userId, int friendshipId, CancellationToken cancellationToken = default)
    {
        try
        {
            var friendship = await _unitOfWork.Friendships.FirstOrDefaultAsync(
                f => f.Id == friendshipId && f.UserId == userId && f.FriendshipStatus == FriendshipStatus.Accepted,
                cancellationToken);

            if (friendship == null)
            {
                return ApiResponse<bool>.ErrorResult("好友关系不存在", ErrorCodes.FRIEND_006);
            }

            // 删除双向好友关系
            var reverseFriendship = await _unitOfWork.Friendships.FirstOrDefaultAsync(
                f => f.UserId == friendship.FriendId && f.FriendId == userId && f.FriendshipStatus == FriendshipStatus.Accepted,
                cancellationToken);

            _unitOfWork.Friendships.Remove(friendship);
            if (reverseFriendship != null)
            {
                _unitOfWork.Friendships.Remove(reverseFriendship);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("好友删除成功: FriendshipId={FriendshipId}", friendshipId);
            return ApiResponse<bool>.SuccessResult(true, "好友删除成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除好友失败: UserId={UserId}, FriendshipId={FriendshipId}", userId, friendshipId);
            return ApiResponse<bool>.ErrorResult("删除好友失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 添加到黑名单
    /// </summary>
    public async Task<ApiResponse<bool>> BlockUserAsync(int userId, int blockedUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (userId == blockedUserId)
            {
                return ApiResponse<bool>.ErrorResult("不能拉黑自己", ErrorCodes.BLACKLIST_001);
            }

            // 检查是否已经拉黑
            var existingBlock = await _unitOfWork.Blacklists.FirstOrDefaultAsync(
                b => b.UserId == userId && b.BlockedUserId == blockedUserId,
                cancellationToken);

            if (existingBlock != null)
            {
                return ApiResponse<bool>.ErrorResult("用户已在黑名单中", ErrorCodes.BLACKLIST_002);
            }

            // 删除好友关系（如果存在）
            var friendships = await _unitOfWork.Friendships.GetAllAsync(
                f => (f.UserId == userId && f.FriendId == blockedUserId) ||
                     (f.UserId == blockedUserId && f.FriendId == userId),
                cancellationToken);

            foreach (var friendship in friendships)
            {
                _unitOfWork.Friendships.Remove(friendship);
            }

            // 添加到黑名单
            var blacklist = new UserBlacklist
            {
                UserId = userId,
                BlockedUserId = blockedUserId
            };

            await _unitOfWork.Blacklists.AddAsync(blacklist, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户拉黑成功: UserId={UserId}, BlockedUserId={BlockedUserId}", userId, blockedUserId);
            return ApiResponse<bool>.SuccessResult(true, "用户已添加到黑名单");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "拉黑用户失败: UserId={UserId}, BlockedUserId={BlockedUserId}", userId, blockedUserId);
            return ApiResponse<bool>.ErrorResult("拉黑用户失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 从黑名单移除
    /// </summary>
    public async Task<ApiResponse<bool>> UnblockUserAsync(int userId, int blockedUserId, CancellationToken cancellationToken = default)
    {
        try
        {
            var blacklist = await _unitOfWork.Blacklists.FirstOrDefaultAsync(
                b => b.UserId == userId && b.BlockedUserId == blockedUserId,
                cancellationToken);

            if (blacklist == null)
            {
                return ApiResponse<bool>.ErrorResult("用户不在黑名单中", ErrorCodes.BLACKLIST_003);
            }

            _unitOfWork.Blacklists.Remove(blacklist);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("用户解除拉黑成功: UserId={UserId}, BlockedUserId={BlockedUserId}", userId, blockedUserId);
            return ApiResponse<bool>.SuccessResult(true, "用户已从黑名单移除");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解除拉黑失败: UserId={UserId}, BlockedUserId={BlockedUserId}", userId, blockedUserId);
            return ApiResponse<bool>.ErrorResult("解除拉黑失败", ErrorCodes.SERVER_001);
        }
    }

    /// <summary>
    /// 获取黑名单
    /// </summary>
    public async Task<ApiResponse<List<UserProfileDto>>> GetBlockedUsersAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var blacklists = await _unitOfWork.Blacklists.GetAllAsync(
                b => b.UserId == userId,
                cancellationToken);

            var blockedUsers = new List<UserProfileDto>();

            foreach (var blacklist in blacklists)
            {
                var blockedUser = await _unitOfWork.Users.GetByIdAsync(blacklist.BlockedUserId, cancellationToken);
                if (blockedUser != null)
                {
                    blockedUsers.Add(new UserProfileDto
                    {
                        Id = blockedUser.Id,
                        CustomUserId = blockedUser.CustomUserId,
                        Username = blockedUser.Username,
                        Email = blockedUser.Email,
                        Phone = blockedUser.Phone,
                        AvatarUrl = blockedUser.AvatarUrl,
                        Bio = blockedUser.Bio,
                        UserStatus = blockedUser.UserStatus.ToString(),
                        CreatedAt = blockedUser.CreatedAt,
                        UpdatedAt = blockedUser.UpdatedAt
                    });
                }
            }

            return ApiResponse<List<UserProfileDto>>.SuccessResult(blockedUsers, "获取黑名单成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取黑名单失败: UserId={UserId}", userId);
            return ApiResponse<List<UserProfileDto>>.ErrorResult("获取黑名单失败", ErrorCodes.SERVER_001);
        }
    }

    public async Task<ApiResponse<bool>> ScanQRCodeAddFriendAsync(int userId, ScanQRCodeRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("处理扫码添加好友请求: UserId={UserId}", userId);

            // 验证二维码数据
            if (!_qrCodeService.ValidateQRCodeData(request.QRCodeData))
            {
                return ApiResponse<bool>.ErrorResult("无效的二维码", "INVALID_QR_CODE");
            }

            // 解析二维码数据
            var parseResult = _qrCodeService.ParseQRCodeData(request.QRCodeData);
            if (!parseResult.Success)
            {
                return ApiResponse<bool>.ErrorResult(parseResult.ErrorMessage ?? "二维码解析失败", "QR_CODE_PARSE_ERROR");
            }

            // 检查二维码是否过期
            if (parseResult.ExpiresAt.HasValue && parseResult.ExpiresAt.Value < DateTime.UtcNow)
            {
                return ApiResponse<bool>.ErrorResult("二维码已过期", "QR_CODE_EXPIRED");
            }

            // 获取目标用户ID
            int targetUserId;
            if (parseResult.UserId.HasValue)
            {
                targetUserId = parseResult.UserId.Value;
            }
            else if (!string.IsNullOrEmpty(parseResult.CustomUserId))
            {
                // 通过自定义ID查找用户
                var users = await _unitOfWork.Users.FindAsync(
                    u => u.CustomUserId == parseResult.CustomUserId,
                    cancellationToken);
                var targetUser = users.FirstOrDefault();
                if (targetUser == null)
                {
                    return ApiResponse<bool>.ErrorResult("用户不存在", "USER_NOT_FOUND");
                }
                targetUserId = targetUser.Id;
            }
            else
            {
                return ApiResponse<bool>.ErrorResult("无效的二维码数据", "INVALID_QR_DATA");
            }

            // 检查是否尝试添加自己
            if (userId == targetUserId)
            {
                return ApiResponse<bool>.ErrorResult("不能添加自己为好友", "CANNOT_ADD_SELF");
            }

            // 检查目标用户是否存在
            var target = await _unitOfWork.Users.GetByIdAsync(targetUserId, cancellationToken);
            if (target == null)
            {
                return ApiResponse<bool>.ErrorResult("目标用户不存在", "TARGET_USER_NOT_FOUND");
            }

            // 检查是否已经是好友
            var existingFriendships = await _unitOfWork.Friendships.FindAsync(
                f => (f.UserId == userId && f.FriendId == targetUserId) ||
                     (f.UserId == targetUserId && f.FriendId == userId),
                cancellationToken);

            var existingFriendship = existingFriendships.FirstOrDefault();
            if (existingFriendship != null)
            {
                if (existingFriendship.Status == FriendshipStatus.Accepted)
                {
                    return ApiResponse<bool>.ErrorResult("你们已经是好友了", "ALREADY_FRIENDS");
                }
                else if (existingFriendship.Status == FriendshipStatus.Pending)
                {
                    return ApiResponse<bool>.ErrorResult("好友请求已发送，请等待对方确认", "REQUEST_ALREADY_SENT");
                }
            }

            // 检查是否被拉黑
            var blacklists = await _unitOfWork.Blacklists.FindAsync(
                b => (b.UserId == userId && b.BlockedUserId == targetUserId) ||
                     (b.UserId == targetUserId && b.BlockedUserId == userId),
                cancellationToken);

            if (blacklists.Any())
            {
                return ApiResponse<bool>.ErrorResult("无法添加该用户为好友", "USER_BLOCKED");
            }

            // 创建好友请求
            var friendship = new UserFriendship
            {
                UserId = userId,
                FriendId = targetUserId,
                Status = FriendshipStatus.Pending,
                RequestMessage = request.Message,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Friendships.AddAsync(friendship, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("扫码添加好友请求发送成功: UserId={UserId}, TargetUserId={TargetUserId}",
                userId, targetUserId);

            return ApiResponse<bool>.SuccessResult(true, "好友请求已发送");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扫码添加好友失败: UserId={UserId}", userId);
            return ApiResponse<bool>.ErrorResult("添加好友失败", ErrorCodes.SERVER_001);
        }
    }
}
