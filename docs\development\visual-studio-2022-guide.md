# Visual Studio 2022 开发指南

本指南详细说明如何在Visual Studio 2022中打开、配置和运行InfoCard系统项目。

## 📋 环境要求

### Visual Studio 2022 版本要求
- **最低版本**: Visual Studio 2022 17.8+
- **推荐版本**: Visual Studio 2022 17.11+
- **版本类型**: Community、Professional 或 Enterprise

### 必需工作负载
在Visual Studio Installer中安装以下工作负载：

#### 核心工作负载
- **ASP.NET 和 Web 开发**: 用于API和Web项目
- **.NET 桌面开发**: 用于核心.NET项目

#### 可选工作负载（如需完整开发）
- **使用 .NET 的移动开发**: 用于MAUI移动端项目
- **Node.js 开发**: 用于前端工具和小程序开发

### .NET SDK 要求
- **.NET 8.0 SDK**: 必需，所有项目都基于.NET 8.0
- **验证安装**: 在命令行运行 `dotnet --version` 确认版本为 8.0.x

## 🚀 快速开始

### 1. 打开项目
有两种方式打开项目：

#### 方式一：使用VS2022专用解决方案（推荐）
```
文件 → 打开 → 项目/解决方案 → 选择 InfoCardSystem.VS2022.sln
```

#### 方式二：使用完整解决方案
```
文件 → 打开 → 项目/解决方案 → 选择 InfoCardSystem.sln
```

> **注意**: VS2022专用解决方案排除了可能存在兼容性问题的项目，推荐用于日常开发。

### 2. 还原NuGet包
项目打开后，Visual Studio会自动还原NuGet包。如果没有自动还原：
```
工具 → NuGet包管理器 → 程序包管理器控制台
执行: Update-Package -reinstall
```

### 3. 设置启动项目
#### 单项目启动
- 右键点击 `InfoCardSystem.API` → 设为启动项目
- 或右键点击 `InfoCardSystem.Web` → 设为启动项目

#### 多项目启动（推荐）
1. 右键点击解决方案 → 属性
2. 选择 "多个启动项目"
3. 设置以下项目为 "启动":
   - InfoCardSystem.API
   - InfoCardSystem.Web
4. 点击确定

## 🔧 项目配置

### 数据库配置
在运行项目前，需要配置数据库连接：

1. **安装MySQL**: 确保本地安装了MySQL 8.0+
2. **创建数据库**: 
   ```sql
   CREATE DATABASE InfoCardDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
3. **配置连接字符串**: 编辑 `src/InfoCardSystem.API/appsettings.Development.json`
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=InfoCardDB;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
     }
   }
   ```

### 运行数据库迁移
1. 打开 "程序包管理器控制台"
2. 设置默认项目为 `InfoCardSystem.Infrastructure`
3. 运行迁移命令：
   ```
   Update-Database
   ```

## ▶️ 运行和调试

### 启动项目
1. **F5 启动调试**: 启动项目并附加调试器
2. **Ctrl+F5 启动不调试**: 启动项目但不附加调试器

### 访问地址
项目启动后可以通过以下地址访问：

#### API服务
- **HTTP**: http://localhost:5001
- **HTTPS**: https://localhost:7001
- **Swagger文档**: https://localhost:7001/swagger

#### Web应用
- **HTTP**: http://localhost:5000
- **HTTPS**: https://localhost:7000

### 调试技巧
1. **断点调试**: 在代码行左侧点击设置断点
2. **即时窗口**: 调试时使用即时窗口查看变量值
3. **输出窗口**: 查看应用程序日志和调试信息
4. **诊断工具**: 监控内存和CPU使用情况

## 🏗️ 项目结构说明

### 解决方案结构
```
InfoCardSystem.VS2022.sln
├── src/
│   ├── InfoCardSystem.Shared/      # 共享模型和常量
│   ├── InfoCardSystem.Core/        # 业务逻辑和服务
│   ├── InfoCardSystem.Infrastructure/ # 数据访问和基础设施
│   ├── InfoCardSystem.API/         # Web API服务
│   └── InfoCardSystem.Web/         # Blazor Web应用
├── docs/                           # 项目文档
└── scripts/                        # 构建和部署脚本
```

### 项目依赖关系
```
InfoCardSystem.API
├── InfoCardSystem.Core
├── InfoCardSystem.Infrastructure
└── InfoCardSystem.Shared

InfoCardSystem.Web
├── InfoCardSystem.Shared

InfoCardSystem.Infrastructure
├── InfoCardSystem.Core
└── InfoCardSystem.Shared

InfoCardSystem.Core
└── InfoCardSystem.Shared
```

## 🔨 开发工作流

### 1. 日常开发流程
1. **拉取最新代码**: `git pull origin main`
2. **还原包**: 右键解决方案 → 还原NuGet包
3. **构建解决方案**: Ctrl+Shift+B
4. **运行项目**: F5 或 Ctrl+F5
5. **进行开发和测试**
6. **提交代码**: 使用Git提交更改

### 2. 添加新功能
1. **创建功能分支**: `git checkout -b feature/new-feature`
2. **在Core项目中添加业务逻辑**
3. **在Infrastructure项目中添加数据访问**
4. **在API项目中添加控制器**
5. **在Web项目中添加页面和组件**
6. **测试功能**
7. **提交并创建Pull Request**

### 3. 数据库更改
1. **修改实体模型**: 在Core项目中修改实体类
2. **添加迁移**: 
   ```
   Add-Migration MigrationName
   ```
3. **更新数据库**: 
   ```
   Update-Database
   ```

## 🐛 常见问题解决

### 1. 项目无法加载
**问题**: 项目文件无法加载或显示为不可用
**解决方案**:
- 确保安装了.NET 8.0 SDK
- 重新启动Visual Studio
- 清理并重新构建解决方案

### 2. NuGet包还原失败
**问题**: 包还原时出现错误
**解决方案**:
```
工具 → 选项 → NuGet包管理器 → 程序包源
确保 nuget.org 源可用
清除所有NuGet缓存: dotnet nuget locals all --clear
```

### 3. 数据库连接失败
**问题**: 无法连接到数据库
**解决方案**:
- 检查MySQL服务是否运行
- 验证连接字符串中的用户名和密码
- 确保数据库已创建
- 检查防火墙设置

### 4. 端口冲突
**问题**: 应用启动时端口被占用
**解决方案**:
- 修改 `launchSettings.json` 中的端口号
- 或停止占用端口的其他应用程序

### 5. 构建错误
**问题**: 项目构建时出现编译错误
**解决方案**:
- 检查错误列表中的具体错误信息
- 确保所有项目引用正确
- 清理解决方案后重新构建

## 🔧 高级配置

### 1. 自定义启动配置
编辑 `Properties/launchSettings.json` 自定义启动参数：
```json
{
  "profiles": {
    "Custom Profile": {
      "commandName": "Project",
      "launchBrowser": true,
      "applicationUrl": "http://localhost:8081",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "CUSTOM_SETTING": "value"
      }
    }
  }
}
```

### 2. 调试配置
在 `.vscode/launch.json` 中配置调试设置：
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch API",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/src/InfoCardSystem.API/bin/Debug/net8.0/InfoCardSystem.API.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/InfoCardSystem.API",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  ]
}
```

### 3. 代码分析配置
启用代码分析和格式化：
1. **工具** → **选项** → **文本编辑器** → **C#** → **代码样式**
2. 配置代码格式化规则
3. 启用 EditorConfig 支持

## 📚 扩展推荐

### 必装扩展
- **C# Dev Kit**: 增强的C#开发体验
- **IntelliCode**: AI辅助代码补全
- **Git Extensions**: 增强的Git集成

### 推荐扩展
- **Resharper**: 代码质量和重构工具（付费）
- **CodeMaid**: 代码清理和格式化
- **Productivity Power Tools**: 提高开发效率
- **Web Essentials**: Web开发增强工具

## 📞 获取帮助

如果在使用Visual Studio 2022开发过程中遇到问题：

1. **查看错误列表**: 查看详细的编译错误和警告
2. **查看输出窗口**: 查看构建和运行时日志
3. **查看项目文档**: 参考 `docs/` 目录下的其他文档
4. **提交Issue**: 在GitHub仓库中提交问题报告
5. **联系支持**: 发送邮件到 <EMAIL>

---

**祝您开发愉快！** 🎉
