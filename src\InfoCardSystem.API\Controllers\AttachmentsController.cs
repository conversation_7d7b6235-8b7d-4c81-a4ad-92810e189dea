using System.Security.Claims;
using InfoCardSystem.Core.DTOs.Attachment;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 附件管理控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[Authorize]
public class AttachmentsController : ControllerBase
{
    private readonly IAttachmentService _attachmentService;
    private readonly ILogger<AttachmentsController> _logger;

    public AttachmentsController(IAttachmentService attachmentService, ILogger<AttachmentsController> logger)
    {
        _attachmentService = attachmentService;
        _logger = logger;
    }

    /// <summary>
    /// 上传单个文件
    /// </summary>
    /// <param name="file">文件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>上传结果</returns>
    [HttpPost("upload")]
    public async Task<ActionResult<ApiResponse<FileUploadResponse>>> UploadFile(
        IFormFile file,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<FileUploadResponse>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.UploadFileAsync(userId.Value, file, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 批量上传文件
    /// </summary>
    /// <param name="files">文件列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量上传结果</returns>
    [HttpPost("upload/batch")]
    public async Task<ActionResult<ApiResponse<BatchFileUploadResponse>>> UploadFiles(
        IFormFileCollection files,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<BatchFileUploadResponse>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.UploadFilesAsync(userId.Value, files, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 下载文件
    /// </summary>
    /// <param name="id">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>文件流</returns>
    [HttpGet("{id}/download")]
    public async Task<IActionResult> DownloadFile(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized();
        }

        var result = await _attachmentService.DownloadFileAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success || result.Data == null)
        {
            return NotFound();
        }

        var downloadInfo = result.Data;
        return File(downloadInfo.FileStream, downloadInfo.MimeType, downloadInfo.FileName);
    }

    /// <summary>
    /// 预览图片
    /// </summary>
    /// <param name="id">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图片流</returns>
    [HttpGet("{id}/preview")]
    public async Task<IActionResult> PreviewImage(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized();
        }

        var result = await _attachmentService.DownloadFileAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success || result.Data == null)
        {
            return NotFound();
        }

        var downloadInfo = result.Data;
        
        // 添加缓存头
        Response.Headers["Cache-Control"] = "public, max-age=3600";
        Response.Headers["ETag"] = $"\"{id}\"";
        
        return File(downloadInfo.FileStream, downloadInfo.MimeType);
    }

    /// <summary>
    /// 获取附件信息
    /// </summary>
    /// <param name="id">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<AttachmentDto>>> GetAttachment(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<AttachmentDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.GetAttachmentAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 删除附件
    /// </summary>
    /// <param name="id">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteAttachment(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.DeleteAttachmentAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取我的附件列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="fileType">文件类型过滤</param>
    /// <param name="fileName">文件名搜索</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<AttachmentDto>>>> GetMyAttachments(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? fileType = null,
        [FromQuery] string? fileName = null,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<AttachmentDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var queryParams = new AttachmentQueryParams
        {
            Page = page,
            PageSize = pageSize,
            FileType = fileType,
            FileName = fileName,
            StartDate = startDate,
            EndDate = endDate
        };

        var result = await _attachmentService.GetUserAttachmentsAsync(userId.Value, queryParams, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取资讯卡的附件列表
    /// </summary>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>附件列表</returns>
    [HttpGet("infocard/{infoCardId}")]
    public async Task<ActionResult<ApiResponse<List<AttachmentDto>>>> GetInfoCardAttachments(
        int infoCardId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<List<AttachmentDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.GetInfoCardAttachmentsAsync(userId.Value, infoCardId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 关联附件到资讯卡
    /// </summary>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="request">关联请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("infocard/{infoCardId}/attach")]
    public async Task<ActionResult<ApiResponse<bool>>> AttachToInfoCard(
        int infoCardId,
        [FromBody] AttachToInfoCardRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.AttachToInfoCardAsync(userId.Value, infoCardId, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 从资讯卡移除附件关联
    /// </summary>
    /// <param name="infoCardId">资讯卡ID</param>
    /// <param name="attachmentId">附件ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("infocard/{infoCardId}/detach/{attachmentId}")]
    public async Task<ActionResult<ApiResponse<bool>>> DetachFromInfoCard(
        int infoCardId,
        int attachmentId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _attachmentService.DetachFromInfoCardAsync(userId.Value, infoCardId, attachmentId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
