# InfoCard 系统部署指南

本指南提供了 InfoCard 系统在不同环境下的部署方案和最佳实践。

## 📋 部署概述

### 部署方案选择
| 方案 | 适用场景 | 优势 | 复杂度 |
|------|----------|------|--------|
| IIS部署 | Windows环境，中小型应用 | 简单易用，集成度高 | 低 |
| Docker部署 | 跨平台，微服务架构 | 环境一致，易扩展 | 中 |
| 云服务部署 | 大型应用，高可用需求 | 弹性伸缩，托管服务 | 高 |
| Kubernetes | 大规模容器化部署 | 自动化运维，高可用 | 高 |

### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │    Database     │
│    (Nginx)      │────│   (IIS/Nginx)  │────│    (MySQL)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   API Server    │
                       │  (.NET Core)    │
                       └─────────────────┘
```

## 🖥️ IIS部署 (推荐)

### 环境准备
```powershell
# 1. 启用IIS功能
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServer
Enable-WindowsOptionalFeature -Online -FeatureName IIS-CommonHttpFeatures
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpErrors
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpLogging
Enable-WindowsOptionalFeature -Online -FeatureName IIS-RequestFiltering
Enable-WindowsOptionalFeature -Online -FeatureName IIS-StaticContent
Enable-WindowsOptionalFeature -Online -FeatureName IIS-DefaultDocument

# 2. 安装 .NET Core Hosting Bundle
# 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
```

### 应用发布
```bash
# 发布API项目
cd src/InfoCardSystem.API
dotnet publish -c Release -o C:\Webs\ICAPI --self-contained false

# 发布Web项目
cd src/InfoCardSystem.Web
dotnet publish -c Release -o C:\Webs\ICWeb --self-contained false
```

### IIS站点配置
```xml
<!-- C:\Webs\ICAPI\web.config -->
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\InfoCardSystem.API.dll" 
                  stdoutLogEnabled="true" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess" />
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="52428800" />
        </requestFiltering>
      </security>
    </system.webServer>
  </location>
</configuration>
```

### 创建IIS站点
```powershell
# 导入WebAdministration模块
Import-Module WebAdministration

# 创建API站点
New-Website -Name "InfoCard-API" -Port 8001 -PhysicalPath "C:\Webs\ICAPI"

# 创建Web站点
New-Website -Name "InfoCard-Web" -Port 8002 -PhysicalPath "C:\Webs\ICWeb"

# 设置应用程序池
Set-ItemProperty -Path "IIS:\AppPools\InfoCard-API" -Name processModel.identityType -Value ApplicationPoolIdentity
Set-ItemProperty -Path "IIS:\AppPools\InfoCard-Web" -Name processModel.identityType -Value ApplicationPoolIdentity
```

### 配置文件设置
```json
// appsettings.Production.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=InfoCardDB;Uid=infocard;Pwd=your_production_password;CharSet=utf8mb4;"
  },
  "JwtSettings": {
    "SecretKey": "your-production-secret-key-minimum-32-characters",
    "Issuer": "InfoCardSystem",
    "Audience": "InfoCardUsers",
    "ExpirationMinutes": 60
  },
  "AllowedHosts": "*",
  "FileUpload": {
    "MaxFileSize": 52428800,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx"],
    "UploadPath": "wwwroot/uploads"
  }
}
```

## 🐳 Docker部署

### Dockerfile配置
```dockerfile
# src/InfoCardSystem.API/Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["InfoCardSystem.API/InfoCardSystem.API.csproj", "InfoCardSystem.API/"]
COPY ["InfoCardSystem.Core/InfoCardSystem.Core.csproj", "InfoCardSystem.Core/"]
COPY ["InfoCardSystem.Infrastructure/InfoCardSystem.Infrastructure.csproj", "InfoCardSystem.Infrastructure/"]
COPY ["InfoCardSystem.Shared/InfoCardSystem.Shared.csproj", "InfoCardSystem.Shared/"]
RUN dotnet restore "InfoCardSystem.API/InfoCardSystem.API.csproj"
COPY . .
WORKDIR "/src/InfoCardSystem.API"
RUN dotnet build "InfoCardSystem.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "InfoCardSystem.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "InfoCardSystem.API.dll"]
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: infocard-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: InfoCardDB
      MYSQL_USER: infocard
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - infocard-network

  api:
    build:
      context: ./src
      dockerfile: InfoCardSystem.API/Dockerfile
    container_name: infocard-api
    ports:
      - "8081:80"
    depends_on:
      - mysql
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=InfoCardDB;Uid=infocard;Pwd=password;CharSet=utf8mb4;
    volumes:
      - ./uploads:/app/wwwroot/uploads
    networks:
      - infocard-network

  web:
    build:
      context: ./src
      dockerfile: InfoCardSystem.Web/Dockerfile
    container_name: infocard-web
    ports:
      - "5000:80"
    depends_on:
      - api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ApiSettings__BaseUrl=http://api:80
    networks:
      - infocard-network

  nginx:
    image: nginx:alpine
    container_name: infocard-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
      - api
    networks:
      - infocard-network

volumes:
  mysql_data:

networks:
  infocard-network:
    driver: bridge
```

### Nginx配置
```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        server api:80;
    }
    
    upstream web_backend {
        server web:80;
    }
    
    server {
        listen 80;
        server_name your-domain.com;
        
        # API代理
        location /api/ {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Web应用代理
        location / {
            proxy_pass http://web_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### 部署命令
```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down

# 更新服务
docker-compose pull
docker-compose up -d --force-recreate
```

## ☁️ 云服务部署

### Azure部署
```yaml
# azure-pipelines.yml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'

steps:
- task: DotNetCoreCLI@2
  displayName: 'Restore packages'
  inputs:
    command: 'restore'
    projects: '**/*.csproj'

- task: DotNetCoreCLI@2
  displayName: 'Build application'
  inputs:
    command: 'build'
    projects: '**/*.csproj'
    arguments: '--configuration $(buildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: 'Publish application'
  inputs:
    command: 'publish'
    projects: 'src/InfoCardSystem.API/InfoCardSystem.API.csproj'
    arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'

- task: PublishBuildArtifacts@1
  displayName: 'Publish artifacts'
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
```

### AWS部署
```json
{
  "AWSTemplateFormatVersion": "2010-09-09",
  "Resources": {
    "InfoCardECS": {
      "Type": "AWS::ECS::Cluster",
      "Properties": {
        "ClusterName": "InfoCard-Cluster"
      }
    },
    "InfoCardRDS": {
      "Type": "AWS::RDS::DBInstance",
      "Properties": {
        "DBInstanceIdentifier": "infocard-db",
        "DBInstanceClass": "db.t3.micro",
        "Engine": "mysql",
        "MasterUsername": "admin",
        "MasterUserPassword": "password",
        "AllocatedStorage": "20"
      }
    }
  }
}
```

## 🔧 生产环境优化

### 性能优化
```json
// appsettings.Production.json
{
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxConcurrentUpgradedConnections": 100,
      "MaxRequestBodySize": 52428800
    }
  },
  "ResponseCaching": {
    "Enabled": true,
    "DefaultDuration": 300
  }
}
```

### 安全配置
```csharp
// Program.cs
app.UseHsts();
app.UseHttpsRedirection();
app.UseSecurityHeaders();
app.UseRateLimiting();
```

### 监控配置
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "logs/infocard-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30
        }
      }
    ]
  }
}
```

## 📊 健康检查

### 健康检查端点
```csharp
// Program.cs
builder.Services.AddHealthChecks()
    .AddDbContextCheck<ApplicationDbContext>()
    .AddUrlGroup(new Uri("https://api.example.com/health"), "External API");

app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});
```

### 监控脚本
```bash
#!/bin/bash
# health-check.sh

API_URL="http://localhost:5001/health"
WEB_URL="http://localhost:5000"

# 检查API健康状态
if curl -f $API_URL > /dev/null 2>&1; then
    echo "API is healthy"
else
    echo "API is down"
    # 发送告警
    curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
         -H 'Content-type: application/json' \
         --data '{"text":"InfoCard API is down!"}'
fi

# 检查Web应用
if curl -f $WEB_URL > /dev/null 2>&1; then
    echo "Web is healthy"
else
    echo "Web is down"
fi
```

## 🔄 自动化部署

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal
      
    - name: Publish
      run: dotnet publish -c Release -o ./publish
      
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /var/www/infocard
          git pull origin main
          dotnet publish -c Release
          sudo systemctl restart infocard-api
          sudo systemctl restart infocard-web
```

## 🚨 故障排除

### 常见问题
1. **应用无法启动**
   ```bash
   # 检查日志
   tail -f /var/log/infocard/app.log
   
   # 检查端口占用
   netstat -tlnp | grep :5000
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   mysql -h localhost -u infocard -p InfoCardDB
   
   # 检查防火墙
   sudo ufw status
   ```

3. **文件上传失败**
   ```bash
   # 检查目录权限
   ls -la /var/www/infocard/wwwroot/uploads
   
   # 设置权限
   sudo chmod 755 /var/www/infocard/wwwroot/uploads
   ```

## 📞 技术支持

如需部署支持，请联系：
- **技术支持**: 本地开发环境
- **本地文档**: http://localhost:8082
- **API文档**: http://localhost:8081/swagger
