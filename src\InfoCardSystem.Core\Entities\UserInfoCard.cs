using InfoCardSystem.Shared.Enums;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户资讯卡实体
/// </summary>
public class UserInfoCard : BaseEntity
{
    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// 资讯卡类型
    /// </summary>
    public InfoCardType InfoCardType { get; set; }
    
    /// <summary>
    /// 原始发布者ID
    /// </summary>
    public int OriginalPublisherId { get; set; }
    
    /// <summary>
    /// 直接发布者ID（转发时为转发者）
    /// </summary>
    public int DirectPublisherId { get; set; }
    
    /// <summary>
    /// 父资讯卡ID（转发时指向原始资讯卡）
    /// </summary>
    public int? ParentInfoCardId { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
    
    /// <summary>
    /// 是否允许转发
    /// </summary>
    public bool AllowForward { get; set; } = true;
    
    /// <summary>
    /// 资讯卡状态
    /// </summary>
    public InfoCardStatus InfoCardStatus { get; set; } = InfoCardStatus.Active;
    
    // 导航属性
    
    /// <summary>
    /// 原始发布者
    /// </summary>
    public virtual AppUser OriginalPublisher { get; set; } = null!;
    
    /// <summary>
    /// 直接发布者
    /// </summary>
    public virtual AppUser DirectPublisher { get; set; } = null!;
    
    /// <summary>
    /// 父资讯卡
    /// </summary>
    public virtual UserInfoCard? ParentInfoCard { get; set; }
    
    /// <summary>
    /// 子资讯卡（转发的资讯卡）
    /// </summary>
    public virtual ICollection<UserInfoCard> ChildInfoCards { get; set; } = new List<UserInfoCard>();
    
    /// <summary>
    /// 接收者记录
    /// </summary>
    public virtual ICollection<UserInfoCardRecipient> Recipients { get; set; } = new List<UserInfoCardRecipient>();
    
    /// <summary>
    /// 收藏记录
    /// </summary>
    public virtual ICollection<UserInfoCardFavorite> Favorites { get; set; } = new List<UserInfoCardFavorite>();
    
    /// <summary>
    /// 附件关联
    /// </summary>
    public virtual ICollection<UserInfoCardAttachment> Attachments { get; set; } = new List<UserInfoCardAttachment>();
}
