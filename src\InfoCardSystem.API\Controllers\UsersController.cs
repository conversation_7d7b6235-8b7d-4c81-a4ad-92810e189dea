using System.Security.Claims;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.User;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 用户管理控制器 - 处理用户资料管理相关操作
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[Authorize]
[SwaggerTag("用户管理相关接口，包括资料查询、更新、密码修改等功能（需要认证）")]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserService userService, ILogger<UsersController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前用户资料
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户资料</returns>
    /// <remarks>
    /// 获取当前登录用户的详细资料信息。
    ///
    /// 返回信息包括：
    /// - 基本信息：用户名、邮箱、手机号
    /// - 个人资料：显示名称、个人简介
    /// - 账户状态：注册时间、最后登录时间
    /// - 统计信息：好友数量、信息卡数量
    ///
    /// 需要在请求头中提供有效的JWT令牌。
    /// </remarks>
    /// <response code="200">成功获取用户资料</response>
    /// <response code="401">未授权，需要登录</response>
    /// <response code="404">用户不存在</response>
    /// <response code="500">服务器内部错误</response>
    [HttpGet("profile")]
    [SwaggerOperation(
        Summary = "获取当前用户资料",
        Description = "获取当前登录用户的详细资料信息",
        OperationId = "GetCurrentUserProfile",
        Tags = new[] { "User Management" }
    )]
    [SwaggerResponse(200, "成功获取用户资料", typeof(ApiResponse<UserProfileDto>))]
    [SwaggerResponse(401, "未授权，需要登录")]
    [SwaggerResponse(404, "用户不存在")]
    [SwaggerResponse(500, "服务器内部错误")]
    public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetCurrentUserProfile(CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<UserProfileDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _userService.GetUserProfileAsync(userId.Value, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 根据自定义用户ID获取用户资料
    /// </summary>
    /// <param name="customUserId">自定义用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户资料</returns>
    [HttpGet("{customUserId}")]
    public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetUserProfile(
        string customUserId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(customUserId))
        {
            return BadRequest(ApiResponse<UserProfileDto>.ErrorResult("用户ID不能为空", "INVALID_USER_ID"));
        }

        var result = await _userService.GetUserProfileByCustomIdAsync(customUserId, cancellationToken);
        
        if (!result.Success)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 更新当前用户资料
    /// </summary>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的用户资料</returns>
    [HttpPut("profile")]
    public async Task<ActionResult<ApiResponse<UserProfileDto>>> UpdateCurrentUserProfile(
        [FromBody] UpdateUserProfileRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<UserProfileDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<UserProfileDto>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _userService.UpdateUserProfileAsync(userId.Value, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="request">修改密码请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("change-password")]
    public async Task<ActionResult<ApiResponse<bool>>> ChangePassword(
        [FromBody] ChangePasswordRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<bool>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _userService.ChangePasswordAsync(userId.Value, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<PagedResult<UserProfileDto>>>> SearchUsers(
        [FromQuery] string? keyword = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var request = new SearchUsersRequest
        {
            Keyword = keyword,
            Page = page,
            PageSize = pageSize
        };

        var result = await _userService.SearchUsersAsync(request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 上传头像
    /// </summary>
    /// <param name="avatar">头像文件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>头像URL</returns>
    [HttpPost("avatar")]
    public async Task<ActionResult<ApiResponse<string>>> UploadAvatar(
        IFormFile avatar,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<string>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        if (avatar == null || avatar.Length == 0)
        {
            return BadRequest(ApiResponse<string>.ErrorResult("请选择头像文件", "INVALID_FILE"));
        }

        // 验证文件类型
        var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
        if (!allowedTypes.Contains(avatar.ContentType.ToLowerInvariant()))
        {
            return BadRequest(ApiResponse<string>.ErrorResult("只支持 JPEG、PNG、GIF 格式的图片", "INVALID_FILE_TYPE"));
        }

        // 验证文件大小 (5MB)
        if (avatar.Length > 5 * 1024 * 1024)
        {
            return BadRequest(ApiResponse<string>.ErrorResult("头像文件大小不能超过5MB", "FILE_TOO_LARGE"));
        }

        using var stream = avatar.OpenReadStream();
        var result = await _userService.UploadAvatarAsync(userId.Value, stream, avatar.FileName, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取用户二维码
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户二维码</returns>
    [HttpGet("qrcode")]
    public async Task<ActionResult<ApiResponse<UserQRCodeDto>>> GetUserQRCode(CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<UserQRCodeDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _userService.GetUserQRCodeAsync(userId.Value, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 匹配联系人
    /// </summary>
    /// <param name="request">联系人匹配请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的用户列表</returns>
    [HttpPost("contacts-match")]
    public async Task<ActionResult<ApiResponse<List<UserProfileDto>>>> MatchContacts(
        [FromBody] ContactsMatchRequest request,
        CancellationToken cancellationToken = default)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<List<UserProfileDto>>.ErrorResult(
                "请求参数验证失败",
                "VALIDATION_ERROR",
                errors));
        }

        var result = await _userService.MatchContactsAsync(request, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
