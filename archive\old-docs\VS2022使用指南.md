# Visual Studio 2022 使用指南

## 🎯 概述

本指南详细说明如何在Visual Studio 2022中正确打开、配置和调试InfoCard项目，确保API和Web应用能够正常协同工作。

## 📋 端口配置总览

### 开发环境端口
- **API服务**: https://localhost:8001 (主要) / http://localhost:8000 (备用)
- **Web应用**: https://localhost:8002 (主要) / http://localhost:8002 (HTTP)

### 生产环境端口
- **API服务**: http://localhost:8001
- **Web应用**: http://localhost:8002

## 🚀 VS2022 开发流程

### 方法一：推荐的开发流程

#### 1. 准备开发环境
```bash
# 运行准备脚本
start-for-vs2022.bat
```

此脚本会：
- 检查并启动MySQL服务
- 清理端口占用
- 构建解决方案
- 在后台启动API服务 (端口8001)
- 可选择自动打开VS2022

#### 2. 在VS2022中调试Web项目
1. 打开 `InfoCardSystem.sln`
2. 在解决方案资源管理器中右键点击 `InfoCardSystem.Web`
3. 选择"设为启动项目"
4. 按 F5 开始调试

Web应用将在端口8002启动，并自动连接到已运行的API服务。

### 方法二：VS2022多项目启动

#### 1. 配置多项目启动
1. 右键点击解决方案 `InfoCardSystem`
2. 选择"属性"
3. 在"启动项目"中选择"多个启动项目"
4. 设置以下项目的操作为"启动"：
   - `InfoCardSystem.API`
   - `InfoCardSystem.Web`
5. 点击"确定"

#### 2. 启动调试
按 F5，VS2022将同时启动API和Web项目。

## 🔧 配置验证

### 运行配置检查
```bash
verify-config.bat
```

此脚本会检查所有配置文件中的端口设置，确保：
- API项目配置为端口8001
- Web项目配置为端口8002
- 没有遗留的旧端口引用

### 手动验证步骤

#### 1. 检查API配置
- `src/InfoCardSystem.API/Properties/launchSettings.json` → 端口8001
- `src/InfoCardSystem.API/appsettings.json` → BaseUrl: 8001
- `src/InfoCardSystem.API/appsettings.Development.json` → BaseUrl: 8001

#### 2. 检查Web配置
- `src/InfoCardSystem.Web/Properties/launchSettings.json` → 端口8002
- `src/InfoCardSystem.Web/appsettings.json` → ApiSettings.BaseUrl: 8001
- `src/InfoCardSystem.Web/appsettings.Development.json` → ApiSettings.BaseUrl: 8001
- `src/InfoCardSystem.Web/Program.cs` → 默认URL: 8001

## 🐛 调试技巧

### 1. 设置断点
- **API调试**: 在Controller方法中设置断点
- **Web调试**: 在Razor页面的代码块中设置断点

### 2. 查看日志
- **输出窗口**: 查看ASP.NET Core日志
- **调试输出**: 查看自定义调试信息

### 3. 网络调试
- **浏览器开发者工具**: 查看HTTP请求/响应
- **Swagger UI**: 测试API端点 (https://localhost:8001/swagger)

## 🔍 常见问题排除

### 1. 端口占用问题
**症状**: 启动时提示端口被占用
**解决方案**:
```bash
# 检查端口占用
netstat -ano | findstr :8001
netstat -ano | findstr :8002

# 停止占用进程
stop-test-env.bat
```

### 2. API连接失败
**症状**: Web应用无法连接到API
**解决方案**:
1. 确认API服务正在运行 (https://localhost:8001/health)
2. 检查Web配置中的API URL
3. 验证CORS设置

### 3. 数据库连接问题
**症状**: API启动时数据库连接失败
**解决方案**:
1. 确认MySQL服务正在运行
2. 检查连接字符串配置
3. 验证数据库是否存在

### 4. 项目加载问题
**症状**: VS2022中项目显示为不可用
**解决方案**:
1. 确认.NET 9.0 SDK已安装
2. 重新加载项目
3. 清理并重新构建解决方案

## 📝 开发最佳实践

### 1. 启动顺序
推荐按以下顺序启动：
1. MySQL数据库
2. API服务 (端口8001)
3. Web应用 (端口8002)

### 2. 代码修改后的操作
1. **API代码修改**: 重启API调试会话
2. **Web代码修改**: Blazor支持热重载，通常无需重启
3. **配置文件修改**: 需要重启相应的服务

### 3. 测试流程
1. **单元测试**: 使用VS2022的测试资源管理器
2. **API测试**: 使用Swagger UI
3. **集成测试**: 使用Web应用进行端到端测试

## 🛠️ 可用脚本

### 开发脚本
- `start-for-vs2022.bat` - 准备VS2022开发环境
- `verify-config.bat` - 验证配置正确性
- `test-build.bat` - 测试项目构建

### 发布脚本
- `publish-to-webs-fixed.bat` - 发布到C:\Webs目录
- `start-test-env.bat` - 启动发布的应用
- `quick-test.bat` - 快速测试服务状态
- `stop-test-env.bat` - 停止所有服务

## 📊 开发环境检查清单

### VS2022准备
- [ ] Visual Studio 2022已安装
- [ ] .NET 9.0 SDK已安装
- [ ] ASP.NET Core工作负载已安装

### 服务准备
- [ ] MySQL服务正在运行
- [ ] 端口8001和8002可用
- [ ] 项目构建成功

### 配置验证
- [ ] 所有配置文件端口正确
- [ ] 解决方案包含所有项目
- [ ] 启动项目设置正确

### 功能测试
- [ ] API健康检查通过
- [ ] Swagger UI可访问
- [ ] Web应用正常加载
- [ ] API和Web通信正常

---

## 🎉 开始开发

完成上述检查后，您就可以在VS2022中愉快地开发InfoCard项目了！

**推荐工作流程**:
1. 运行 `start-for-vs2022.bat`
2. 在VS2022中设置Web项目为启动项目
3. 按F5开始调试
4. 在浏览器中测试功能
5. 使用Swagger UI测试API

如果遇到问题，请参考上述故障排除部分或运行相应的验证脚本。
