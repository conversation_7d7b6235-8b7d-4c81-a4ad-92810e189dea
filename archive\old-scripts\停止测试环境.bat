@echo off
chcp 65001 >nul
echo ========================================
echo InfoCard 测试环境停止脚本
echo ========================================
echo.

echo 正在停止InfoCard服务...
echo.

REM 停止API服务
echo 停止API服务 (端口7001)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7001"') do (
    taskkill /PID %%a /F >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✓ API服务已停止 (PID: %%a)
    )
)

REM 停止Web应用
echo 停止Web应用 (端口5001)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5001"') do (
    taskkill /PID %%a /F >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✓ Web应用已停止 (PID: %%a)
    )
)

REM 停止所有InfoCardSystem进程
echo 停止所有InfoCardSystem进程...
taskkill /IM "InfoCardSystem.API.exe" /F >nul 2>&1
taskkill /IM "InfoCardSystem.Web.exe" /F >nul 2>&1

echo.
echo 验证服务状态...
netstat -ano | findstr ":7001" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ✓ 端口7001已释放
) else (
    echo ⚠️ 端口7001仍被占用
)

netstat -ano | findstr ":5001" | findstr "LISTENING" >nul
if %errorLevel% neq 0 (
    echo ✓ 端口5001已释放
) else (
    echo ⚠️ 端口5001仍被占用
)

echo.
echo ========================================
echo 测试环境已停止
echo ========================================
echo.

pause
