using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Shared.Constants;
using System.Net;
using System.Text.Json;

namespace InfoCardSystem.API.Middleware;

/// <summary>
/// 全局异常处理中间件
/// </summary>
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发生未处理的异常: {Message}", ex.Message);
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";
        
        var response = exception switch
        {
            ArgumentException => ApiResponse<object>.ErrorResult("请求参数无效", ErrorCodes.VALIDATION_001),
            UnauthorizedAccessException => ApiResponse<object>.ErrorResult("未授权访问", ErrorCodes.AUTH_001),
            KeyNotFoundException => ApiResponse<object>.ErrorResult("请求的资源不存在", ErrorCodes.NOT_FOUND_001),
            InvalidOperationException => ApiResponse<object>.ErrorResult("操作无效", ErrorCodes.BUSINESS_001),
            TimeoutException => ApiResponse<object>.ErrorResult("请求超时", ErrorCodes.TIMEOUT_001),
            _ => ApiResponse<object>.ErrorResult("服务器内部错误", ErrorCodes.SERVER_001)
        };

        context.Response.StatusCode = exception switch
        {
            ArgumentException => (int)HttpStatusCode.BadRequest,
            UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
            KeyNotFoundException => (int)HttpStatusCode.NotFound,
            InvalidOperationException => (int)HttpStatusCode.BadRequest,
            TimeoutException => (int)HttpStatusCode.RequestTimeout,
            _ => (int)HttpStatusCode.InternalServerError
        };

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}

/// <summary>
/// 全局异常处理中间件扩展
/// </summary>
public static class GlobalExceptionMiddlewareExtensions
{
    public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<GlobalExceptionMiddleware>();
    }
}
