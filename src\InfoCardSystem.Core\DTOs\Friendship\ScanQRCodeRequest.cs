using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.Friendship;

/// <summary>
/// 扫码添加好友请求
/// </summary>
public class ScanQRCodeRequest
{
    /// <summary>
    /// 二维码数据
    /// </summary>
    [Required(ErrorMessage = "二维码数据不能为空")]
    public string QRCodeData { get; set; } = string.Empty;

    /// <summary>
    /// 好友请求消息
    /// </summary>
    [StringLength(200, ErrorMessage = "消息长度不能超过200个字符")]
    public string Message { get; set; } = "我是通过扫描二维码添加您为好友的";
}
