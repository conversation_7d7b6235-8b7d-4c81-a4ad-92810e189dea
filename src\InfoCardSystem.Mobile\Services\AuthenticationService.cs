using InfoCardSystem.Mobile.Models;
using Microsoft.Extensions.Logging;
using System.IdentityModel.Tokens.Jwt;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 认证服务实现类
/// 负责用户身份验证、JWT令牌管理、用户状态维护等核心认证功能
///
/// 主要功能：
/// - 用户登录/注册/登出
/// - JWT访问令牌和刷新令牌管理
/// - 令牌自动刷新机制
/// - 用户信息安全存储
/// - 认证状态检查
///
/// 技术特性：
/// - 使用安全存储保护敏感信息
/// - 支持令牌过期自动刷新
/// - 完整的错误处理和日志记录
/// - 线程安全的用户状态管理
/// </summary>
/// <remarks>
/// 此服务是整个应用的认证核心，所有需要身份验证的操作都依赖于此服务。
/// 令牌会在过期前5分钟自动刷新，确保用户体验的连续性。
/// </remarks>
public class AuthenticationService : IAuthenticationService
{
    #region 私有字段

    /// <summary>API客户端，用于与后端服务通信</summary>
    private readonly IInfoCardApiClient _apiClient;

    /// <summary>安全存储服务，用于保存敏感信息如令牌和用户数据</summary>
    private readonly ISecureStorageService _secureStorage;

    /// <summary>偏好设置服务，用于保存非敏感的用户偏好</summary>
    private readonly IPreferencesService _preferences;

    /// <summary>日志记录器，用于记录认证相关的操作和错误</summary>
    private readonly ILogger<AuthenticationService> _logger;

    /// <summary>访问令牌在安全存储中的键名</summary>
    private const string ACCESS_TOKEN_KEY = "access_token";

    /// <summary>刷新令牌在安全存储中的键名</summary>
    private const string REFRESH_TOKEN_KEY = "refresh_token";

    /// <summary>用户信息在安全存储中的键名</summary>
    private const string USER_INFO_KEY = "user_info";

    /// <summary>令牌过期时间在偏好设置中的键名</summary>
    private const string TOKEN_EXPIRY_KEY = "token_expiry";

    /// <summary>当前登录用户信息的内存缓存</summary>
    private UserInfo? _currentUser;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化认证服务实例
    /// </summary>
    /// <param name="apiClient">API客户端服务，用于与后端通信</param>
    /// <param name="secureStorage">安全存储服务，用于保存敏感信息</param>
    /// <param name="preferences">偏好设置服务，用于保存用户偏好</param>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public AuthenticationService(
        IInfoCardApiClient apiClient,
        ISecureStorageService secureStorage,
        IPreferencesService preferences,
        ILogger<AuthenticationService> logger)
    {
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _secureStorage = secureStorage ?? throw new ArgumentNullException(nameof(secureStorage));
        _preferences = preferences ?? throw new ArgumentNullException(nameof(preferences));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _logger.LogDebug("认证服务已初始化");
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 检查用户是否已通过身份验证
    /// </summary>
    /// <returns>
    /// 如果用户已认证且令牌有效则返回true，否则返回false
    /// </returns>
    /// <remarks>
    /// 此方法会执行以下检查：
    /// 1. 验证是否存在访问令牌
    /// 2. 检查令牌是否过期（提前5分钟）
    /// 3. 如果令牌过期，尝试使用刷新令牌自动刷新
    /// 4. 如果刷新失败，自动执行登出操作
    /// 5. 为API客户端设置有效的认证令牌
    /// </remarks>
    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            // 从安全存储获取访问令牌
            var accessToken = await _secureStorage.GetAsync(ACCESS_TOKEN_KEY);

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogDebug("没有访问令牌，用户未认证");
                return false;
            }

            // 检查令牌是否过期（提前5分钟检查）
            if (IsTokenExpired(accessToken))
            {
                _logger.LogDebug("访问令牌已过期，尝试自动刷新");

                // 尝试刷新令牌
                var refreshResult = await RefreshTokenAsync();
                if (!refreshResult.Success)
                {
                    _logger.LogWarning("刷新令牌失败，用户需要重新登录");
                    await LogoutAsync(); // 清除无效的认证信息
                    return false;
                }
            }

            // 为API客户端设置当前有效的认证令牌
            var currentToken = await _secureStorage.GetAsync(ACCESS_TOKEN_KEY);
            if (!string.IsNullOrEmpty(currentToken))
            {
                _apiClient.SetAuthToken(currentToken);
                _logger.LogDebug("用户认证状态有效");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查认证状态时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 执行用户登录操作
    /// </summary>
    /// <param name="request">登录请求，包含用户名/邮箱/手机号和密码</param>
    /// <returns>包含登录结果和用户信息的API响应</returns>
    /// <remarks>
    /// 登录成功后会自动执行以下操作：
    /// 1. 将访问令牌和刷新令牌保存到安全存储
    /// 2. 将用户信息保存到安全存储
    /// 3. 为API客户端设置认证令牌
    /// 4. 缓存用户信息到内存
    /// </remarks>
    /// <exception cref="ArgumentNullException">当request为null时抛出</exception>
    public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        try
        {
            _logger.LogInformation("开始用户登录: {Username}", request.UsernameOrEmailOrPhone);

            // 调用API执行登录
            var response = await _apiClient.LoginAsync(request);

            if (response.Success && response.Data != null)
            {
                // 保存认证信息到本地存储
                await SaveAuthenticationDataAsync(response.Data);

                // 为API客户端设置认证令牌，用于后续API调用
                _apiClient.SetAuthToken(response.Data.AccessToken);

                _logger.LogInformation("用户登录成功: {Username}", request.UsernameOrEmailOrPhone);
            }
            else
            {
                _logger.LogWarning("用户登录失败: {Username}, 原因: {Message}",
                    request.UsernameOrEmailOrPhone, response.Message);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登录过程中发生异常: {Username}", request.UsernameOrEmailOrPhone);
            return ApiResponse<LoginResponse>.ErrorResult("登录失败，请重试");
        }
    }

    public async Task<ApiResponse<RegisterResponse>> RegisterAsync(RegisterRequest request)
    {
        try
        {
            _logger.LogInformation("开始用户注册: {Username}", request.Username);

            var response = await _apiClient.RegisterAsync(request);

            if (response.Success)
            {
                _logger.LogInformation("用户注册成功: {Username}", request.Username);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户注册失败: {Username}", request.Username);
            return ApiResponse<RegisterResponse>.ErrorResult("注册失败，请重试");
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            _logger.LogInformation("用户登出");

            // 清除本地存储的认证信息
            await _secureStorage.RemoveAsync(ACCESS_TOKEN_KEY);
            await _secureStorage.RemoveAsync(REFRESH_TOKEN_KEY);
            await _secureStorage.RemoveAsync(USER_INFO_KEY);
            _preferences.Remove(TOKEN_EXPIRY_KEY);

            // 清除API客户端的认证令牌
            _apiClient.ClearAuthToken();

            // 清除当前用户信息
            _currentUser = null;

            _logger.LogInformation("用户登出完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户登出失败");
        }
    }

    public async Task<ApiResponse<TokenResponse>> RefreshTokenAsync()
    {
        try
        {
            var refreshToken = await _secureStorage.GetAsync(REFRESH_TOKEN_KEY);
            
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("没有刷新令牌，无法刷新");
                return ApiResponse<TokenResponse>.ErrorResult("没有刷新令牌");
            }

            _logger.LogDebug("开始刷新访问令牌");

            var response = await _apiClient.RefreshTokenAsync(refreshToken);

            if (response.Success && response.Data != null)
            {
                // 保存新的令牌
                await _secureStorage.SetAsync(ACCESS_TOKEN_KEY, response.Data.AccessToken);
                await _secureStorage.SetAsync(REFRESH_TOKEN_KEY, response.Data.RefreshToken);
                
                // 计算并保存过期时间
                var expiryTime = DateTime.UtcNow.AddSeconds(response.Data.ExpiresIn);
                _preferences.SetDateTime(TOKEN_EXPIRY_KEY, expiryTime);

                // 设置API客户端的认证令牌
                _apiClient.SetAuthToken(response.Data.AccessToken);

                _logger.LogDebug("访问令牌刷新成功");
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌失败");
            return ApiResponse<TokenResponse>.ErrorResult("刷新令牌失败");
        }
    }

    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        try
        {
            if (_currentUser != null)
            {
                return _currentUser;
            }

            var userInfoJson = await _secureStorage.GetAsync(USER_INFO_KEY);
            if (!string.IsNullOrEmpty(userInfoJson))
            {
                _currentUser = System.Text.Json.JsonSerializer.Deserialize<UserInfo>(userInfoJson);
                return _currentUser;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取当前用户信息失败");
            return null;
        }
    }

    public async Task<string?> GetAccessTokenAsync()
    {
        try
        {
            return await _secureStorage.GetAsync(ACCESS_TOKEN_KEY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取访问令牌失败");
            return null;
        }
    }

    public async Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            _logger.LogInformation("发送忘记密码请求: {EmailOrPhone}", request.EmailOrPhone);

            var response = await _apiClient.ForgotPasswordAsync(request);

            if (response.Success)
            {
                _logger.LogInformation("忘记密码请求发送成功: {EmailOrPhone}", request.EmailOrPhone);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送忘记密码请求失败: {EmailOrPhone}", request.EmailOrPhone);
            return ApiResponse<bool>.ErrorResult("发送请求失败，请重试");
        }
    }

    public async Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            _logger.LogInformation("重置密码");

            var response = await _apiClient.ResetPasswordAsync(request);

            if (response.Success)
            {
                _logger.LogInformation("密码重置成功");
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码失败");
            return ApiResponse<bool>.ErrorResult("重置密码失败，请重试");
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 保存登录成功后的认证数据到本地存储
    /// </summary>
    /// <param name="loginResponse">登录响应，包含令牌和用户信息</param>
    /// <returns>异步任务</returns>
    /// <exception cref="ArgumentNullException">当loginResponse为null时抛出</exception>
    /// <exception cref="Exception">当保存过程中发生错误时抛出</exception>
    /// <remarks>
    /// 此方法会保存以下信息：
    /// 1. 访问令牌和刷新令牌到安全存储
    /// 2. 用户信息序列化后保存到安全存储
    /// 3. 令牌过期时间保存到偏好设置
    /// 4. 更新内存中的用户信息缓存
    /// </remarks>
    private async Task SaveAuthenticationDataAsync(LoginResponse loginResponse)
    {
        ArgumentNullException.ThrowIfNull(loginResponse);

        try
        {
            // 保存访问令牌和刷新令牌到安全存储
            await _secureStorage.SetAsync(ACCESS_TOKEN_KEY, loginResponse.AccessToken);
            await _secureStorage.SetAsync(REFRESH_TOKEN_KEY, loginResponse.RefreshToken);

            // 序列化并保存用户信息到安全存储
            var userInfoJson = System.Text.Json.JsonSerializer.Serialize(loginResponse.User);
            await _secureStorage.SetAsync(USER_INFO_KEY, userInfoJson);

            // 更新内存中的用户信息缓存
            _currentUser = loginResponse.User;

            // 计算令牌过期时间并保存到偏好设置
            var expiryTime = DateTime.UtcNow.AddSeconds(loginResponse.ExpiresIn);
            _preferences.SetDateTime(TOKEN_EXPIRY_KEY, expiryTime);

            _logger.LogDebug("认证数据保存成功，用户: {Username}", loginResponse.User.Username);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存认证数据时发生异常");
            throw; // 重新抛出异常，让调用者处理
        }
    }

    /// <summary>
    /// 检查JWT令牌是否已过期
    /// </summary>
    /// <param name="token">要检查的JWT令牌字符串</param>
    /// <returns>如果令牌已过期或即将过期（5分钟内）则返回true，否则返回false</returns>
    /// <remarks>
    /// 此方法会解析JWT令牌并检查其过期时间。
    /// 为了确保用户体验的连续性，会在令牌实际过期前5分钟就认为其已过期，
    /// 这样可以提前触发令牌刷新机制。
    ///
    /// 如果令牌格式无效或解析失败，会返回true（认为已过期），
    /// 这是一种安全的失败处理方式。
    /// </remarks>
    private bool IsTokenExpired(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
        {
            _logger.LogDebug("令牌为空，认为已过期");
            return true;
        }

        try
        {
            // 使用JWT处理器解析令牌
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            // 获取令牌过期时间
            var expiry = jsonToken.ValidTo;
            var now = DateTime.UtcNow;

            // 提前5分钟刷新令牌，确保用户体验的连续性
            var isExpired = expiry <= now.AddMinutes(5);

            if (isExpired)
            {
                _logger.LogDebug("令牌已过期或即将过期，过期时间: {Expiry}, 当前时间: {Now}",
                    expiry, now);
            }

            return isExpired;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析JWT令牌时发生异常，认为令牌已过期");
            return true; // 安全的失败处理：如果无法解析，认为已过期
        }
    }

    #endregion
}
