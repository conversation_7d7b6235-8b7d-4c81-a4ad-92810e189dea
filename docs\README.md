# InfoCard 系统文档中心

欢迎来到 InfoCard 信息卡片分享系统的文档中心。这里包含了系统的完整文档，帮助您快速了解、安装、开发和部署 InfoCard 系统。

## 📚 文档导航

### 🚀 快速开始
- [快速开始指南](../QUICKSTART.md) - 5分钟快速体验系统
- [系统概述](#系统概述) - 了解 InfoCard 系统的核心功能和特性

### 📋 安装指南
- [完整安装指南](installation/README.md) - 详细的安装步骤和环境配置

### 🚀 部署文档
- [部署概述](deployment/README.md) - 部署方案选择
- [IIS部署指南](deployment/IIS-Configuration-Guide.md) - Windows IIS部署指南
- [部署总结](deployment/Deployment-Summary.md) - 部署要点总结

### 💻 开发指南
- [Visual Studio 2022 开发指南](development/visual-studio-2022-guide.md) - VS2022开发环境配置
- [前端开发](development/frontend-development.md) - 前端开发指南
- [移动端开发](development/mobile-development.md) - MAUI移动端开发
- [测试指南](development/testing.md) - 单元测试和集成测试
- [调试技巧](development/debugging.md) - 开发调试技巧

### 🔧 API文档
- [API概述](api/README.md) - API接口总览
- [认证授权](api/authentication.md) - 用户认证和授权
- [用户管理](api/users.md) - 用户相关接口
- [好友管理](api/friends.md) - 好友系统接口
- [群组管理](api/groups.md) - 群组管理接口
- [资讯卡管理](api/infocards.md) - 资讯卡相关接口
- [附件管理](api/attachments.md) - 文件上传和管理
- [错误码说明](api/error-codes.md) - API错误码参考

### 🗄️ 数据库设计
- [数据库概述](database/README.md) - 数据库设计说明
- [表结构设计](database/schema.md) - 详细表结构
- [数据字典](database/data-dictionary.md) - 字段说明
- [索引设计](database/indexes.md) - 数据库索引策略
- [数据迁移](database/migrations.md) - 数据库版本迁移

### 🏗️ 架构设计
- [系统架构](architecture/README.md) - 整体架构设计
- [技术选型](architecture/technology-stack.md) - 技术栈说明
- [模块设计](architecture/modules.md) - 系统模块划分
- [安全设计](architecture/security.md) - 安全架构设计
- [性能优化](architecture/performance.md) - 性能优化策略
- [扩展性设计](architecture/scalability.md) - 系统扩展性考虑

### 📱 客户端文档
- [Web前端](../src/InfoCardSystem.Web/README.md) - Blazor Web应用
- [移动端应用](../src/InfoCardSystem.Mobile/README.md) - .NET MAUI应用
- [微信小程序](../src/InfoCardSystem.MiniProgram/README.md) - 微信小程序
- [快应用](../src/InfoCardSystem.QuickApp/README.md) - 快应用开发

### 📖 用户手册
- [用户指南](user-guide/README.md) - 用户使用指南
- [功能介绍](user-guide/features.md) - 详细功能说明
- [常见问题](user-guide/faq.md) - 用户常见问题
- [使用技巧](user-guide/tips.md) - 高效使用技巧

### 🔄 运维管理
- [系统监控](operations/monitoring.md) - 系统监控指南
- [日志管理](operations/logging.md) - 日志收集和分析
- [备份恢复](operations/backup.md) - 数据备份和恢复
- [性能调优](operations/performance-tuning.md) - 系统性能调优
- [故障排除](operations/troubleshooting.md) - 常见故障处理

### 🤝 贡献指南
- [贡献指南](contributing/README.md) - 如何参与项目开发
- [代码提交](contributing/commit-guidelines.md) - 代码提交规范
- [问题反馈](contributing/issue-reporting.md) - Bug反馈指南
- [功能建议](contributing/feature-requests.md) - 新功能建议

## 📋 文档版本

| 版本 | 发布日期 | 主要更新 |
|------|----------|----------|
| v1.0.0 | 2024-01-15 | 初始版本发布 |

## 🔍 快速查找

### 我想要...
- **快速体验系统** → [快速安装](installation/quick-start.md)
- **部署到生产环境** → [IIS部署指南](deployment/iis.md)
- **开发新功能** → [开发指南](development/README.md)
- **了解API接口** → [API文档](api/README.md)
- **解决问题** → [故障排除](operations/troubleshooting.md)

### 按角色查看
- **系统管理员** → [部署文档](deployment/) + [运维管理](operations/)
- **开发人员** → [开发指南](development/) + [API文档](api/)
- **最终用户** → [用户手册](user-guide/)
- **项目经理** → [系统概述](overview.md) + [架构设计](architecture/)

## 📞 获取帮助

如果您在文档中没有找到需要的信息，可以通过以下方式获取帮助：

- **GitHub Issues**: [提交问题](https://github.com/your-org/InfoCardSystem/issues)
- **邮箱支持**: <EMAIL>
- **在线文档**: https://docs.infocard.com

## 📝 文档贡献

我们欢迎您为文档做出贡献！如果您发现文档中的错误或希望改进文档，请：

1. Fork 项目仓库
2. 在 `docs/` 目录下修改相应文档
3. 提交 Pull Request

详细的贡献指南请参考：[文档贡献指南](contributing/documentation.md)

---

**InfoCard Documentation Team** © 2024
