<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.MyInfoCardsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="我的资讯卡"
             BackgroundColor="{StaticResource Background}">

    <StackLayout VerticalOptions="Center" 
                HorizontalOptions="Center"
                Padding="32">
        
        <Label Text="📋"
               FontSize="64"
               HorizontalOptions="Center" />
        
        <Label Text="我的资讯卡"
               FontSize="24"
               FontAttributes="Bold"
               TextColor="{StaticResource TextPrimary}"
               HorizontalOptions="Center"
               Margin="0,16,0,8" />
        
        <Label Text="管理您创建的所有资讯卡"
               FontSize="16"
               TextColor="{StaticResource TextSecondary}"
               HorizontalOptions="Center" />
        
        <Button Text="即将推出"
               Margin="0,24,0,0"
               IsEnabled="False" />
        
    </StackLayout>

</ContentPage>
