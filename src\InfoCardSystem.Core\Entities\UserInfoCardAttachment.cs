namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户资讯卡附件关联实体
/// </summary>
public class UserInfoCardAttachment : BaseEntity
{
    /// <summary>
    /// 资讯卡ID
    /// </summary>
    public int InfoCardId { get; set; }
    
    /// <summary>
    /// 附件ID
    /// </summary>
    public int AttachmentId { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 资讯卡
    /// </summary>
    public virtual UserInfoCard InfoCard { get; set; } = null!;
    
    /// <summary>
    /// 附件
    /// </summary>
    public virtual UserAttachment Attachment { get; set; } = null!;
}
