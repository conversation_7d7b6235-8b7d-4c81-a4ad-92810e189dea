<?xml version="1.0" encoding="UTF-8" ?>
<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!-- Base Styles -->
    <Style TargetType="Page" ApplyToDerivedTypes="True">
        <Setter Property="Padding" Value="0"/>
        <Setter Property="BackgroundColor" Value="{StaticResource Background}" />
    </Style>

    <Style TargetType="Shell" ApplyToDerivedTypes="True">
        <Setter Property="Shell.BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="Shell.ForegroundColor" Value="{StaticResource OnPrimary}" />
        <Setter Property="Shell.TitleColor" Value="{StaticResource OnPrimary}" />
        <Setter Property="Shell.DisabledColor" Value="{StaticResource Gray400}" />
        <Setter Property="Shell.UnselectedColor" Value="{StaticResource Gray300}" />
        <Setter Property="Shell.TabBarBackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="Shell.TabBarForegroundColor" Value="{StaticResource Primary}"/>
        <Setter Property="Shell.TabBarUnselectedColor" Value="{StaticResource Gray400}"/>
        <Setter Property="Shell.TabBarTitleColor" Value="{StaticResource Primary}"/>
    </Style>

    <!-- Button Styles -->
    <Style TargetType="Button">
        <Setter Property="TextColor" Value="{StaticResource OnPrimary}" />
        <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="BorderWidth" Value="0" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="Padding" Value="14,10" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="FontAttributes" Value="Bold" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{StaticResource Gray400}" />
                            <Setter Property="BackgroundColor" Value="{StaticResource Gray200}" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="Pressed">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource PrimaryDark}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButton" TargetType="Button">
        <Setter Property="TextColor" Value="{StaticResource Primary}" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="BorderColor" Value="{StaticResource Primary}" />
        <Setter Property="BorderWidth" Value="1" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="Padding" Value="14,10" />
        <Setter Property="FontSize" Value="16" />
    </Style>

    <!-- Text Button Style -->
    <Style x:Key="TextButton" TargetType="Button">
        <Setter Property="TextColor" Value="{StaticResource Primary}" />
        <Setter Property="BackgroundColor" Value="{StaticResource Transparent}" />
        <Setter Property="BorderWidth" Value="0" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="8,4" />
    </Style>

    <!-- Entry Styles -->
    <Style TargetType="Entry">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource TextHint}" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="HeightRequest" Value="44" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Focused">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{StaticResource TextDisabled}" />
                            <Setter Property="BackgroundColor" Value="{StaticResource Gray100}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <!-- Label Styles -->
    <Style TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="LineBreakMode" Value="WordWrap" />
    </Style>

    <Style x:Key="Headline1" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="32" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>

    <Style x:Key="Headline2" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="24" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>

    <Style x:Key="Headline3" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="20" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>

    <Style x:Key="Subtitle1" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>

    <Style x:Key="Subtitle2" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextSecondary}" />
        <Setter Property="FontSize" Value="16" />
    </Style>

    <Style x:Key="Body1" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="FontSize" Value="16" />
    </Style>

    <Style x:Key="Body2" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextSecondary}" />
        <Setter Property="FontSize" Value="14" />
    </Style>

    <Style x:Key="Caption" TargetType="Label">
        <Setter Property="TextColor" Value="{StaticResource TextHint}" />
        <Setter Property="FontSize" Value="12" />
    </Style>

    <!-- Frame Styles -->
    <Style TargetType="Frame">
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="BorderColor" Value="{StaticResource Outline}" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="HasShadow" Value="True" />
        <Setter Property="Padding" Value="16" />
    </Style>

    <Style x:Key="CardFrame" TargetType="Frame">
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="BorderColor" Value="{StaticResource Transparent}" />
        <Setter Property="CornerRadius" Value="12" />
        <Setter Property="HasShadow" Value="True" />
        <Setter Property="Padding" Value="16" />
        <Setter Property="Margin" Value="8" />
    </Style>

    <!-- ActivityIndicator Style -->
    <Style TargetType="ActivityIndicator">
        <Setter Property="Color" Value="{StaticResource Primary}" />
    </Style>

    <!-- CheckBox Style -->
    <Style TargetType="CheckBox">
        <Setter Property="Color" Value="{StaticResource Primary}" />
    </Style>

    <!-- Switch Style -->
    <Style TargetType="Switch">
        <Setter Property="OnColor" Value="{StaticResource Primary}" />
        <Setter Property="ThumbColor" Value="{StaticResource Surface}" />
    </Style>

    <!-- Slider Style -->
    <Style TargetType="Slider">
        <Setter Property="MinimumTrackColor" Value="{StaticResource Primary}" />
        <Setter Property="MaximumTrackColor" Value="{StaticResource Gray300}" />
        <Setter Property="ThumbColor" Value="{StaticResource Primary}" />
    </Style>

    <!-- ProgressBar Style -->
    <Style TargetType="ProgressBar">
        <Setter Property="ProgressColor" Value="{StaticResource Primary}" />
    </Style>

    <!-- SearchBar Style -->
    <Style TargetType="SearchBar">
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource TextHint}" />
        <Setter Property="CancelButtonColor" Value="{StaticResource Primary}" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
    </Style>

</ResourceDictionary>
