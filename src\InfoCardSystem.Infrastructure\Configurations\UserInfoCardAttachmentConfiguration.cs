using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户资讯卡附件关联实体配置
/// </summary>
public class UserInfoCardAttachmentConfiguration : IEntityTypeConfiguration<UserInfoCardAttachment>
{
    public void Configure(EntityTypeBuilder<UserInfoCardAttachment> builder)
    {
        // 表名
        builder.ToTable("user_infocard_attachments");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.InfoCardId)
            .IsRequired();
            
        builder.Property(x => x.AttachmentId)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => new { x.InfoCardId, x.AttachmentId })
            .IsUnique()
            .HasDatabaseName("idx_infocard_attachments_infocard_attachment");
            
        builder.HasIndex(x => x.AttachmentId)
            .HasDatabaseName("idx_infocard_attachments_attachment");
        
        // 关系配置已在UserInfoCardConfiguration和UserAttachmentConfiguration中定义
    }
}
