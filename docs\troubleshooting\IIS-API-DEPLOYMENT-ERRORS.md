# InfoCard System - IIS部署错误解决指南

## 🚨 错误描述

**错误信息**: "执行处理程序时出错，数据无效（错误代码：HRESULT 0x80070005）"

**影响范围**: API项目和Web项目都可能遇到此问题

## 🔍 问题分析

### 常见原因
1. **应用程序文件缺失** - 只有web.config，缺少实际的.NET应用程序文件
2. **权限不足** - IIS应用程序池没有足够的文件系统权限
3. **.NET运行时问题** - .NET 8.0 Hosting Bundle未安装或配置错误
4. **web.config配置错误** - 配置文件语法错误或模块缺失

### 错误代码含义
- **HRESULT 0x80070005**: 访问被拒绝，通常是权限问题
- **数据无效**: 配置文件格式错误或引用的文件不存在

## ✅ 解决方案

### 方案1: 自动修复（推荐）

运行自动修复脚本：
```bash
# 1. 重新发布两个项目（推荐）
scripts\republish-both-projects.bat

# 2. 或者单独发布
scripts\republish-api-to-iis.bat
scripts\republish-web-to-iis.bat

# 3. 验证发布状态
scripts\verify-publish.bat

# 4. 修复IIS配置（如需要）
scripts\fix-iis-api-config.bat
```

### 方案2: 手动修复步骤

#### 步骤1: 检查.NET 8.0 Hosting Bundle
1. **下载并安装**: [.NET 8.0 Hosting Bundle](https://dotnet.microsoft.com/download/dotnet/8.0)
2. **验证安装**:
   ```bash
   dotnet --list-runtimes
   # 应该看到: Microsoft.AspNetCore.App 8.0.x
   ```

#### 步骤2: 重新发布API项目
```bash
# 清理项目
dotnet clean src\InfoCardSystem.API\InfoCardSystem.API.csproj -c Release

# 发布项目
dotnet publish src\InfoCardSystem.API\InfoCardSystem.API.csproj -c Release -o "publish\ICAPI"

# 复制到IIS目录
xcopy "publish\ICAPI\*" "C:\Webs\ICAPI\" /E /Y /I
```

#### 步骤3: 设置正确的权限
```bash
# 设置IIS用户权限
icacls "C:\Webs\ICAPI" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\Webs\ICAPI" /grant "IUSR:(OI)(CI)R" /T
icacls "C:\Webs\ICAPI" /grant "IIS AppPool\ICAPI:(OI)(CI)F" /T
```

#### 步骤4: 配置应用程序池
1. **打开IIS管理器**
2. **选择应用程序池 → ICAPI**
3. **设置以下属性**:
   - **.NET CLR版本**: 无托管代码
   - **托管管道模式**: 集成
   - **标识**: ApplicationPoolIdentity
   - **启用32位应用程序**: False

#### 步骤5: 验证web.config
确保web.config包含正确的配置：
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet"
                  arguments=".\InfoCardSystem.API.dll"
                  stdoutLogEnabled="true"
                  stdoutLogFile=".\logs\stdout"
                  hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

### 方案3: 创建简化配置

如果复杂配置有问题，使用简化的web.config：
```bash
scripts\create-simple-webconfig.bat
```

## 🔧 诊断工具

### 1. 检查文件完整性
```bash
# 验证必要文件存在
dir "C:\Webs\ICAPI\InfoCardSystem.API.dll"
dir "C:\Webs\ICAPI\web.config"
```

### 2. 检查IIS日志
```bash
# 查看IIS日志
type "C:\inetpub\logs\LogFiles\W3SVC*\*.log" | findstr "8081"
```

### 3. 检查Windows事件日志
1. **打开事件查看器**
2. **导航到**: Windows日志 → 应用程序
3. **筛选**: 来源为"IIS AspNetCore Module V2"

### 4. 测试API连接
```bash
# 测试健康检查端点
curl http://localhost:8081/api/health

# 测试基本连接
telnet localhost 8081
```

## 📋 预防措施

### 1. 部署检查清单
- [ ] .NET 8.0 Hosting Bundle已安装
- [ ] 应用程序文件完整发布
- [ ] 权限正确设置
- [ ] 应用程序池配置正确
- [ ] web.config语法正确

### 2. 定期维护
- 定期更新.NET运行时
- 监控IIS日志
- 验证权限设置
- 备份工作配置

### 3. 开发环境同步
- 使用相同的.NET版本
- 保持配置文件一致
- 定期测试部署流程

## 🚀 快速解决流程

### 紧急修复（5分钟）
1. **运行**: `scripts\republish-api-to-iis.bat`
2. **等待**: 发布完成
3. **测试**: `curl http://localhost:8081/api/health`

### 完整修复（15分钟）
1. **检查**: .NET 8.0 Hosting Bundle
2. **发布**: 重新发布API项目
3. **配置**: 设置IIS权限和应用程序池
4. **测试**: 验证所有端点正常工作

## 📞 获取帮助

如果问题仍然存在：

1. **收集信息**:
   - IIS错误日志
   - Windows事件日志
   - web.config内容
   - 目录文件列表

2. **检查环境**:
   - Windows版本
   - IIS版本
   - .NET版本
   - 权限设置

3. **联系支持**并提供上述信息

## 🎯 成功指标

修复成功的标志：
- API健康检查返回200状态码
- Swagger UI可以正常访问
- IIS管理器中网站状态为"已启动"
- 无错误日志产生

---

**最后更新**: 2024年1月15日  
**适用版本**: IIS 10+, .NET 8.0  
**支持平台**: Windows Server 2019+, Windows 10+
