using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户二维码实体配置
/// </summary>
public class UserQRCodeConfiguration : IEntityTypeConfiguration<UserQRCode>
{
    public void Configure(EntityTypeBuilder<UserQRCode> builder)
    {
        // 表名
        builder.ToTable("user_qr_codes");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.Id)
            .IsRequired()
            .ValueGeneratedOnAdd();
            
        builder.Property(x => x.UserId)
            .IsRequired();
            
        builder.Property(x => x.QRCodeData)
            .IsRequired()
            .HasMaxLength(500);
            
        builder.Property(x => x.QRCodeUrl)
            .IsRequired(false)
            .HasMaxLength(500);
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.ExpiresAt)
            .IsRequired();
            
        builder.Property(x => x.IsActive)
            .IsRequired()
            .HasDefaultValue(true);
        
        // 索引
        builder.HasIndex(x => x.UserId)
            .HasDatabaseName("idx_user_qr_codes_user_id");
            
        builder.HasIndex(x => x.QRCodeData)
            .IsUnique()
            .HasDatabaseName("idx_user_qr_codes_data");
            
        builder.HasIndex(x => x.ExpiresAt)
            .HasDatabaseName("idx_user_qr_codes_expires_at");
            
        builder.HasIndex(x => new { x.UserId, x.IsActive })
            .HasDatabaseName("idx_user_qr_codes_user_active");
        
        // 关系配置
        builder.HasOne(x => x.User)
            .WithMany()
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
