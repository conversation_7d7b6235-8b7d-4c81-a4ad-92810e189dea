@echo off
echo ========================================
echo InfoCard Quick Verification
echo ========================================
echo.

echo Checking source code files only...
echo.

echo 1. Checking infocard.com references in source files...
findstr /R "infocard\.com" src\InfoCardSystem.API\*.cs src\InfoCardSystem.API\*.json src\InfoCardSystem.Web\*.cs src\InfoCardSystem.Web\*.json src\InfoCardSystem.Mobile\*.cs 2>nul
if %errorLevel% equ 0 (
    echo ERROR: Found infocard.com references in source files
) else (
    echo OK: No infocard.com references in source files
)
echo.

echo 2. Checking API port configuration...
findstr "8081" src\InfoCardSystem.API\appsettings.json 2>nul
if %errorLevel% equ 0 (
    echo OK: API port 8081 configured
) else (
    echo ERROR: API port 8081 not found
)

echo 3. Checking Web API configuration...
findstr "localhost:8081" src\InfoCardSystem.Web\appsettings.json 2>nul
if %errorLevel% equ 0 (
    echo OK: Web API URL configured correctly
) else (
    echo ERROR: Web API URL not configured correctly
)

echo 4. Checking Mobile API configuration...
findstr "localhost:8081" src\InfoCardSystem.Mobile\MauiProgram.cs 2>nul
if %errorLevel% equ 0 (
    echo OK: Mobile API URL configured correctly
) else (
    echo ERROR: Mobile API URL not configured correctly
)
echo.

echo ========================================
echo Quick Verification Complete
echo ========================================
echo.

echo Summary of fixes applied:
echo - Replaced infocard.com with localhost URLs
echo - Updated all port configurations to use 8081/8082
echo - Removed external dependencies
echo - Updated documentation and scripts
echo.

echo Local URLs:
echo - API: http://localhost:8081/health
echo - Web: http://localhost:8082/
echo.

pause
