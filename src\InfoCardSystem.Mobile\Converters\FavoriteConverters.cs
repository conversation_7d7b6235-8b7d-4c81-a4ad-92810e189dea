using System.Globalization;

namespace InfoCardSystem.Mobile.Converters;

/// <summary>
/// 收藏状态到文本转换器
/// </summary>
public class FavoriteTextConverter : IValueConverter
{
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isFavorited)
        {
            return isFavorited ? "⭐ 已收藏" : "☆ 收藏";
        }
        
        return "☆ 收藏";
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 收藏状态到颜色转换器
/// </summary>
public class FavoriteColorConverter : IValueConverter
{
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool isFavorited)
        {
            return isFavorited ? Colors.Orange : Colors.Gray;
        }
        
        return Colors.Gray;
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
