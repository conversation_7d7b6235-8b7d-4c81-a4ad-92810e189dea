namespace InfoCardSystem.Core.DTOs.Common;

/// <summary>
/// API响应基类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 数据
    /// </summary>
    public T? Data { get; set; }
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public ErrorInfo? Error { get; set; }
    
    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse<T> SuccessResult(T data, string message = "操作成功")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message
        };
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误码</param>
    /// <param name="details">错误详情</param>
    /// <returns>失败响应</returns>
    public static ApiResponse<T> ErrorResult(string message, string? errorCode = null, object? details = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Error = new ErrorInfo
            {
                Code = errorCode ?? "UNKNOWN_ERROR",
                Message = message,
                Details = details
            }
        };
    }
}

/// <summary>
/// 无数据的API响应
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse SuccessResult(string message = "操作成功")
    {
        return new ApiResponse
        {
            Success = true,
            Message = message
        };
    }
    
    /// <summary>
    /// 创建失败响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误码</param>
    /// <param name="details">错误详情</param>
    /// <returns>失败响应</returns>
    public static new ApiResponse ErrorResult(string message, string? errorCode = null, object? details = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            Error = new ErrorInfo
            {
                Code = errorCode ?? "UNKNOWN_ERROR",
                Message = message,
                Details = details
            }
        };
    }
}

/// <summary>
/// 错误信息
/// </summary>
public class ErrorInfo
{
    /// <summary>
    /// 错误码
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误详情
    /// </summary>
    public object? Details { get; set; }
}
