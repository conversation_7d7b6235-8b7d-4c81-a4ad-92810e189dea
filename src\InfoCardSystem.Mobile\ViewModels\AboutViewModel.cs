using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 关于页面ViewModel
/// </summary>
public partial class AboutViewModel : BaseViewModel
{
    public AboutViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        ILogger<AboutViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        Title = "关于InfoCard";
        
        // 初始化版本信息
        InitializeVersionInfo();
    }

    [ObservableProperty]
    private string appVersion = "1.0.0";

    [ObservableProperty]
    private string buildInfo = "Build 2025.07.11";

    /// <summary>
    /// 联系邮箱命令
    /// </summary>
    [RelayCommand]
    private async Task ContactEmailAsync()
    {
        try
        {
            _logger.LogDebug("打开邮箱客户端");
            
            var message = new EmailMessage
            {
                Subject = "InfoCard 用户反馈",
                Body = "请在此输入您的反馈内容...",
                To = new List<string> { "<EMAIL>" }
            };

            await Email.Default.ComposeAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开邮箱客户端失败");
            await ShowToastAsync("无法打开邮箱客户端");
        }
    }

    /// <summary>
    /// 访问网站命令
    /// </summary>
    [RelayCommand]
    private async Task VisitWebsiteAsync()
    {
        try
        {
            _logger.LogDebug("打开官方网站");
            
            var uri = new Uri("https://www.infocard.com");
            await Browser.Default.OpenAsync(uri, BrowserLaunchMode.SystemPreferred);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "打开网站失败");
            await ShowToastAsync("无法打开网站");
        }
    }

    /// <summary>
    /// 在线客服命令
    /// </summary>
    [RelayCommand]
    private async Task OnlineServiceAsync()
    {
        await ShowToastAsync("在线客服功能即将推出");
    }

    /// <summary>
    /// 初始化版本信息
    /// </summary>
    private void InitializeVersionInfo()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            
            if (version != null)
            {
                AppVersion = $"v{version.Major}.{version.Minor}.{version.Build}";
            }

            var buildDate = GetBuildDate(assembly);
            BuildInfo = $"Build {buildDate:yyyy.MM.dd}";
            
            _logger.LogInformation("版本信息初始化完成: {Version}, {BuildInfo}", AppVersion, BuildInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化版本信息失败");
        }
    }

    /// <summary>
    /// 获取构建日期
    /// </summary>
    private static DateTime GetBuildDate(Assembly assembly)
    {
        try
        {
            var attribute = assembly.GetCustomAttribute<AssemblyMetadataAttribute>();
            if (attribute != null && DateTime.TryParse(attribute.Value, out var buildDate))
            {
                return buildDate;
            }
        }
        catch
        {
            // 忽略错误，使用默认日期
        }
        
        return DateTime.Now;
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }
}
