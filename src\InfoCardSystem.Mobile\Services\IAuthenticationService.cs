using InfoCardSystem.Mobile.Models;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// 检查是否已认证
    /// </summary>
    Task<bool> IsAuthenticatedAsync();

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);

    /// <summary>
    /// 用户注册
    /// </summary>
    /// <param name="request">注册请求</param>
    Task<ApiResponse<RegisterResponse>> RegisterAsync(RegisterRequest request);

    /// <summary>
    /// 用户登出
    /// </summary>
    Task LogoutAsync();

    /// <summary>
    /// 刷新Token
    /// </summary>
    Task<ApiResponse<TokenResponse>> RefreshTokenAsync();

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    Task<UserInfo?> GetCurrentUserAsync();

    /// <summary>
    /// 获取访问令牌
    /// </summary>
    Task<string?> GetAccessTokenAsync();

    /// <summary>
    /// 忘记密码
    /// </summary>
    /// <param name="request">忘记密码请求</param>
    Task<ApiResponse<bool>> ForgotPasswordAsync(ForgotPasswordRequest request);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="request">重置密码请求</param>
    Task<ApiResponse<bool>> ResetPasswordAsync(ResetPasswordRequest request);
}
