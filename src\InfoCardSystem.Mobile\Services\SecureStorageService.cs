using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 安全存储服务实现
/// </summary>
public class SecureStorageService : ISecureStorageService
{
    private readonly ISecureStorage _secureStorage;
    private readonly ILogger<SecureStorageService> _logger;

    public SecureStorageService(ISecureStorage secureStorage, ILogger<SecureStorageService> logger)
    {
        _secureStorage = secureStorage;
        _logger = logger;
    }

    public async Task<string?> GetAsync(string key)
    {
        try
        {
            _logger.LogDebug("获取安全存储值: {Key}", key);
            return await _secureStorage.GetAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取安全存储值失败: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync(string key, string value)
    {
        try
        {
            _logger.LogDebug("设置安全存储值: {Key}", key);
            await _secureStorage.SetAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置安全存储值失败: {Key}", key);
            throw;
        }
    }

    public async Task<bool> RemoveAsync(string key)
    {
        try
        {
            _logger.LogDebug("移除安全存储值: {Key}", key);
            _secureStorage.Remove(key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除安全存储值失败: {Key}", key);
            return false;
        }
    }

    public async Task<bool> ContainsKeyAsync(string key)
    {
        try
        {
            var value = await _secureStorage.GetAsync(key);
            return !string.IsNullOrEmpty(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查安全存储键失败: {Key}", key);
            return false;
        }
    }

    public async Task RemoveAllAsync()
    {
        try
        {
            _logger.LogDebug("清除所有安全存储值");
            _secureStorage.RemoveAll();
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除所有安全存储值失败");
            throw;
        }
    }
}
