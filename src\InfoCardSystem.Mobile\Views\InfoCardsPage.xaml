<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.InfoCardsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             xmlns:models="clr-namespace:InfoCardSystem.Mobile.Models"
             x:DataType="viewmodels:InfoCardsViewModel"
             Title="首页"
             BackgroundColor="{StaticResource Background}">

    <Grid RowDefinitions="Auto,*">
        
        <!-- 顶部导航栏 -->
        <Grid Grid.Row="0" 
              BackgroundColor="{StaticResource Primary}"
              Padding="16,8"
              ColumnDefinitions="*,Auto,Auto">
            
            <!-- 标题 -->
            <Label Grid.Column="0"
                   Text="InfoCard"
                   FontSize="20"
                   FontAttributes="Bold"
                   TextColor="White"
                   VerticalOptions="Center" />
            
            <!-- 搜索按钮 -->
            <Button Grid.Column="1"
                   Text="🔍"
                   BackgroundColor="Transparent"
                   TextColor="White"
                   FontSize="18"
                   WidthRequest="44"
                   HeightRequest="44"
                   CornerRadius="22"
                   Command="{Binding SearchCommand}" />
            
            <!-- 添加按钮 -->
            <Button Grid.Column="2"
                   Text="+"
                   BackgroundColor="White"
                   TextColor="{StaticResource Primary}"
                   FontSize="24"
                   FontAttributes="Bold"
                   WidthRequest="44"
                   HeightRequest="44"
                   CornerRadius="22"
                   Command="{Binding AddFriendCommand}" />
            
        </Grid>

        <!-- 主内容区域 -->
        <RefreshView Grid.Row="1"
                    IsRefreshing="{Binding IsRefreshing}"
                    Command="{Binding RefreshCommand}">
            
            <CollectionView ItemsSource="{Binding InfoCards}"
                           BackgroundColor="Transparent"
                           SelectionMode="None">
                
                <CollectionView.ItemsLayout>
                    <LinearItemsLayout Orientation="Vertical" ItemSpacing="8" />
                </CollectionView.ItemsLayout>
                
                <CollectionView.Header>
                    <StackLayout Padding="16,16,16,8">
                        
                        <!-- 欢迎消息 -->
                        <Frame Style="{StaticResource CardFrame}"
                               BackgroundColor="{StaticResource PrimaryLight}"
                               Margin="0,0,0,8">
                            <StackLayout>
                                <Label Text="{Binding WelcomeMessage}"
                                       FontSize="16"
                                       FontAttributes="Bold"
                                       TextColor="White" />
                                <Label Text="分享您的精彩时刻"
                                       FontSize="14"
                                       TextColor="White"
                                       Opacity="0.9"
                                       Margin="0,4,0,0" />
                            </StackLayout>
                        </Frame>
                        
                        <!-- 快速操作 -->
                        <Grid ColumnDefinitions="*,*,*" 
                              ColumnSpacing="8"
                              Margin="0,0,0,16">
                            
                            <Button Grid.Column="0"
                                   Text="📝 发布"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding CreateInfoCardCommand}" />
                            
                            <Button Grid.Column="1"
                                   Text="👥 好友"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding ViewFriendsCommand}" />
                            
                            <Button Grid.Column="2"
                                   Text="⭐ 收藏"
                                   Style="{StaticResource SecondaryButton}"
                                   FontSize="14"
                                   HeightRequest="40"
                                   Command="{Binding ViewFavoritesCommand}" />
                            
                        </Grid>
                        
                    </StackLayout>
                </CollectionView.Header>
                
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="viewmodels:InfoCardViewModel">
                        <Grid Padding="16,0,16,8">
                            
                            <!-- 资讯卡片 -->
                            <Frame Style="{StaticResource CardFrame}"
                                   Margin="0">
                                <StackLayout Spacing="12">
                                    
                                    <!-- 发布者信息 -->
                                    <Grid ColumnDefinitions="Auto,*,Auto">
                                        
                                        <!-- 头像 -->
                                        <Frame Grid.Column="0"
                                               WidthRequest="40"
                                               HeightRequest="40"
                                               CornerRadius="20"
                                               Padding="0"
                                               BackgroundColor="{StaticResource Gray200}">
                                            <Image Source="{Binding Publisher.AvatarUrl, FallbackValue='default_avatar.png'}"
                                                   Aspect="AspectFill" />
                                        </Frame>
                                        
                                        <!-- 用户信息 -->
                                        <StackLayout Grid.Column="1"
                                                    Margin="12,0,0,0"
                                                    VerticalOptions="Center">
                                            <Label Text="{Binding Publisher.DisplayName, FallbackValue={Binding Publisher.Username}}"
                                                   FontSize="16"
                                                   FontAttributes="Bold"
                                                   TextColor="{StaticResource TextPrimary}" />
                                            <Label Text="{Binding CreatedAt, StringFormat='{0:MM-dd HH:mm}'}"
                                                   FontSize="12"
                                                   TextColor="{StaticResource TextSecondary}" />
                                        </StackLayout>
                                        
                                        <!-- 更多按钮 -->
                                        <Button Grid.Column="2"
                                               Text="⋯"
                                               BackgroundColor="Transparent"
                                               TextColor="{StaticResource TextSecondary}"
                                               FontSize="18"
                                               WidthRequest="32"
                                               HeightRequest="32" />
                                        
                                    </Grid>
                                    
                                    <!-- 资讯卡内容 -->
                                    <StackLayout>
                                        
                                        <!-- 标题 -->
                                        <Label Text="{Binding Title}"
                                               FontSize="18"
                                               FontAttributes="Bold"
                                               TextColor="{StaticResource TextPrimary}"
                                               IsVisible="{Binding Title, Converter={StaticResource StringToBoolConverter}}" />
                                        
                                        <!-- 内容 -->
                                        <Label Text="{Binding Content}"
                                               FontSize="16"
                                               TextColor="{StaticResource TextPrimary}"
                                               LineBreakMode="WordWrap"
                                               MaxLines="5" />
                                        
                                        <!-- 附件 (如果有) -->
                                        <CollectionView ItemsSource="{Binding Attachments}"
                                                       IsVisible="{Binding Attachments.Count, Converter={StaticResource CountToBoolConverter}}"
                                                       HeightRequest="120"
                                                       Margin="0,8,0,0">
                                            <CollectionView.ItemsLayout>
                                                <LinearItemsLayout Orientation="Horizontal" ItemSpacing="8" />
                                            </CollectionView.ItemsLayout>
                                            <CollectionView.ItemTemplate>
                                                <DataTemplate x:DataType="models:AttachmentViewModel">
                                                    <Frame WidthRequest="100"
                                                           HeightRequest="100"
                                                           CornerRadius="8"
                                                           Padding="0"
                                                           BackgroundColor="{StaticResource Gray100}">
                                                        <Image Source="{Binding PreviewUrl}"
                                                               Aspect="AspectFill" />
                                                    </Frame>
                                                </DataTemplate>
                                            </CollectionView.ItemTemplate>
                                        </CollectionView>
                                        
                                    </StackLayout>
                                    
                                    <!-- 操作按钮 -->
                                    <Grid ColumnDefinitions="*,*,*" 
                                          ColumnSpacing="16"
                                          Margin="0,8,0,0">
                                        
                                        <Button Grid.Column="0"
                                               Text="💬 评论"
                                               BackgroundColor="Transparent"
                                               TextColor="{StaticResource TextSecondary}"
                                               FontSize="14"
                                               HeightRequest="36" />
                                        
                                        <Button Grid.Column="1"
                                               Text="🔄 转发"
                                               BackgroundColor="Transparent"
                                               TextColor="{StaticResource TextSecondary}"
                                               FontSize="14"
                                               HeightRequest="36" />
                                        
                                        <Button Grid.Column="2"
                                               Text="{Binding IsFavorited, Converter={StaticResource FavoriteTextConverter}}"
                                               BackgroundColor="Transparent"
                                               TextColor="{Binding IsFavorited, Converter={StaticResource FavoriteColorConverter}}"
                                               FontSize="14"
                                               HeightRequest="36" />
                                        
                                    </Grid>
                                    
                                </StackLayout>
                            </Frame>
                            
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
                
                <CollectionView.EmptyView>
                    <StackLayout VerticalOptions="Center"
                                HorizontalOptions="Center"
                                Padding="32">
                        <Label Text="📝"
                               FontSize="48"
                               HorizontalOptions="Center" />
                        <Label Text="暂无资讯卡"
                               FontSize="18"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextSecondary}"
                               HorizontalOptions="Center"
                               Margin="0,16,0,8" />
                        <Label Text="开始创建您的第一张资讯卡吧"
                               FontSize="14"
                               TextColor="{StaticResource TextHint}"
                               HorizontalOptions="Center" />
                        <Button Text="立即创建"
                               Command="{Binding CreateInfoCardCommand}"
                               Margin="0,16,0,0" />
                    </StackLayout>
                </CollectionView.EmptyView>
                
            </CollectionView>
            
        </RefreshView>
        
        <!-- 加载指示器 -->
        <ActivityIndicator Grid.Row="1"
                          IsRunning="{Binding IsBusy}"
                          IsVisible="{Binding IsBusy}"
                          VerticalOptions="Center"
                          HorizontalOptions="Center" />
        
    </Grid>

</ContentPage>
