# InfoCard API 文档

InfoCard系统的RESTful API接口文档，提供完整的后端服务接口说明。

## 📋 API概述

### 基础信息
- **API版本**: v1.0
- **基础URL**: `http://localhost:8081/api/v1`
- **认证方式**: JWT <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

### 响应格式
所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "uuid-string"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "errorCode": "ERROR_CODE",
  "errors": [
    {
      "field": "字段名",
      "message": "字段错误信息"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "uuid-string"
}
```

## 🔐 认证授权

### JWT Token获取
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh-token-string",
    "expiresIn": 3600,
    "user": {
      "id": 1,
      "username": "用户名",
      "email": "<EMAIL>"
    }
  }
}
```

### Token使用
在请求头中添加Authorization字段：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 👤 用户管理 API

### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "用户名",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "phone": "13800138000"
}
```

### 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer {token}
```

### 更新用户信息
```http
PUT /api/v1/users/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "username": "新用户名",
  "signature": "个人签名",
  "avatar": "头像URL"
}
```

### 修改密码
```http
POST /api/v1/users/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "currentPassword": "当前密码",
  "newPassword": "新密码",
  "confirmPassword": "确认新密码"
}
```

## 📝 资讯卡管理 API

### 获取资讯卡列表
```http
GET /api/v1/infocards?page=1&pageSize=20&filter=all&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {token}
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20, 最大: 100)
- `filter`: 筛选条件 (all, friends, following, hot)
- `sortBy`: 排序字段 (createdAt, likeCount, commentCount)
- `sortOrder`: 排序方向 (asc, desc)

### 获取资讯卡详情
```http
GET /api/v1/infocards/{id}
Authorization: Bearer {token}
```

### 创建资讯卡
```http
POST /api/v1/infocards
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "资讯卡标题",
  "content": "资讯卡内容",
  "tags": ["标签1", "标签2"],
  "visibility": "public",
  "attachments": [
    {
      "type": "image",
      "url": "图片URL",
      "fileName": "文件名"
    }
  ],
  "location": "位置信息",
  "expireAt": "2024-12-31T23:59:59Z"
}
```

### 更新资讯卡
```http
PUT /api/v1/infocards/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": "更新后的内容",
  "tags": ["新标签"],
  "visibility": "friends"
}
```

### 删除资讯卡
```http
DELETE /api/v1/infocards/{id}
Authorization: Bearer {token}
```

### 点赞/取消点赞
```http
POST /api/v1/infocards/{id}/like
Authorization: Bearer {token}
```

### 收藏/取消收藏
```http
POST /api/v1/infocards/{id}/favorite
Authorization: Bearer {token}
```

### 转发资讯卡
```http
POST /api/v1/infocards/{id}/forward
Authorization: Bearer {token}
Content-Type: application/json

{
  "message": "转发说明",
  "visibility": "public"
}
```

## 👥 好友管理 API

### 获取好友列表
```http
GET /api/v1/friends?page=1&pageSize=20&search=关键词
Authorization: Bearer {token}
```

### 搜索用户
```http
GET /api/v1/friends/search?keyword=搜索关键词&type=username
Authorization: Bearer {token}
```

### 发送好友请求
```http
POST /api/v1/friends/request
Authorization: Bearer {token}
Content-Type: application/json

{
  "targetUserId": 123,
  "message": "好友请求消息"
}
```

### 获取好友请求列表
```http
GET /api/v1/friends/requests?type=received&page=1&pageSize=20
Authorization: Bearer {token}
```

### 处理好友请求
```http
POST /api/v1/friends/requests/{id}/handle
Authorization: Bearer {token}
Content-Type: application/json

{
  "action": "accept",  // accept 或 reject
  "message": "处理说明"
}
```

### 删除好友
```http
DELETE /api/v1/friends/{friendId}
Authorization: Bearer {token}
```

## 💬 评论管理 API

### 获取评论列表
```http
GET /api/v1/infocards/{id}/comments?page=1&pageSize=20&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {token}
```

### 添加评论
```http
POST /api/v1/infocards/{id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "评论内容",
  "parentId": null,  // 回复评论时填写父评论ID
  "attachments": [
    {
      "type": "image",
      "url": "图片URL"
    }
  ]
}
```

### 删除评论
```http
DELETE /api/v1/comments/{id}
Authorization: Bearer {token}
```

### 点赞评论
```http
POST /api/v1/comments/{id}/like
Authorization: Bearer {token}
```

## 🔍 搜索 API

### 全局搜索
```http
GET /api/v1/search?keyword=搜索关键词&type=all&page=1&pageSize=20
Authorization: Bearer {token}
```

**查询参数:**
- `keyword`: 搜索关键词
- `type`: 搜索类型 (all, infocards, users, tags)
- `page`: 页码
- `pageSize`: 每页数量

### 搜索建议
```http
GET /api/v1/search/suggestions?keyword=关键词
Authorization: Bearer {token}
```

### 热门搜索
```http
GET /api/v1/search/hot
Authorization: Bearer {token}
```

## 📁 文件上传 API

### 上传文件
```http
POST /api/v1/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [文件数据]
type: image  // image, document, video, audio
```

**响应:**
```json
{
  "success": true,
  "data": {
    "id": "file-id",
    "url": "https://api.infocard.com/uploads/file.jpg",
    "fileName": "原始文件名.jpg",
    "size": 1024000,
    "type": "image",
    "mimeType": "image/jpeg"
  }
}
```

### 删除文件
```http
DELETE /api/v1/upload/{fileId}
Authorization: Bearer {token}
```

## 📊 统计 API

### 获取用户统计
```http
GET /api/v1/users/stats
Authorization: Bearer {token}
```

### 获取系统统计
```http
GET /api/v1/system/stats
Authorization: Bearer {token}
```

## 🔔 通知 API

### 获取通知列表
```http
GET /api/v1/notifications?page=1&pageSize=20&type=all&unreadOnly=false
Authorization: Bearer {token}
```

### 标记通知已读
```http
POST /api/v1/notifications/{id}/read
Authorization: Bearer {token}
```

### 标记所有通知已读
```http
POST /api/v1/notifications/read-all
Authorization: Bearer {token}
```

## ❌ 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| AUTH_001 | 401 | 未授权访问 |
| AUTH_002 | 401 | Token已过期 |
| AUTH_003 | 401 | Token无效 |
| USER_001 | 400 | 用户名已存在 |
| USER_002 | 400 | 邮箱已存在 |
| USER_003 | 404 | 用户不存在 |
| CARD_001 | 404 | 资讯卡不存在 |
| CARD_002 | 403 | 无权限访问资讯卡 |
| FILE_001 | 400 | 文件格式不支持 |
| FILE_002 | 400 | 文件大小超限 |
| RATE_001 | 429 | 请求频率超限 |
| SYS_001 | 500 | 系统内部错误 |

## 📝 请求限制

### 频率限制
- **普通用户**: 每分钟100次请求
- **VIP用户**: 每分钟500次请求
- **上传接口**: 每分钟10次请求

### 文件限制
- **图片文件**: 最大10MB，支持jpg、png、gif、webp
- **文档文件**: 最大50MB，支持pdf、doc、docx、xls、xlsx
- **视频文件**: 最大100MB，支持mp4、avi、mov
- **音频文件**: 最大20MB，支持mp3、wav、aac

## 🔧 开发工具

### Swagger文档
访问 `https://api.infocard.com/swagger` 查看交互式API文档

### Postman集合
下载 [InfoCard API Postman Collection](../assets/InfoCard-API.postman_collection.json)

### SDK支持
- JavaScript SDK: 本地开发环境
- Python SDK: 本地开发环境
- PHP SDK: 本地开发环境

## 📞 技术支持

如有API使用问题，请联系：
- **技术支持**: 本地开发环境
- **本地文档**: http://localhost:8081/swagger
- **API状态页**: http://localhost:8081/health
