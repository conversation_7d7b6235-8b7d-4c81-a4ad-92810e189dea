#!/bin/bash

# InfoCard系统部署脚本
# 用于将构建好的应用部署到目标环境
# 
# 使用方法:
#   ./scripts/deploy.sh [环境] [选项]
#
# 环境:
#   dev         开发环境
#   test        测试环境  
#   prod        生产环境
#
# 选项:
#   --backup    部署前备份
#   --rollback  回滚到上一版本
#   --check     仅检查部署状态

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 环境配置
setup_environment() {
    case $DEPLOY_ENV in
        "dev")
            API_PATH="/var/www/infocard-dev/api"
            WEB_PATH="/var/www/infocard-dev/web"
            API_PORT="5001"
            WEB_PORT="5000"
            DB_NAME="InfoCardDB_Dev"
            ;;
        "test")
            API_PATH="/var/www/infocard-test/api"
            WEB_PATH="/var/www/infocard-test/web"
            API_PORT="6001"
            WEB_PORT="6000"
            DB_NAME="InfoCardDB_Test"
            ;;
        "prod")
            API_PATH="/var/www/infocard/api"
            WEB_PATH="/var/www/infocard/web"
            API_PORT="8081"
            WEB_PORT="8082"
            DB_NAME="InfoCardDB"
            ;;
        *)
            log_error "未知环境: $DEPLOY_ENV"
            exit 1
            ;;
    esac
    
    log_info "部署环境: $DEPLOY_ENV"
    log_info "API路径: $API_PATH"
    log_info "Web路径: $WEB_PATH"
}

# 检查部署前提条件
check_prerequisites() {
    log_info "检查部署前提条件..."
    
    # 检查是否有构建输出
    if [ ! -d "publish" ]; then
        log_error "未找到构建输出，请先运行 ./scripts/build.sh --release"
        exit 1
    fi
    
    # 检查目标目录权限
    if [ ! -w "$(dirname $API_PATH)" ]; then
        log_error "没有写入权限: $(dirname $API_PATH)"
        exit 1
    fi
    
    # 检查服务状态
    if systemctl is-active --quiet infocard-api-$DEPLOY_ENV; then
        log_info "API服务正在运行"
    else
        log_warning "API服务未运行"
    fi
    
    log_success "前提条件检查完成"
}

# 备份当前版本
backup_current() {
    if [ "$BACKUP" = true ]; then
        log_info "备份当前版本..."
        
        BACKUP_DIR="/var/backups/infocard-$DEPLOY_ENV/$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        if [ -d "$API_PATH" ]; then
            cp -r "$API_PATH" "$BACKUP_DIR/api"
        fi
        
        if [ -d "$WEB_PATH" ]; then
            cp -r "$WEB_PATH" "$BACKUP_DIR/web"
        fi
        
        # 备份数据库
        mysqldump -u root -p$DB_PASSWORD $DB_NAME > "$BACKUP_DIR/database.sql"
        
        log_success "备份完成: $BACKUP_DIR"
        echo "$BACKUP_DIR" > /tmp/infocard-last-backup-$DEPLOY_ENV
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止API服务
    if systemctl is-active --quiet infocard-api-$DEPLOY_ENV; then
        sudo systemctl stop infocard-api-$DEPLOY_ENV
        log_info "API服务已停止"
    fi
    
    # 停止Web服务
    if systemctl is-active --quiet infocard-web-$DEPLOY_ENV; then
        sudo systemctl stop infocard-web-$DEPLOY_ENV
        log_info "Web服务已停止"
    fi
    
    # 等待服务完全停止
    sleep 3
}

# 部署API
deploy_api() {
    log_info "部署API..."
    
    # 创建目标目录
    mkdir -p "$API_PATH"
    
    # 复制文件
    cp -r publish/api/* "$API_PATH/"
    
    # 设置权限
    chown -R www-data:www-data "$API_PATH"
    chmod -R 755 "$API_PATH"
    
    # 创建上传目录
    mkdir -p "$API_PATH/wwwroot/uploads"
    chmod 777 "$API_PATH/wwwroot/uploads"
    
    # 更新配置文件
    update_api_config
    
    log_success "API部署完成"
}

# 部署Web应用
deploy_web() {
    log_info "部署Web应用..."
    
    # 创建目标目录
    mkdir -p "$WEB_PATH"
    
    # 复制文件
    cp -r publish/web/* "$WEB_PATH/"
    
    # 设置权限
    chown -R www-data:www-data "$WEB_PATH"
    chmod -R 755 "$WEB_PATH"
    
    # 更新配置文件
    update_web_config
    
    log_success "Web应用部署完成"
}

# 更新API配置
update_api_config() {
    log_info "更新API配置..."
    
    CONFIG_FILE="$API_PATH/appsettings.Production.json"
    
    cat > "$CONFIG_FILE" << EOF
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=$DB_NAME;Uid=infocard;Pwd=$DB_PASSWORD;CharSet=utf8mb4;"
  },
  "JwtSettings": {
    "SecretKey": "$JWT_SECRET",
    "Issuer": "InfoCardSystem",
    "Audience": "InfoCardUsers",
    "ExpirationMinutes": 60
  },
  "AllowedHosts": "*",
  "FileUpload": {
    "MaxFileSize": 52428800,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"],
    "UploadPath": "wwwroot/uploads"
  },
  "Urls": "http://localhost:$API_PORT"
}
EOF

    log_success "API配置更新完成"
}

# 更新Web配置
update_web_config() {
    log_info "更新Web配置..."
    
    CONFIG_FILE="$WEB_PATH/appsettings.Production.json"
    
    cat > "$CONFIG_FILE" << EOF
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ApiSettings": {
    "BaseUrl": "http://localhost:$API_PORT"
  },
  "AllowedHosts": "*",
  "Urls": "http://localhost:$WEB_PORT"
}
EOF

    log_success "Web配置更新完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    cd "$API_PATH"
    
    # 设置环境变量
    export ASPNETCORE_ENVIRONMENT=Production
    export ConnectionStrings__DefaultConnection="Server=localhost;Database=$DB_NAME;Uid=infocard;Pwd=$DB_PASSWORD;CharSet=utf8mb4;"
    
    # 运行迁移
    dotnet InfoCardSystem.API.dll --migrate
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动API服务
    sudo systemctl start infocard-api-$DEPLOY_ENV
    sudo systemctl enable infocard-api-$DEPLOY_ENV
    
    # 启动Web服务
    sudo systemctl start infocard-web-$DEPLOY_ENV
    sudo systemctl enable infocard-web-$DEPLOY_ENV
    
    # 等待服务启动
    sleep 5
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查API健康状态
    API_URL="http://localhost:$API_PORT/health"
    if curl -f "$API_URL" > /dev/null 2>&1; then
        log_success "API健康检查通过"
    else
        log_error "API健康检查失败"
        return 1
    fi
    
    # 检查Web应用
    WEB_URL="http://localhost:$WEB_PORT"
    if curl -f "$WEB_URL" > /dev/null 2>&1; then
        log_success "Web应用健康检查通过"
    else
        log_error "Web应用健康检查失败"
        return 1
    fi
    
    log_success "所有健康检查通过"
}

# 回滚到上一版本
rollback() {
    log_info "回滚到上一版本..."
    
    LAST_BACKUP=$(cat /tmp/infocard-last-backup-$DEPLOY_ENV 2>/dev/null || echo "")
    
    if [ -z "$LAST_BACKUP" ] || [ ! -d "$LAST_BACKUP" ]; then
        log_error "未找到备份，无法回滚"
        exit 1
    fi
    
    # 停止服务
    stop_services
    
    # 恢复文件
    if [ -d "$LAST_BACKUP/api" ]; then
        rm -rf "$API_PATH"
        cp -r "$LAST_BACKUP/api" "$API_PATH"
    fi
    
    if [ -d "$LAST_BACKUP/web" ]; then
        rm -rf "$WEB_PATH"
        cp -r "$LAST_BACKUP/web" "$WEB_PATH"
    fi
    
    # 恢复数据库
    if [ -f "$LAST_BACKUP/database.sql" ]; then
        mysql -u root -p$DB_PASSWORD $DB_NAME < "$LAST_BACKUP/database.sql"
    fi
    
    # 启动服务
    start_services
    
    log_success "回滚完成"
}

# 检查部署状态
check_status() {
    log_info "检查部署状态..."
    
    echo "服务状态:"
    systemctl status infocard-api-$DEPLOY_ENV --no-pager -l
    systemctl status infocard-web-$DEPLOY_ENV --no-pager -l
    
    echo ""
    echo "端口监听:"
    netstat -tlnp | grep ":$API_PORT\|:$WEB_PORT"
    
    echo ""
    echo "磁盘使用:"
    df -h "$API_PATH" "$WEB_PATH"
    
    echo ""
    echo "最近日志:"
    tail -n 20 /var/log/infocard-$DEPLOY_ENV.log 2>/dev/null || echo "无日志文件"
}

# 主函数
main() {
    log_info "开始部署InfoCard系统..."
    
    # 默认参数
    DEPLOY_ENV=""
    BACKUP=false
    ROLLBACK=false
    CHECK_ONLY=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|test|prod)
                DEPLOY_ENV="$1"
                shift
                ;;
            --backup)
                BACKUP=true
                shift
                ;;
            --rollback)
                ROLLBACK=true
                shift
                ;;
            --check)
                CHECK_ONLY=true
                shift
                ;;
            -h|--help)
                echo "InfoCard系统部署脚本"
                echo ""
                echo "使用方法: $0 [环境] [选项]"
                echo ""
                echo "环境:"
                echo "  dev         开发环境"
                echo "  test        测试环境"
                echo "  prod        生产环境"
                echo ""
                echo "选项:"
                echo "  --backup    部署前备份"
                echo "  --rollback  回滚到上一版本"
                echo "  --check     仅检查部署状态"
                echo "  -h, --help  显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$DEPLOY_ENV" ]; then
        log_error "请指定部署环境 (dev/test/prod)"
        exit 1
    fi
    
    # 设置环境
    setup_environment
    
    # 加载环境变量
    if [ -f ".env.$DEPLOY_ENV" ]; then
        source ".env.$DEPLOY_ENV"
    fi
    
    # 执行操作
    if [ "$CHECK_ONLY" = true ]; then
        check_status
        exit 0
    fi
    
    if [ "$ROLLBACK" = true ]; then
        rollback
        exit 0
    fi
    
    # 正常部署流程
    check_prerequisites
    backup_current
    stop_services
    deploy_api
    deploy_web
    run_migrations
    start_services
    
    # 健康检查
    if health_check; then
        log_success "InfoCard系统部署成功！"
        log_info "API地址: http://localhost:$API_PORT"
        log_info "Web地址: http://localhost:$WEB_PORT"
    else
        log_error "部署后健康检查失败，请检查日志"
        exit 1
    fi
}

# 执行主函数
main "$@"
