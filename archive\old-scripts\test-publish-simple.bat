@echo off
echo Testing VS2022 Publish Profiles...
echo.

echo Step 1: Build solution
dotnet build InfoCardSystem.VS2022.sln -c Release
if %errorLevel% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo Step 2: Publish API
dotnet publish src/InfoCardSystem.API/InfoCardSystem.API.csproj -p:PublishProfile=IIS-ICAPI
if %errorLevel% neq 0 (
    echo API publish failed
    pause
    exit /b 1
)

echo.
echo Step 3: Publish Web
dotnet publish src/InfoCardSystem.Web/InfoCardSystem.Web.csproj -p:PublishProfile=IIS-ICWeb
if %errorLevel% neq 0 (
    echo Web publish failed
    pause
    exit /b 1
)

echo.
echo Step 4: Verify files
if exist "C:\Webs\ICAPI\InfoCardSystem.API.exe" (
    echo API exe found
) else (
    echo API exe NOT found
)

if exist "C:\Webs\ICWeb\InfoCardSystem.Web.exe" (
    echo Web exe found
) else (
    echo Web exe NOT found
)

echo.
echo VS2022 publish profiles are working correctly!
echo.
echo To use in Visual Studio 2022:
echo 1. Right-click project
echo 2. Select Publish
echo 3. Choose profile (IIS-ICAPI or IIS-ICWeb)
echo 4. Click Publish
echo.
pause
