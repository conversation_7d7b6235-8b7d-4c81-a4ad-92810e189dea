namespace InfoCardSystem.Shared.Constants;

/// <summary>
/// API相关常量
/// </summary>
public static class ApiConstants
{
    /// <summary>
    /// API版本
    /// </summary>
    public const string ApiVersion = "v1";
    
    /// <summary>
    /// API基础路径
    /// </summary>
    public const string ApiBasePath = "/api/v1";
    
    /// <summary>
    /// JWT配置
    /// </summary>
    public static class Jwt
    {
        public const string Issuer = "InfoCardSystem";
        public const string Audience = "InfoCardSystem.Client";
        public const int ExpirationMinutes = 60 * 24; // 24小时
        public const int RefreshTokenExpirationDays = 7; // 7天
    }
    
    /// <summary>
    /// 分页配置
    /// </summary>
    public static class Pagination
    {
        public const int DefaultPageSize = 20;
        public const int MaxPageSize = 100;
        public const int MinPageSize = 1;
    }
    
    /// <summary>
    /// 文件上传配置
    /// </summary>
    public static class FileUpload
    {
        public const long MaxImageSize = 10 * 1024 * 1024; // 10MB
        public const long MaxDocumentSize = 20 * 1024 * 1024; // 20MB
        public const long MaxVideoSize = 100 * 1024 * 1024; // 100MB
        
        public static readonly string[] AllowedImageTypes = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
        public static readonly string[] AllowedDocumentTypes = { ".pdf", ".doc", ".docx", ".txt" };
        public static readonly string[] AllowedVideoTypes = { ".mp4", ".avi", ".mov", ".wmv" };
    }
    
    /// <summary>
    /// 限流配置
    /// </summary>
    public static class RateLimit
    {
        public const int RequestsPerMinute = 100;
        public const int LoginAttemptsPerHour = 5;
        public const int RegistrationAttemptsPerHour = 3;
    }
}
