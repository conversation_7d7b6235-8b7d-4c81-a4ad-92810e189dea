using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.Auth;

/// <summary>
/// 用户注册请求
/// </summary>
public class RegisterRequest
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "用户名长度必须在2-100个字符之间")]
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [Required(ErrorMessage = "邮箱不能为空")]
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    [StringLength(255, ErrorMessage = "邮箱长度不能超过255个字符")]
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string Password { get; set; } = string.Empty;
    
    /// <summary>
    /// 确认密码
    /// </summary>
    [Required(ErrorMessage = "确认密码不能为空")]
    [Compare("Password", ErrorMessage = "两次输入的密码不一致")]
    public string ConfirmPassword { get; set; } = string.Empty;
    
    /// <summary>
    /// 手机号（可选）
    /// </summary>
    [Phone(ErrorMessage = "手机号格式不正确")]
    [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
    public string? Phone { get; set; }
    
    /// <summary>
    /// 自定义用户ID（可选）
    /// </summary>
    [StringLength(50, MinimumLength = 3, ErrorMessage = "自定义用户ID长度必须在3-50个字符之间")]
    [RegularExpression(@"^[a-zA-Z0-9_-]+$", ErrorMessage = "自定义用户ID只能包含字母、数字、下划线和连字符")]
    public string? CustomUserId { get; set; }
    
    /// <summary>
    /// 个人简介（可选）
    /// </summary>
    [StringLength(1000, ErrorMessage = "个人简介长度不能超过1000个字符")]
    public string? Bio { get; set; }
}
