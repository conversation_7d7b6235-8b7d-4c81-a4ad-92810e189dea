using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 对话框服务实现
/// </summary>
public class DialogService : IDialogService
{
    private readonly ILogger<DialogService> _logger;
    private Page? _loadingPage;

    public DialogService(ILogger<DialogService> logger)
    {
        _logger = logger;
    }

    public async Task ShowErrorAsync(string title, string message)
    {
        try
        {
            _logger.LogDebug("显示错误对话框: {Title} - {Message}", title, message);
            
            if (Application.Current?.MainPage != null)
            {
                await Application.Current.MainPage.DisplayAlert(title, message, "确定");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示错误对话框失败");
        }
    }

    public async Task ShowSuccessAsync(string message)
    {
        try
        {
            _logger.LogDebug("显示成功消息: {Message}", message);
            
            if (Application.Current?.MainPage != null)
            {
                await Application.Current.MainPage.DisplayAlert("成功", message, "确定");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示成功消息失败");
        }
    }

    public async Task<bool> ShowConfirmAsync(string title, string message)
    {
        try
        {
            _logger.LogDebug("显示确认对话框: {Title} - {Message}", title, message);
            
            if (Application.Current?.MainPage != null)
            {
                return await Application.Current.MainPage.DisplayAlert(title, message, "确定", "取消");
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示确认对话框失败");
            return false;
        }
    }

    public async Task<string?> ShowPromptAsync(string title, string message, string placeholder = "")
    {
        try
        {
            _logger.LogDebug("显示输入对话框: {Title} - {Message}", title, message);
            
            if (Application.Current?.MainPage != null)
            {
                return await Application.Current.MainPage.DisplayPromptAsync(title, message, placeholder: placeholder);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示输入对话框失败");
            return null;
        }
    }

    public async Task ShowLoadingAsync(string message)
    {
        try
        {
            _logger.LogDebug("显示加载对话框: {Message}", message);
            
            if (Application.Current?.MainPage != null)
            {
                // 创建简单的加载页面
                _loadingPage = new ContentPage
                {
                    BackgroundColor = Colors.Black.WithAlpha(0.5f),
                    Content = new StackLayout
                    {
                        VerticalOptions = LayoutOptions.Center,
                        HorizontalOptions = LayoutOptions.Center,
                        Children =
                        {
                            new ActivityIndicator
                            {
                                IsRunning = true,
                                Color = Colors.White,
                                WidthRequest = 50,
                                HeightRequest = 50
                            },
                            new Label
                            {
                                Text = message,
                                TextColor = Colors.White,
                                HorizontalOptions = LayoutOptions.Center,
                                Margin = new Thickness(0, 10, 0, 0)
                            }
                        }
                    }
                };
                
                await Application.Current.MainPage.Navigation.PushModalAsync(_loadingPage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示加载对话框失败");
        }
    }

    public async Task HideLoadingAsync()
    {
        try
        {
            _logger.LogDebug("隐藏加载对话框");
            
            if (_loadingPage != null && Application.Current?.MainPage != null)
            {
                await Application.Current.MainPage.Navigation.PopModalAsync();
                _loadingPage = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "隐藏加载对话框失败");
        }
    }

    public async Task ShowToastAsync(string message, int duration = 3000)
    {
        try
        {
            _logger.LogDebug("显示Toast消息: {Message}", message);

            // 在MAUI中，可以使用第三方库如CommunityToolkit.Maui.Alerts
            // 这里使用简单的实现
            if (Application.Current?.MainPage != null)
            {
                await Application.Current.MainPage.DisplayAlert("提示", message, "确定");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示Toast消息失败");
        }
    }

    public async Task<string?> ShowActionSheetAsync(string title, string cancel, string? destruction, params string[] buttons)
    {
        try
        {
            _logger.LogDebug("显示操作表: {Title}", title);

            if (Application.Current?.MainPage != null)
            {
                return await Application.Current.MainPage.DisplayActionSheet(title, cancel, destruction, buttons);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示操作表失败");
            return null;
        }
    }
}
