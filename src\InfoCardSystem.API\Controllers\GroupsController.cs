using System.Security.Claims;
using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.DTOs.Group;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 群组管理控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
[Authorize]
public class GroupsController : ControllerBase
{
    private readonly IGroupService _groupService;
    private readonly ILogger<GroupsController> _logger;

    public GroupsController(IGroupService groupService, ILogger<GroupsController> logger)
    {
        _groupService = groupService;
        _logger = logger;
    }

    /// <summary>
    /// 创建群组
    /// </summary>
    /// <param name="request">创建群组请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的群组信息</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<GroupDto>>> CreateGroup(
        [FromBody] CreateGroupRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<GroupDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.CreateGroupAsync(userId.Value, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取我的群组列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>群组列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<GroupDto>>>> GetMyGroups(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<PagedResult<GroupDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.GetUserGroupsAsync(userId.Value, page, pageSize, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取群组详情
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>群组详情</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<GroupDetailDto>>> GetGroupDetail(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<GroupDetailDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.GetGroupDetailAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return NotFound(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 更新群组信息
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="request">更新请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新后的群组信息</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<GroupDto>>> UpdateGroup(
        int id,
        [FromBody] UpdateGroupRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<GroupDto>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.UpdateGroupAsync(userId.Value, id, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 删除群组
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<bool>>> DeleteGroup(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.DeleteGroupAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取群组成员列表
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成员列表</returns>
    [HttpGet("{id}/members")]
    public async Task<ActionResult<ApiResponse<List<GroupMemberDto>>>> GetGroupMembers(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<List<GroupMemberDto>>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.GetGroupMembersAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 添加群组成员
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="request">添加成员请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/members")]
    public async Task<ActionResult<ApiResponse<bool>>> AddGroupMembers(
        int id,
        [FromBody] AddGroupMembersRequest request,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.AddGroupMembersAsync(userId.Value, id, request, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 移除群组成员
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="memberId">成员用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{id}/members/{memberId}")]
    public async Task<ActionResult<ApiResponse<bool>>> RemoveGroupMember(
        int id,
        int memberId,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.RemoveGroupMemberAsync(userId.Value, id, memberId, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 退出群组
    /// </summary>
    /// <param name="id">群组ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/leave")]
    public async Task<ActionResult<ApiResponse<bool>>> LeaveGroup(
        int id,
        CancellationToken cancellationToken = default)
    {
        var userId = GetCurrentUserId();
        if (userId == null)
        {
            return Unauthorized(ApiResponse<bool>.ErrorResult("用户未登录", "UNAUTHORIZED"));
        }

        var result = await _groupService.LeaveGroupAsync(userId.Value, id, cancellationToken);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <returns>用户ID</returns>
    private int? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
        {
            return userId;
        }
        return null;
    }
}
