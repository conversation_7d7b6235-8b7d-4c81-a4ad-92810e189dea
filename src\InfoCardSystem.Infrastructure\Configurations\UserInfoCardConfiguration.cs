using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户资讯卡实体配置
/// </summary>
public class UserInfoCardConfiguration : IEntityTypeConfiguration<UserInfoCard>
{
    public void Configure(EntityTypeBuilder<UserInfoCard> builder)
    {
        // 表名
        builder.ToTable("user_infocards");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.Title)
            .HasMaxLength(200)
            .IsRequired();
            
        builder.Property(x => x.Content)
            .HasMaxLength(2000)
            .IsRequired();
            
        builder.Property(x => x.InfoCardType)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(x => x.OriginalPublisherId)
            .IsRequired();
            
        builder.Property(x => x.DirectPublisherId)
            .IsRequired();
            
        builder.Property(x => x.ParentInfoCardId);
            
        builder.Property(x => x.ExpiresAt);
            
        builder.Property(x => x.AllowForward)
            .IsRequired()
            .HasDefaultValue(true);
            
        builder.Property(x => x.InfoCardStatus)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => x.OriginalPublisherId)
            .HasDatabaseName("idx_infocards_original_publisher");
            
        builder.HasIndex(x => x.DirectPublisherId)
            .HasDatabaseName("idx_infocards_direct_publisher");
            
        builder.HasIndex(x => new { x.InfoCardType, x.InfoCardStatus })
            .HasDatabaseName("idx_infocards_type_status");
            
        builder.HasIndex(x => x.ExpiresAt)
            .HasDatabaseName("idx_infocards_expires");
            
        builder.HasIndex(x => x.CreatedAt)
            .HasDatabaseName("idx_infocards_created");
        
        // 关系配置
        builder.HasOne(x => x.ParentInfoCard)
            .WithMany(x => x.ChildInfoCards)
            .HasForeignKey(x => x.ParentInfoCardId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.Recipients)
            .WithOne(x => x.InfoCard)
            .HasForeignKey(x => x.InfoCardId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.Favorites)
            .WithOne(x => x.InfoCard)
            .HasForeignKey(x => x.InfoCardId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.Attachments)
            .WithOne(x => x.InfoCard)
            .HasForeignKey(x => x.InfoCardId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
