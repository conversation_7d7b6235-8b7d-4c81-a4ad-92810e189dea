-- 添加密码重置令牌表和用户二维码表
-- 迁移文件: 20250710_AddPasswordResetAndQRCodeTables

-- 创建密码重置令牌表
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `UserId` int NOT NULL,
    `Token` varchar(255) NOT NULL,
    `EmailOrPhone` varchar(100) NOT NULL,
    `CreatedAt` datetime(6) NOT NULL,
    `ExpiresAt` datetime(6) NOT NULL,
    `IsUsed` tinyint(1) NOT NULL DEFAULT 0,
    `UsedAt` datetime(6) NULL,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `idx_password_reset_tokens_token` (`Token`),
    KEY `idx_password_reset_tokens_user_id` (`UserId`),
    KEY `idx_password_reset_tokens_email_phone` (`EmailOrPhone`),
    <PERSON>EY `idx_password_reset_tokens_expires_at` (`ExpiresAt`),
    CONSTRAINT `FK_password_reset_tokens_users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户二维码表
CREATE TABLE IF NOT EXISTS `user_qr_codes` (
    `Id` int NOT NULL AUTO_INCREMENT,
    `UserId` int NOT NULL,
    `QRCodeData` varchar(500) NOT NULL,
    `QRCodeUrl` varchar(500) NULL,
    `CreatedAt` datetime(6) NOT NULL,
    `ExpiresAt` datetime(6) NOT NULL,
    `IsActive` tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (`Id`),
    UNIQUE KEY `idx_user_qr_codes_data` (`QRCodeData`),
    KEY `idx_user_qr_codes_user_id` (`UserId`),
    KEY `idx_user_qr_codes_expires_at` (`ExpiresAt`),
    KEY `idx_user_qr_codes_user_active` (`UserId`, `IsActive`),
    CONSTRAINT `FK_user_qr_codes_users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
