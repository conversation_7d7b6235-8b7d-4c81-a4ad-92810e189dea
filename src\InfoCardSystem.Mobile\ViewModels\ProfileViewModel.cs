using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using InfoCardSystem.Mobile.Models;
using InfoCardSystem.Mobile.Services;
using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.ViewModels;

/// <summary>
/// 个人中心页面ViewModel
/// </summary>
public partial class ProfileViewModel : BaseViewModel
{
    private readonly IAuthenticationService _authService;
    private readonly IInfoCardApiClient _apiClient;

    public ProfileViewModel(
        INavigationService navigationService,
        IDialogService dialogService,
        IAuthenticationService authService,
        IInfoCardApiClient apiClient,
        ILogger<ProfileViewModel> logger)
        : base(navigationService, dialogService, logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        Title = "我的";
        
        // 初始化用户信息
        _ = LoadUserInfoAsync();
    }

    [ObservableProperty]
    private UserInfo userInfo = new();

    [ObservableProperty]
    private int infoCardCount;

    [ObservableProperty]
    private int friendCount;

    [ObservableProperty]
    private int groupCount;

    /// <summary>
    /// 编辑资料命令
    /// </summary>
    [RelayCommand]
    private async Task EditProfileAsync()
    {
        await NavigateToAsync("editprofile");
    }

    /// <summary>
    /// 我的资讯卡命令
    /// </summary>
    [RelayCommand]
    private async Task MyInfoCardsAsync()
    {
        await NavigateToAsync("mycards");
    }

    /// <summary>
    /// 收藏夹命令
    /// </summary>
    [RelayCommand]
    private async Task FavoritesAsync()
    {
        await NavigateToAsync("favorites");
    }

    /// <summary>
    /// 我的二维码命令
    /// </summary>
    [RelayCommand]
    private async Task MyQRCodeAsync()
    {
        await NavigateToAsync("myqrcode");
    }

    /// <summary>
    /// 设置命令
    /// </summary>
    [RelayCommand]
    private async Task SettingsAsync()
    {
        await NavigateToAsync("settings");
    }

    /// <summary>
    /// 帮助命令
    /// </summary>
    [RelayCommand]
    private async Task HelpAsync()
    {
        await ShowToastAsync("帮助功能即将推出");
    }

    /// <summary>
    /// 关于命令
    /// </summary>
    [RelayCommand]
    private async Task AboutAsync()
    {
        await NavigateToAsync("about");
    }

    /// <summary>
    /// 退出登录命令
    /// </summary>
    [RelayCommand]
    private async Task LogoutAsync()
    {
        var confirmed = await ShowConfirmAsync("退出登录", "确定要退出登录吗？");
        if (!confirmed) return;

        await ExecuteSafelyAsync(async () =>
        {
            _logger.LogInformation("用户退出登录");

            await _authService.LogoutAsync();
            
            // 导航到登录页面
            await NavigateToAsync("//login");
            
            _logger.LogInformation("用户退出登录完成");
        }, "退出登录");
    }

    /// <summary>
    /// 加载用户信息
    /// </summary>
    private async Task LoadUserInfoAsync()
    {
        try
        {
            _logger.LogDebug("加载用户信息");

            var currentUser = await _authService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                UserInfo = currentUser;
                
                // 加载统计信息
                await LoadStatisticsAsync();
                
                _logger.LogInformation("用户信息加载完成: {Username}", currentUser.Username);
            }
            else
            {
                _logger.LogWarning("未找到当前用户信息");
                
                // 如果没有用户信息，可能需要重新登录
                await NavigateToAsync("//login");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载用户信息失败");
            await ShowErrorAsync("加载失败", "无法加载用户信息，请重试");
        }
    }

    /// <summary>
    /// 加载统计信息
    /// </summary>
    private async Task LoadStatisticsAsync()
    {
        try
        {
            _logger.LogDebug("加载统计信息");

            // 模拟统计数据
            InfoCardCount = 15;
            FriendCount = 28;
            GroupCount = 5;

            _logger.LogInformation("统计信息加载完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载统计信息失败");
        }
    }

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    private async Task ShowToastAsync(string message)
    {
        await _dialogService.ShowToastAsync(message);
    }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        
        // 每次进入页面时刷新用户信息
        await LoadUserInfoAsync();
    }
}
