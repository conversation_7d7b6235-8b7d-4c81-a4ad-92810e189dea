<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.ForgotPasswordPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:ForgotPasswordViewModel"
             Title="忘记密码"
             Shell.NavBarIsVisible="True"
             BackgroundColor="{StaticResource Background}">

    <ScrollView>
        <StackLayout Padding="24" Spacing="24">
            
            <!-- 顶部图标和说明 -->
            <StackLayout VerticalOptions="Center" 
                        HorizontalOptions="Center"
                        Margin="0,40,0,20">
                
                <Label Text="🔐"
                       FontSize="64"
                       HorizontalOptions="Center" />
                
                <Label Text="忘记密码"
                       FontSize="24"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextPrimary}"
                       HorizontalOptions="Center"
                       Margin="0,16,0,8" />
                
                <Label Text="请输入您的邮箱或手机号，我们将发送重置密码的链接给您"
                       FontSize="14"
                       TextColor="{StaticResource TextSecondary}"
                       HorizontalOptions="Center"
                       HorizontalTextAlignment="Center"
                       LineBreakMode="WordWrap" />
                
            </StackLayout>

            <!-- 输入表单 -->
            <StackLayout Spacing="16">

                <!-- 邮箱或手机号输入框 -->
                <Frame Style="{StaticResource CardFrame}">
                    <StackLayout>
                        <Label Text="邮箱或手机号"
                               FontSize="14"
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextPrimary}"
                               Margin="0,0,0,8" />
                        <Entry x:Name="EmailOrPhoneEntry"
                               Text="{Binding EmailOrPhone}"
                               Placeholder="请输入邮箱地址或手机号"
                               FontSize="16"
                               BackgroundColor="Transparent"
                               TextColor="{StaticResource TextPrimary}"
                               PlaceholderColor="{StaticResource TextHint}" />
                    </StackLayout>
                </Frame>

                <!-- 发送按钮 -->
                <Button Text="发送重置链接"
                       FontSize="16"
                       FontAttributes="Bold"
                       HeightRequest="50"
                       Command="{Binding SendResetLinkCommand}"
                       IsEnabled="{Binding IsNotBusy}" />

                <!-- 加载指示器 -->
                <ActivityIndicator IsRunning="{Binding IsBusy}"
                                  IsVisible="{Binding IsBusy}"
                                  HorizontalOptions="Center" />

                <!-- 错误消息 -->
                <Frame IsVisible="{Binding HasError}"
                       BackgroundColor="{StaticResource Error}"
                       Margin="0,8,0,0">
                    <Label Text="{Binding ErrorMessage}"
                           TextColor="White"
                           FontSize="14"
                           HorizontalOptions="Center" />
                </Frame>

                <!-- 成功消息 -->
                <Frame IsVisible="{Binding IsSuccess}"
                       BackgroundColor="{StaticResource Success}"
                       Margin="0,8,0,0">
                    <StackLayout>
                        <Label Text="✅ 重置链接已发送"
                               TextColor="White"
                               FontSize="16"
                               FontAttributes="Bold"
                               HorizontalOptions="Center" />
                        <Label Text="请检查您的邮箱或短信，点击链接重置密码"
                               TextColor="White"
                               FontSize="14"
                               HorizontalOptions="Center"
                               HorizontalTextAlignment="Center"
                               Margin="0,4,0,0" />
                    </StackLayout>
                </Frame>

            </StackLayout>

            <!-- 底部链接 -->
            <StackLayout Margin="0,40,0,20">
                
                <!-- 分割线 -->
                <StackLayout Orientation="Horizontal" 
                            HorizontalOptions="Center"
                            Margin="0,0,0,20">
                    <BoxView BackgroundColor="{StaticResource Outline}" 
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                    <Label Text="或者" 
                           TextColor="{StaticResource TextHint}"
                           FontSize="14"
                           Margin="16,0" />
                    <BoxView BackgroundColor="{StaticResource Outline}" 
                            HeightRequest="1" 
                            WidthRequest="80"
                            VerticalOptions="Center" />
                </StackLayout>

                <!-- 其他选项 -->
                <Grid ColumnDefinitions="*,*" 
                      ColumnSpacing="16">
                    
                    <Button Grid.Column="0"
                           Text="返回登录"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding BackToLoginCommand}" />
                    
                    <Button Grid.Column="1"
                           Text="联系客服"
                           Style="{StaticResource TextButton}"
                           Command="{Binding ContactSupportCommand}" />
                    
                </Grid>

            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
