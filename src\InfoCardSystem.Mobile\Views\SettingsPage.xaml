<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="InfoCardSystem.Mobile.Views.SettingsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:InfoCardSystem.Mobile.ViewModels"
             x:DataType="viewmodels:SettingsViewModel"
             Title="设置"
             BackgroundColor="{StaticResource Background}">

    <ScrollView>
        <StackLayout Padding="16" Spacing="16">

            <!-- 账户设置 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="账户设置"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />

                    <!-- 修改密码 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🔐"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="修改密码"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="更改您的登录密码"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ChangePasswordCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>

                    <!-- 绑定手机 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="📱"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="绑定手机"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="{Binding PhoneNumber, FallbackValue='未绑定'}"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding BindPhoneCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>

                    <!-- 绑定邮箱 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="📧"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="绑定邮箱"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="{Binding EmailAddress, FallbackValue='未绑定'}"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding BindEmailCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 通知设置 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="通知设置"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />

                    <!-- 推送通知 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🔔"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="推送通知"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="接收新消息推送"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding PushNotificationEnabled}"
                               VerticalOptions="Center" />
                    </Grid>

                    <!-- 好友请求通知 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="👥"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="好友请求通知"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="收到好友请求时通知"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding FriendRequestNotificationEnabled}"
                               VerticalOptions="Center" />
                    </Grid>

                    <!-- 资讯卡通知 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="📋"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="资讯卡通知"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="好友发布资讯卡时通知"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding InfoCardNotificationEnabled}"
                               VerticalOptions="Center" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 隐私设置 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="隐私设置"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />

                    <!-- 允许搜索 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🔍"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="允许被搜索"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="其他用户可以搜索到您"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding AllowSearchEnabled}"
                               VerticalOptions="Center" />
                    </Grid>

                    <!-- 显示在线状态 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🟢"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="显示在线状态"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="好友可以看到您的在线状态"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding ShowOnlineStatusEnabled}"
                               VerticalOptions="Center" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 应用设置 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="应用设置"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />

                    <!-- 深色模式 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🌙"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="深色模式"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="使用深色主题"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Switch Grid.Column="2"
                               IsToggled="{Binding DarkModeEnabled}"
                               VerticalOptions="Center" />
                    </Grid>

                    <!-- 语言设置 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🌍"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="语言"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="{Binding CurrentLanguage}"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ChangeLanguageCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>

                    <!-- 清除缓存 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🗑️"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="清除缓存"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="{Binding CacheSize}"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding ClearCacheCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 关于 -->
            <Frame Style="{StaticResource CardFrame}">
                <StackLayout>
                    <Label Text="关于"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextPrimary}"
                           Margin="0,0,0,12" />

                    <!-- 版本信息 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="ℹ️"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="版本信息"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="{Binding AppVersion}"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding AboutCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>

                    <!-- 用户协议 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="📄"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="用户协议"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="查看用户服务协议"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding UserAgreementCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>

                    <!-- 隐私政策 -->
                    <Grid ColumnDefinitions="Auto,*,Auto" Margin="0,8">
                        <Label Grid.Column="0"
                               Text="🔒"
                               FontSize="20"
                               VerticalOptions="Center" />
                        <StackLayout Grid.Column="1" Margin="12,0,0,0">
                            <Label Text="隐私政策"
                                   FontSize="16"
                                   TextColor="{StaticResource TextPrimary}" />
                            <Label Text="查看隐私保护政策"
                                   FontSize="12"
                                   TextColor="{StaticResource TextSecondary}" />
                        </StackLayout>
                        <Label Grid.Column="2"
                               Text=">"
                               FontSize="16"
                               TextColor="{StaticResource TextHint}"
                               VerticalOptions="Center" />
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding PrivacyPolicyCommand}" />
                        </Grid.GestureRecognizers>
                    </Grid>
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
