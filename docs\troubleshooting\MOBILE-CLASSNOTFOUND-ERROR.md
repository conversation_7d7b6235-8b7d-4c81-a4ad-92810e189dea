# InfoCard Mobile - ClassNotFoundException 错误解决指南

## 🚨 错误描述

```
Java.Lang.RuntimeException: Unable to instantiate activity ComponentInfo{com.infocardsystem.mobile/crc64e1fb321c08285b90.MainActivity}: 
java.lang.ClassNotFoundException: Didn't find class "crc64e1fb321c08285b90.MainActivity"
```

## 🔍 问题分析

这个错误表明Android系统无法找到MainActivity类，通常由以下原因引起：

1. **构建缓存问题** - 旧的构建缓存导致类名不匹配
2. **AndroidManifest.xml配置错误** - 硬编码了错误的类名
3. **MAUI项目配置问题** - 项目配置不正确
4. **Android模拟器问题** - 模拟器状态异常

## ✅ 解决方案

### 方案1: 自动修复脚本（推荐）

运行我们提供的自动修复脚本：

```bash
scripts\fix-mobile-classnotfound.bat
```

这个脚本会自动执行以下步骤：
- 清理构建缓存
- 重启ADB服务
- 卸载旧版本应用
- 重新构建项目
- 验证Android模拟器连接

### 方案2: 手动修复步骤

#### 步骤1: 清理项目
```bash
# 清理构建缓存
dotnet clean src\InfoCardSystem.Mobile\InfoCardSystem.Mobile.csproj

# 删除bin和obj目录
rmdir /s /q src\InfoCardSystem.Mobile\bin
rmdir /s /q src\InfoCardSystem.Mobile\obj
```

#### 步骤2: 重启ADB服务
```bash
adb kill-server
adb start-server
adb devices
```

#### 步骤3: 卸载旧版本应用
```bash
adb uninstall com.infocardsystem.mobile
```

#### 步骤4: 重新构建项目
```bash
dotnet restore src\InfoCardSystem.Mobile\InfoCardSystem.Mobile.csproj
dotnet build src\InfoCardSystem.Mobile\InfoCardSystem.Mobile.csproj -c Debug
```

#### 步骤5: 重新部署
1. 在VS2022中设置InfoCardSystem.Mobile为启动项目
2. 选择Android模拟器作为部署目标
3. 按F5开始调试

### 方案3: 重置Android模拟器

如果问题持续存在，尝试重置Android模拟器：

1. **关闭模拟器**
2. **打开Android Device Manager**
3. **选择模拟器 → 操作 → Wipe Data**
4. **重新启动模拟器**
5. **等待完全启动后重新部署**

### 方案4: 创建新的Android模拟器

1. **打开Android Device Manager**
2. **点击"新建"**
3. **推荐配置**:
   - **设备**: Pixel 5
   - **系统映像**: Android 11.0 (API 30)
   - **架构**: x86_64
   - **RAM**: 4GB
   - **存储**: 8GB
4. **创建并启动新模拟器**

## 🔧 已修复的配置问题

### 1. AndroidManifest.xml修复
**问题**: 硬编码了错误的MainActivity类名
```xml
<!-- 修复前 -->
<activity android:name="crc64e1fb321c08285b90.MainActivity" ... />

<!-- 修复后 -->
<!-- 让MAUI自动处理类名 -->
```

### 2. MainActivity.cs增强
**改进**: 添加了错误处理和调试信息
```csharp
// 添加了异常处理
try
{
    // 设置状态栏颜色
    if (Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop)
    {
        Window?.SetStatusBarColor(Android.Graphics.Color.ParseColor("#1976D2"));
    }
}
catch (System.Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"MainActivity OnCreate error: {ex.Message}");
}
```

### 3. 目标SDK版本调整
**修复**: 从API 35降级到API 34以提高兼容性
```xml
<!-- 修复前 -->
<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="35" />

<!-- 修复后 -->
<uses-sdk android:minSdkVersion="21" android:targetSdkVersion="34" />
```

## 📋 预防措施

### 1. 开发环境检查
- [ ] Visual Studio 2022已安装MAUI工作负载
- [ ] Android SDK已正确配置
- [ ] Android模拟器已创建并正常运行
- [ ] ADB服务正常运行

### 2. 项目配置验证
- [ ] 项目目标框架为net8.0-android
- [ ] ApplicationId配置正确
- [ ] MainActivity类存在且配置正确
- [ ] AndroidManifest.xml配置正确

### 3. 构建流程
- [ ] 定期清理构建缓存
- [ ] 使用增量构建
- [ ] 避免同时运行多个Android应用
- [ ] 监控ADB连接状态

## 🚀 快速解决流程

### 标准流程（5分钟）
1. **运行修复脚本**: `scripts\fix-mobile-classnotfound.bat`
2. **等待完成**: 脚本会自动处理所有步骤
3. **重新部署**: 在VS2022中按F5启动

### 应急流程（10分钟）
1. **重启所有服务**: 重启VS2022、ADB、Android模拟器
2. **清理项目**: 删除bin/obj目录
3. **重新构建**: 完整重新构建项目
4. **重新部署**: 部署到干净的模拟器

## 📞 获取帮助

如果问题仍然存在：

1. **检查VS2022输出窗口**的详细错误信息
2. **查看Android日志**: `adb logcat`
3. **验证MAUI工作负载**: Visual Studio Installer
4. **联系技术支持**并提供：
   - 完整的错误堆栈跟踪
   - Android模拟器配置
   - Visual Studio版本信息
   - 项目配置详情

## 🎯 成功指标

解决方案成功的标志：
- 应用成功部署到Android模拟器
- MainActivity正常启动
- 应用界面正常显示
- 调试功能正常工作

---

**最后更新**: 2024年1月15日  
**适用版本**: Visual Studio 2022, .NET MAUI 8.0  
**支持平台**: Android API 21+
