using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户群组实体配置
/// </summary>
public class UserGroupConfiguration : IEntityTypeConfiguration<UserGroup>
{
    public void Configure(EntityTypeBuilder<UserGroup> builder)
    {
        // 表名
        builder.ToTable("user_groups");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.GroupName)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(x => x.GroupDescription)
            .HasMaxLength(500);
            
        builder.Property(x => x.AvatarUrl)
            .HasMaxLength(500);
            
        builder.Property(x => x.CreatorId)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => x.CreatorId)
            .HasDatabaseName("idx_groups_creator");
            
        builder.HasIndex(x => x.GroupName)
            .HasDatabaseName("idx_groups_name");
        
        // 关系配置
        builder.HasMany(x => x.Members)
            .WithOne(x => x.Group)
            .HasForeignKey(x => x.GroupId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.InfoCardRecipients)
            .WithOne(x => x.RecipientGroup)
            .HasForeignKey(x => x.RecipientId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
