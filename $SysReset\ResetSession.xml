<ResetSession TargetSet="True" TargetSignature="{EA648D38-38C9-498B-A942-C45700E3822A}" TargetCapacity="512110190592" TargetLocation="PCIROOT(0)#PCI(1D00)#PCI(0000)#NVME(P00T00L00)" TargetOffset="122683392" TargetPath="\" Phase="Online"><Options Scenario="Reset" WipeData="False" OverwriteSpace="True" PreserveWorkplace="False"/><OfflineBoot IsStaged="False" OriginalFirmwareTimeout="0" OriginalBCDDefault="{00000000-0000-0000-0000-000000000000}" OriginalBCDTimeout="0"><OriginalFirmwareBootOrder/><OriginalBCDDisplayOrder/></OfflineBoot><ExecState HaveTargetVolume="True" TargetVolumeAccessible="True" TargetVolumeRootSet="True" TargetVolumeRootSignature="{EA648D38-38C9-498B-A942-C45700E3822A}" TargetVolumeRootCapacity="512110190592" TargetVolumeRootLocation="PCIROOT(0)#PCI(1D00)#PCI(0000)#NVME(P00T00L00)" TargetVolumeRootOffset="122683392" TargetVolumeRootPath="\" TargetVolumeCapacity="497490587648" TargetVolumeFreeSpace="454754611200" HaveOldOS="True" OldOSRootSet="True" OldOSRootSignature="{EA648D38-38C9-498B-A942-C45700E3822A}" OldOSRootCapacity="512110190592" OldOSRootLocation="PCIROOT(0)#PCI(1D00)#PCI(0000)#NVME(P00T00L00)" OldOSRootOffset="122683392" OldOSRootPath="\Windows.old" HaveNewOS="True" NewOSRootSet="True" NewOSRootSignature="{EA648D38-38C9-498B-A942-C45700E3822A}" NewOSRootCapacity="512110190592" NewOSRootLocation="PCIROOT(0)#PCI(1D00)#PCI(0000)#NVME(P00T00L00)" NewOSRootOffset="122683392" NewOSRootPath="\" NewOSBootEntry="{E0568F1A-41AF-11EE-A2B1-B1862E3ACA98}" SetupSourcesCleaned="True" SetupSourcesDirSet="True" SetupSourcesDirSignature="{EA648D38-38C9-498B-A942-C45700E3822A}" SetupSourcesDirCapacity="512110190592" SetupSourcesDirLocation="PCIROOT(0)#PCI(1D00)#PCI(0000)#NVME(P00T00L00)" SetupSourcesDirOffset="122683392" SetupSourcesDirPath="\$WINDOWS.~BT\Sources" SavedWinRE="True" IgoreDiskSpaceValidate="False" Remediation="Continue"/><OnlinePhase/></ResetSession>
