﻿@*
    InfoCard主页组件

    功能说明：
    - 未登录用户：显示产品介绍和功能特性
    - 已登录用户：显示资讯卡动态和管理界面
    - 响应式设计，支持桌面和移动设备

    主要特性：
    - 自动检测认证状态
    - 资讯卡列表展示
    - 分页导航
    - 收藏和转发功能
    - 实时状态更新
*@

@page "/"
@using InfoCardSystem.Shared.DTOs
@using InfoCardSystem.Web.Services
@inject AuthStateService AuthState
@inject IApiService ApiService
@inject NavigationManager Navigation
@inject IToastService ToastService
@rendermode InteractiveServer

<PageTitle>InfoCard - 资讯分享平台</PageTitle>

@if (!AuthState.IsAuthenticated)
{
    <!-- 未登录用户的欢迎页面 -->
    <div class="hero-section bg-primary text-white py-5">
        <div class="container text-center">
            <h1 class="display-4 mb-4">欢迎来到InfoCard</h1>
            <p class="lead mb-4">好友间资讯卡分享平台，发现和分享精彩内容</p>
            <div class="d-flex justify-content-center gap-3">
                <a href="/login" class="btn btn-light btn-lg">立即登录</a>
                <a href="/register" class="btn btn-outline-light btn-lg">免费注册</a>
            </div>
        </div>
    </div>

    <div class="container py-5">
        <div class="row">
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-share-alt fa-2x"></i>
                </div>
                <h3>轻松分享</h3>
                <p class="text-muted">与好友分享有趣的资讯卡片，传递知识和见解</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h3>好友互动</h3>
                <p class="text-muted">建立好友关系，在私密的圈子里分享内容</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                    <i class="fas fa-bookmark fa-2x"></i>
                </div>
                <h3>收藏管理</h3>
                <p class="text-muted">收藏喜欢的资讯卡，随时回顾重要内容</p>
            </div>
        </div>
    </div>
}
else
{
    <!-- 已登录用户的主页面 -->
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 bg-light sidebar py-3">
                <div class="d-flex align-items-center mb-4">
                    <img src="@(AuthState.CurrentUser?.AvatarUrl ?? "/images/default-avatar.png")"
                         alt="头像"
                         class="rounded-circle me-2"
                         style="width: 40px; height: 40px;">
                    <div>
                        <div class="fw-bold">@AuthState.CurrentUser?.DisplayName</div>
                        <small class="text-muted">@AuthState.CurrentUser?.Username</small>
                    </div>
                </div>

                <nav class="nav flex-column">
                    <a class="nav-link active" href="/">
                        <i class="fas fa-home me-2"></i>首页
                    </a>
                    <a class="nav-link" href="/friends">
                        <i class="fas fa-users me-2"></i>好友
                    </a>
                    <a class="nav-link" href="/groups">
                        <i class="fas fa-layer-group me-2"></i>群组
                    </a>
                    <a class="nav-link" href="/favorites">
                        <i class="fas fa-bookmark me-2"></i>收藏
                    </a>
                    <a class="nav-link" href="/profile">
                        <i class="fas fa-user me-2"></i>个人资料
                    </a>
                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10">
                <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
                    <h2>资讯动态</h2>
                    <button class="btn btn-primary" @onclick="ShowCreateModal">
                        <i class="fas fa-plus me-2"></i>创建资讯卡
                    </button>
                </div>

                @if (isLoading)
                {
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">加载资讯卡中...</p>
                    </div>
                }
                else if (infoCards?.Any() == true)
                {
                    <div class="row py-3">
                        @foreach (var card in infoCards)
                        {
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 shadow-sm">
                                    @if (!string.IsNullOrEmpty(card.ImageUrl))
                                    {
                                        <img src="@card.ImageUrl" class="card-img-top" alt="资讯卡图片" style="height: 200px; object-fit: cover;">
                                    }
                                    <div class="card-body">
                                        <h5 class="card-title">@card.Title</h5>
                                        <p class="card-text">@card.Content</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>@card.PublisherName
                                            </small>
                                            <small class="text-muted">
                                                @card.CreatedAt.ToString("MM-dd HH:mm")
                                            </small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <div class="d-flex justify-content-between">
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewCard(card.Id)">
                                                <i class="fas fa-eye me-1"></i>查看
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" @onclick="() => ForwardCard(card.Id)">
                                                <i class="fas fa-share me-1"></i>转发
                                            </button>
                                            <button class="btn btn-sm @(card.IsFavorited ? "btn-warning" : "btn-outline-warning")"
                                                    @onclick="() => ToggleFavorite(card.Id, card.IsFavorited)">
                                                <i class="fas fa-bookmark me-1"></i>@(card.IsFavorited ? "已收藏" : "收藏")
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    <!-- 分页 -->
                    @if (totalPages > 1)
                    {
                        <nav aria-label="资讯卡分页">
                            <ul class="pagination justify-content-center">
                                <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => LoadPage(currentPage - 1)" disabled="@(currentPage == 1)">上一页</button>
                                </li>
                                @for (int i = 1; i <= totalPages; i++)
                                {
                                    var pageNumber = i;
                                    <li class="page-item @(currentPage == pageNumber ? "active" : "")">
                                        <button class="page-link" @onclick="() => LoadPage(pageNumber)">@pageNumber</button>
                                    </li>
                                }
                                <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => LoadPage(currentPage + 1)" disabled="@(currentPage == totalPages)">下一页</button>
                                </li>
                            </ul>
                        </nav>
                    }
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4>暂无资讯卡</h4>
                        <p class="text-muted">开始创建您的第一张资讯卡吧！</p>
                        <button class="btn btn-primary" @onclick="ShowCreateModal">
                            <i class="fas fa-plus me-2"></i>创建资讯卡
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private List<InfoCardDto>? infoCards;
    private bool isLoading = true;
    private int currentPage = 1;
    private int pageSize = 12;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        // 初始化认证状态
        await AuthState.InitializeAsync();

        // 如果已登录，加载资讯卡
        if (AuthState.IsAuthenticated)
        {
            await LoadInfoCards();
        }

        isLoading = false;
    }

    private async Task LoadInfoCards()
    {
        try
        {
            isLoading = true;
            var response = await ApiService.GetInfoCardsAsync(currentPage, pageSize);
            if (response.IsSuccess && response.Data != null)
            {
                infoCards = response.Data;
                // 这里应该从响应中获取总页数，暂时简单计算
                totalPages = Math.Max(1, (int)Math.Ceiling(infoCards.Count / (double)pageSize));
            }
            else
            {
                ToastService.ShowError("加载资讯卡失败");
                infoCards = new List<InfoCardDto>();
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"加载资讯卡时发生错误：{ex.Message}");
            infoCards = new List<InfoCardDto>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadPage(int page)
    {
        if (page < 1 || page > totalPages || page == currentPage) return;

        currentPage = page;
        await LoadInfoCards();
    }

    private void ShowCreateModal()
    {
        Navigation.NavigateTo("/infocards/create");
    }

    private void ViewCard(int cardId)
    {
        Navigation.NavigateTo($"/infocards/{cardId}");
    }

    private void ForwardCard(int cardId)
    {
        Navigation.NavigateTo($"/infocards/{cardId}/forward");
    }

    private async Task ToggleFavorite(int cardId, bool isFavorited)
    {
        try
        {
            ApiResponse<object> response;
            if (isFavorited)
            {
                response = await ApiService.UnfavoriteInfoCardAsync(cardId);
                if (response.IsSuccess)
                {
                    ToastService.ShowSuccess("已取消收藏");
                }
            }
            else
            {
                response = await ApiService.FavoriteInfoCardAsync(cardId);
                if (response.IsSuccess)
                {
                    ToastService.ShowSuccess("已添加到收藏");
                }
            }

            if (response.IsSuccess)
            {
                // 更新本地状态
                var card = infoCards?.FirstOrDefault(c => c.Id == cardId);
                if (card != null)
                {
                    card.IsFavorited = !isFavorited;
                    StateHasChanged();
                }
            }
            else
            {
                ToastService.ShowError(response.ErrorMessage ?? "操作失败");
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"操作失败：{ex.Message}");
        }
    }
}
