using InfoCardSystem.Shared.DTOs;
using InfoCardSystem.Shared.Enums;

namespace InfoCardSystem.Tests;

/// <summary>
/// 基础测试类，验证项目的基本功能
/// </summary>
public class BasicTests
{
    /// <summary>
    /// 测试DTO对象创建
    /// </summary>
    [Fact]
    public void CreateRegisterRequest_ShouldCreateValidObject()
    {
        // Arrange & Act
        var request = new RegisterRequestDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Password = "Password123!",
            ConfirmPassword = "Password123!",
            Phone = "1234567890"
        };

        // Assert
        Assert.NotNull(request);
        Assert.Equal("testuser", request.Username);
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("Password123!", request.Password);
        Assert.Equal("Password123!", request.ConfirmPassword);
        Assert.Equal("1234567890", request.Phone);
    }

    /// <summary>
    /// 测试登录请求DTO
    /// </summary>
    [Fact]
    public void CreateLoginRequest_ShouldCreateValidObject()
    {
        // Arrange & Act
        var request = new LoginRequestDto
        {
            LoginType = LoginType.Email,
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        // Assert
        Assert.NotNull(request);
        Assert.Equal(LoginType.Email, request.LoginType);
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("Password123!", request.Password);
    }

    /// <summary>
    /// 测试枚举值
    /// </summary>
    [Fact]
    public void UserStatus_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.True(Enum.IsDefined(typeof(UserStatus), UserStatus.Active));
        Assert.True(Enum.IsDefined(typeof(UserStatus), UserStatus.Inactive));
        Assert.True(Enum.IsDefined(typeof(UserStatus), UserStatus.Suspended));
    }

    /// <summary>
    /// 测试InfoCard类型枚举
    /// </summary>
    [Fact]
    public void InfoCardType_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.True(Enum.IsDefined(typeof(InfoCardType), InfoCardType.Request));
        Assert.True(Enum.IsDefined(typeof(InfoCardType), InfoCardType.Supply));
        Assert.True(Enum.IsDefined(typeof(InfoCardType), InfoCardType.Info));
    }

    /// <summary>
    /// 测试InfoCard状态枚举
    /// </summary>
    [Fact]
    public void InfoCardStatus_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.True(Enum.IsDefined(typeof(InfoCardStatus), InfoCardStatus.Active));
        Assert.True(Enum.IsDefined(typeof(InfoCardStatus), InfoCardStatus.Expired));
        Assert.True(Enum.IsDefined(typeof(InfoCardStatus), InfoCardStatus.Deleted));
    }

    /// <summary>
    /// 测试好友状态枚举
    /// </summary>
    [Fact]
    public void FriendshipStatus_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.True(Enum.IsDefined(typeof(FriendshipStatus), FriendshipStatus.Pending));
        Assert.True(Enum.IsDefined(typeof(FriendshipStatus), FriendshipStatus.Accepted));
        Assert.True(Enum.IsDefined(typeof(FriendshipStatus), FriendshipStatus.Rejected));
    }

    /// <summary>
    /// 测试字符串验证
    /// </summary>
    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("", false)]
    [InlineData("   ", false)]
    public void ValidateEmail_ShouldReturnCorrectResult(string email, bool expected)
    {
        // Act
        bool isValid = IsValidEmail(email);

        // Assert
        Assert.Equal(expected, isValid);
    }

    /// <summary>
    /// 简单的邮箱验证方法
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        return email.Contains("@") && email.Contains(".");
    }

    /// <summary>
    /// 测试密码强度验证
    /// </summary>
    [Theory]
    [InlineData("Password123!", true)]
    [InlineData("password", false)]
    [InlineData("PASSWORD", false)]
    [InlineData("Password", false)]
    [InlineData("Pass123!", true)]
    [InlineData("", false)]
    public void ValidatePassword_ShouldReturnCorrectResult(string password, bool expected)
    {
        // Act
        bool isValid = IsValidPassword(password);

        // Assert
        Assert.Equal(expected, isValid);
    }

    /// <summary>
    /// 简单的密码验证方法
    /// </summary>
    private static bool IsValidPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
            return false;

        bool hasUpper = password.Any(char.IsUpper);
        bool hasLower = password.Any(char.IsLower);
        bool hasDigit = password.Any(char.IsDigit);
        bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    /// <summary>
    /// 测试用户名验证
    /// </summary>
    [Theory]
    [InlineData("testuser", true)]
    [InlineData("test_user", true)]
    [InlineData("test-user", true)]
    [InlineData("test user", false)]
    [InlineData("", false)]
    [InlineData("a", false)]
    [InlineData("verylongusernamethatexceedsmaximumlength", false)]
    public void ValidateUsername_ShouldReturnCorrectResult(string username, bool expected)
    {
        // Act
        bool isValid = IsValidUsername(username);

        // Assert
        Assert.Equal(expected, isValid);
    }

    /// <summary>
    /// 简单的用户名验证方法
    /// </summary>
    private static bool IsValidUsername(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
            return false;

        if (username.Length < 2 || username.Length > 20)
            return false;

        if (username.Contains(' '))
            return false;

        return true;
    }
}
