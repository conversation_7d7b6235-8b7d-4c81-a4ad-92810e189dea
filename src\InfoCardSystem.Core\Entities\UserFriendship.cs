using InfoCardSystem.Shared.Enums;

namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户好友关系实体
/// </summary>
public class UserFriendship : BaseEntity
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 好友ID
    /// </summary>
    public int FriendId { get; set; }
    
    /// <summary>
    /// 好友别名
    /// </summary>
    public string? FriendAlias { get; set; }
    
    /// <summary>
    /// 好友关系状态
    /// </summary>
    public FriendshipStatus FriendshipStatus { get; set; } = FriendshipStatus.Pending;

    /// <summary>
    /// 状态（兼容性属性）
    /// </summary>
    public FriendshipStatus Status
    {
        get => FriendshipStatus;
        set => FriendshipStatus = value;
    }

    /// <summary>
    /// 好友请求消息
    /// </summary>
    public string? RequestMessage { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 用户
    /// </summary>
    public virtual AppUser User { get; set; } = null!;
    
    /// <summary>
    /// 好友
    /// </summary>
    public virtual AppUser Friend { get; set; } = null!;
}
