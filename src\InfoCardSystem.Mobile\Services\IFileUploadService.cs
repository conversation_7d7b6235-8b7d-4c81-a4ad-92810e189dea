namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 文件上传服务接口
/// </summary>
public interface IFileUploadService
{
    /// <summary>
    /// 拍照
    /// </summary>
    /// <returns>照片文件路径</returns>
    Task<string?> TakePhotoAsync();

    /// <summary>
    /// 从相册选择照片
    /// </summary>
    /// <returns>照片文件路径</returns>
    Task<string?> PickPhotoAsync();

    /// <summary>
    /// 选择文件
    /// </summary>
    /// <returns>文件路径</returns>
    Task<string?> PickFileAsync();

    /// <summary>
    /// 上传文件到服务器
    /// </summary>
    /// <param name="filePath">本地文件路径</param>
    /// <param name="fileName">文件名</param>
    /// <param name="contentType">文件类型</param>
    /// <returns>服务器文件URL</returns>
    Task<string?> UploadFileAsync(string filePath, string fileName, string contentType);

    /// <summary>
    /// 批量上传文件
    /// </summary>
    /// <param name="files">文件列表</param>
    /// <returns>服务器文件URL列表</returns>
    Task<List<string>> UploadFilesAsync(List<(string filePath, string fileName, string contentType)> files);

    /// <summary>
    /// 获取文件大小
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件大小(字节)</returns>
    Task<long> GetFileSizeAsync(string filePath);

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否存在</returns>
    Task<bool> FileExistsAsync(string filePath);

    /// <summary>
    /// 删除本地文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    Task DeleteFileAsync(string filePath);

    /// <summary>
    /// 压缩图片
    /// </summary>
    /// <param name="imagePath">图片路径</param>
    /// <param name="quality">压缩质量(0-100)</param>
    /// <returns>压缩后的图片路径</returns>
    Task<string?> CompressImageAsync(string imagePath, int quality = 80);
}
