using InfoCardSystem.Core.DTOs.User;

namespace InfoCardSystem.Core.DTOs.Friendship;

/// <summary>
/// 好友关系DTO
/// </summary>
public class FriendshipDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 好友信息
    /// </summary>
    public UserProfileDto Friend { get; set; } = null!;
    
    /// <summary>
    /// 好友别名
    /// </summary>
    public string? FriendAlias { get; set; }
    
    /// <summary>
    /// 好友关系状态
    /// </summary>
    public string FriendshipStatus { get; set; } = string.Empty;
    
    /// <summary>
    /// 建立好友关系时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 好友请求DTO
/// </summary>
public class FriendRequestDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 请求发送者信息
    /// </summary>
    public UserProfileDto Requester { get; set; } = null!;
    
    /// <summary>
    /// 请求状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 发送好友请求
/// </summary>
public class SendFriendRequestDto
{
    /// <summary>
    /// 目标用户的自定义用户ID
    /// </summary>
    public string TargetCustomUserId { get; set; } = string.Empty;
}

/// <summary>
/// 处理好友请求
/// </summary>
public class HandleFriendRequestDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int FriendshipId { get; set; }
    
    /// <summary>
    /// 是否接受请求
    /// </summary>
    public bool Accept { get; set; }
}

/// <summary>
/// 更新好友别名
/// </summary>
public class UpdateFriendAliasDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int FriendshipId { get; set; }
    
    /// <summary>
    /// 新的别名
    /// </summary>
    public string? Alias { get; set; }
}
