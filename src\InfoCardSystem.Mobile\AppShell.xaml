<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="InfoCardSystem.Mobile.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:InfoCardSystem.Mobile"
    xmlns:views="clr-namespace:InfoCardSystem.Mobile.Views"
    Title="InfoCard"
    FlyoutBehavior="Disabled">

    <!-- 启动页面 -->
    <ShellContent
        x:Name="SplashShell"
        Route="splash"
        ContentTemplate="{DataTemplate views:SplashPage}"
        Shell.FlyoutBehavior="Disabled" />

    <!-- 登录页面 -->
    <ShellContent
        x:Name="LoginShell"
        Route="login"
        ContentTemplate="{DataTemplate views:LoginPage}"
        Shell.FlyoutBehavior="Disabled" />

    <!-- 注册页面 -->
    <ShellContent
        x:Name="RegisterShell"
        Route="register"
        ContentTemplate="{DataTemplate views:RegisterPage}"
        Shell.FlyoutBehavior="Disabled" />

    <!-- 主应用页面 -->
    <TabBar x:Name="MainTabBar" Route="main">
        
        <!-- 首页时间线 -->
        <ShellContent
            Title="首页"
            Icon="home.png"
            Route="timeline"
            ContentTemplate="{DataTemplate views:InfoCardsPage}" />

        <!-- 我的资讯卡 -->
        <ShellContent
            Title="资讯卡"
            Icon="card.png"
            Route="mycards"
            ContentTemplate="{DataTemplate views:MyInfoCardsPage}" />

        <!-- 好友管理 -->
        <ShellContent
            Title="好友"
            Icon="friends.png"
            Route="friends"
            ContentTemplate="{DataTemplate views:FriendsPage}" />

        <!-- 个人中心 -->
        <ShellContent
            Title="我的"
            Icon="profile.png"
            Route="profile"
            ContentTemplate="{DataTemplate views:ProfilePage}" />

        <!-- 设置 -->
        <ShellContent
            Title="设置"
            Icon="settings.png"
            Route="settings"
            ContentTemplate="{DataTemplate views:SettingsPage}" />

    </TabBar>

</Shell>
