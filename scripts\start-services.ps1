Write-Host "Starting InfoCard System Services..." -ForegroundColor Green

# Start API
Write-Host "Starting API on port 8081..." -ForegroundColor Yellow
$apiProcess = Start-Process -FilePath "dotnet" -ArgumentList "InfoCardSystem.API.dll", "--urls", "http://localhost:8081" -WorkingDirectory "C:\Webs\ICAPI" -WindowStyle Minimized -PassThru
$env:ASPNETCORE_ENVIRONMENT = "IIS"

Start-Sleep -Seconds 10

# Test API
try {
    $apiResponse = Invoke-WebRequest -Uri "http://localhost:8081/health" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ API is running! Status: $($apiResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ API failed to start: $($_.Exception.Message)" -ForegroundColor Red
}

# Start Web
Write-Host "Starting Web on port 8082..." -ForegroundColor Yellow
$webProcess = Start-Process -FilePath "dotnet" -ArgumentList "InfoCardSystem.Web.dll", "--urls", "http://localhost:8082" -WorkingDirectory "C:\Webs\ICWeb" -WindowStyle Minimized -PassThru

Start-Sleep -Seconds 10

# Test Web
try {
    $webResponse = Invoke-WebRequest -Uri "http://localhost:8082/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ Web is running! Status: $($webResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ Web failed to start: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🌐 Access URLs:" -ForegroundColor Cyan
Write-Host "  • API Health: http://localhost:8081/health" -ForegroundColor White
Write-Host "  • API Swagger: http://localhost:8081/swagger" -ForegroundColor White
Write-Host "  • Web App: http://localhost:8082/" -ForegroundColor White

Write-Host "`nOpening browser windows..." -ForegroundColor Yellow
Start-Process "http://localhost:8081/health"
Start-Process "http://localhost:8081/swagger"
Start-Process "http://localhost:8082/"

Write-Host "`n✅ Services started successfully!" -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
