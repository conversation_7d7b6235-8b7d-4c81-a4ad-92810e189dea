@echo off
echo ========================================
echo InfoCard External Links Verification
echo ========================================
echo.

echo Checking external links and API calls...
echo.

echo 1. Checking infocard.com domain references...
findstr /R /S "infocard\.com" src\*.* docs\*.* 2>nul
if %errorLevel% equ 0 (
    echo ERROR: Found infocard.com domain references
) else (
    echo OK: No infocard.com domain references found
)
echo.

echo 2. Checking tools.ietf.org references...
findstr /R /S "tools\.ietf\.org" src\*.* docs\*.* 2>nul
if %errorLevel% equ 0 (
    echo ERROR: Found tools.ietf.org references
) else (
    echo OK: No tools.ietf.org references found
)
echo.

echo 3. Checking port configuration consistency...
echo.

echo   API configuration check:
findstr "8081" src\InfoCardSystem.API\appsettings*.json src\InfoCardSystem.API\Properties\launchSettings.json 2>nul | findstr -v "808[2-9]"
if %errorLevel% equ 0 (
    echo OK: API port configuration correct (8081)
) else (
    echo ERROR: API port configuration may have issues
)

echo   Web configuration check:
findstr "8082" src\InfoCardSystem.Web\appsettings*.json src\InfoCardSystem.Web\Properties\launchSettings.json 2>nul
if %errorLevel% equ 0 (
    echo OK: Web port configuration correct (8082)
) else (
    echo ERROR: Web port configuration may have issues
)

echo   API BaseUrl check:
findstr "localhost:8081" src\InfoCardSystem.Web\appsettings*.json src\InfoCardSystem.Web\Program.cs 2>nul
if %errorLevel% equ 0 (
    echo OK: Web project API address configuration correct
) else (
    echo ERROR: Web project API address configuration may have issues
)
echo.

echo 4. Checking old port references...
findstr /R "700[0-9]\|500[0-9]\|800[0-9]" src\InfoCardSystem.API\appsettings*.json src\InfoCardSystem.Web\appsettings*.json src\InfoCardSystem.Web\Program.cs src\InfoCardSystem.API\Properties\launchSettings.json src\InfoCardSystem.Web\Properties\launchSettings.json 2>nul
if %errorLevel% equ 0 (
    echo ERROR: Found old port references
) else (
    echo OK: No old port references found
)
echo.

echo 5. Checking Mobile project configuration...
findstr "localhost:8081" src\InfoCardSystem.Mobile\MauiProgram.cs 2>nul
if %errorLevel% equ 0 (
    echo OK: Mobile project API address configuration correct
) else (
    echo ERROR: Mobile project API address configuration may have issues
)
echo.

echo ========================================
echo Verification Complete
echo ========================================
echo.

echo Fix Summary:
echo - All infocard.com references replaced with localhost
echo - Removed tools.ietf.org and other external dependencies
echo - Unified local port configuration (API:8081, Web:8082)
echo - Updated all configuration files and documentation
echo.

echo Local Access URLs:
echo   - API Health Check: http://localhost:8081/health
echo   - API Documentation: http://localhost:8081/swagger
echo   - Web Application: http://localhost:8082/
echo.

pause
