using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Core.DTOs.Auth;

/// <summary>
/// 用户登录请求
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// 登录类型
    /// </summary>
    [Required(ErrorMessage = "登录类型不能为空")]
    public LoginType LoginType { get; set; }
    
    /// <summary>
    /// 邮箱（当LoginType为Email时必填）
    /// </summary>
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    public string? Email { get; set; }
    
    /// <summary>
    /// 手机号（当LoginType为Phone时必填）
    /// </summary>
    [Phone(ErrorMessage = "手机号格式不正确")]
    public string? Phone { get; set; }
    
    /// <summary>
    /// 自定义用户ID（当LoginType为UserId时必填）
    /// </summary>
    public string? CustomUserId { get; set; }
    
    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = string.Empty;
    
    /// <summary>
    /// 记住我
    /// </summary>
    public bool RememberMe { get; set; } = false;
}

/// <summary>
/// 登录类型枚举
/// </summary>
public enum LoginType
{
    /// <summary>
    /// 邮箱登录
    /// </summary>
    Email = 1,
    
    /// <summary>
    /// 手机号登录
    /// </summary>
    Phone = 2,
    
    /// <summary>
    /// 自定义用户ID登录
    /// </summary>
    UserId = 3
}
