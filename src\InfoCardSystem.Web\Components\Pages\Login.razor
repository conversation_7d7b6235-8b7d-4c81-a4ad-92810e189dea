@*
    用户登录页面组件

    功能说明：
    - 提供手机号+密码的登录方式
    - 支持密码可见性切换
    - 记住我功能
    - 表单验证和错误提示
    - 自动重定向到目标页面

    安全特性：
    - JWT令牌自动管理
    - 登录状态持久化
    - 防止重复提交
    - 错误信息友好提示
*@

@page "/login"
@using InfoCardSystem.Shared.DTOs
@using InfoCardSystem.Web.Services
@inject AuthStateService AuthState
@inject NavigationManager Navigation
@inject IToastService ToastService
@rendermode InteractiveServer

<PageTitle>登录 - InfoCard</PageTitle>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- 左侧背景图片区域 -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center bg-primary">
            <div class="text-center text-white">
                <h1 class="display-4 mb-4">InfoCard</h1>
                <p class="lead">好友间资讯卡分享平台</p>
                <p>分享知识，连接朋友，发现精彩内容</p>
            </div>
        </div>
        
        <!-- 右侧登录表单区域 -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="card-title">欢迎回来</h2>
                        <p class="text-muted">请登录您的账户</p>
                    </div>

                    <EditForm Model="loginRequest" OnValidSubmit="HandleLogin" FormName="LoginForm">
                        <DataAnnotationsValidator />
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱</label>
                            <InputText @bind-Value="loginRequest.Email"
                                      class="form-control form-control-lg"
                                      id="email"
                                      placeholder="请输入注册时使用的邮箱地址"
                                      disabled="@isLoading" />
                            <ValidationMessage For="() => loginRequest.Email" class="text-danger" />
                            <small class="form-text text-muted">请使用注册时填写的邮箱地址登录</small>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <InputText @bind-Value="loginRequest.Password" 
                                          type="@(showPassword ? "text" : "password")"
                                          class="form-control form-control-lg" 
                                          id="password" 
                                          placeholder="请输入密码" 
                                          disabled="@isLoading" />
                                <button type="button" 
                                        class="btn btn-outline-secondary" 
                                        @onclick="TogglePasswordVisibility"
                                        disabled="@isLoading">
                                    <i class="@(showPassword ? "fas fa-eye-slash" : "fas fa-eye")"></i>
                                </button>
                            </div>
                            <ValidationMessage For="() => loginRequest.Password" class="text-danger" />
                        </div>

                        <div class="mb-3 form-check">
                            <InputCheckbox @bind-Value="rememberMe" class="form-check-input" id="rememberMe" disabled="@isLoading" />
                            <label class="form-check-label" for="rememberMe">
                                记住我
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>正在验证身份...</span>
                                }
                                else
                                {
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    <span>登录</span>
                                }
                            </button>
                        </div>

                        <ValidationSummary class="text-danger" />
                    </EditForm>

                    <div class="text-center">
                        <p class="mb-0">
                            还没有账户？
                            <a href="/register" class="text-decoration-none">立即注册</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginRequestDto loginRequest = new() { LoginType = LoginType.Email };
    private bool isLoading = false;
    private bool showPassword = false;
    private bool rememberMe = false;

    protected override async Task OnInitializedAsync()
    {
        // 如果已经登录，重定向到主页
        if (AuthState.IsAuthenticated)
        {
            Navigation.NavigateTo("/");
            return;
        }

        // 初始化认证状态
        await AuthState.InitializeAsync();
    }

    private async Task HandleLogin()
    {
        if (isLoading) return;

        isLoading = true;
        try
        {
            var success = await AuthState.LoginAsync(loginRequest);
            if (success)
            {
                ToastService.ShowSuccess("登录成功！欢迎回来！");

                // 获取返回URL或默认跳转到主页
                var returnUrl = Navigation.GetUriWithQueryParameter("returnUrl", (string?)null);
                var targetUrl = !string.IsNullOrEmpty(returnUrl) ? returnUrl : "/";

                Navigation.NavigateTo(targetUrl);
            }
            else
            {
                var errorMessage = AuthState.LastError ?? "登录失败，请检查邮箱和密码";
                ToastService.ShowError(errorMessage);
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"登录时发生错误：{ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }
}
