@echo off
echo Updating web.config files for IIS...

echo Creating updated API web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath=".\InfoCardSystem.API.exe" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="IIS" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICAPI\web.config"

echo Creating updated Web web.config...
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<location path="." inheritInChildApplications="false"^>
echo     ^<system.webServer^>
echo       ^<handlers^>
echo         ^<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" /^>
echo       ^</handlers^>
echo       ^<aspNetCore processPath=".\InfoCardSystem.Web.exe" 
echo                   stdoutLogEnabled="true" 
echo                   stdoutLogFile=".\logs\stdout" 
echo                   hostingModel="inprocess"^>
echo         ^<environmentVariables^>
echo           ^<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="IIS" /^>
echo         ^</environmentVariables^>
echo       ^</aspNetCore^>
echo     ^</system.webServer^>
echo   ^</location^>
echo ^</configuration^>
) > "C:\Webs\ICWeb\web.config"

echo Creating logs directories...
if not exist "C:\Webs\ICAPI\logs" mkdir "C:\Webs\ICAPI\logs"
if not exist "C:\Webs\ICWeb\logs" mkdir "C:\Webs\ICWeb\logs"

echo Setting permissions...
icacls "C:\Webs\ICAPI\logs" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1
icacls "C:\Webs\ICWeb\logs" /grant "IIS_IUSRS:(OI)(CI)F" /T >nul 2>&1

echo Web.config files updated successfully!
echo Environment set to IIS, which will use appsettings.IIS.json
echo Stdout logging enabled for debugging
