namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户群组成员实体
/// </summary>
public class UserGroupMember : BaseEntity
{
    /// <summary>
    /// 群组ID
    /// </summary>
    public int GroupId { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 群组
    /// </summary>
    public virtual UserGroup Group { get; set; } = null!;
    
    /// <summary>
    /// 用户
    /// </summary>
    public virtual AppUser User { get; set; } = null!;
}
