using InfoCardSystem.Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace InfoCardSystem.Infrastructure.Configurations;

/// <summary>
/// 用户资讯卡接收者实体配置
/// </summary>
public class UserInfoCardRecipientConfiguration : IEntityTypeConfiguration<UserInfoCardRecipient>
{
    public void Configure(EntityTypeBuilder<UserInfoCardRecipient> builder)
    {
        // 表名
        builder.ToTable("user_infocard_recipients");
        
        // 主键
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.InfoCardId)
            .IsRequired();
            
        builder.Property(x => x.RecipientId)
            .IsRequired();
            
        builder.Property(x => x.RecipientType)
            .HasConversion<int>()
            .IsRequired();
            
        builder.Property(x => x.IsRead)
            .IsRequired()
            .HasDefaultValue(false);
            
        builder.Property(x => x.IsVisible)
            .IsRequired()
            .HasDefaultValue(true);
            
        builder.Property(x => x.ReceivedAt)
            .IsRequired();
            
        builder.Property(x => x.CreatedAt)
            .IsRequired();
            
        builder.Property(x => x.UpdatedAt)
            .IsRequired();
        
        // 索引
        builder.HasIndex(x => x.InfoCardId)
            .HasDatabaseName("idx_recipients_infocard");
            
        builder.HasIndex(x => new { x.RecipientId, x.RecipientType })
            .HasDatabaseName("idx_recipients_recipient");
            
        builder.HasIndex(x => new { x.RecipientId, x.RecipientType, x.IsVisible })
            .HasDatabaseName("idx_recipients_visible");
        
        // 关系配置
        builder.HasOne(x => x.RecipientUser)
            .WithMany()
            .HasForeignKey(x => x.RecipientId)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("FK_InfoCardRecipients_Users");
            
        // 注意：RecipientGroup关系在UserGroupConfiguration中定义
    }
}
