#!/bin/bash

# InfoCard系统项目验证脚本
# 验证项目结构完整性和代码质量
# 
# 使用方法:
#   ./scripts/verify-project.sh [选项]
#
# 选项:
#   --full      完整验证（包括构建测试）
#   --quick     快速验证（仅检查文件结构）
#   --fix       自动修复发现的问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "✓ $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            log_error "✗ $description"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            log_warning "⚠ $description"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
        return 1
    fi
}

# 验证项目结构
verify_project_structure() {
    log_info "验证项目结构..."
    
    # 检查根目录文件
    check_item "README.md 存在" "[ -f README.md ]"
    check_item ".gitignore 存在" "[ -f .gitignore ]"
    check_item "解决方案文件存在" "[ -f src/InfoCardSystem.sln ]"
    
    # 检查源码目录
    check_item "API项目目录存在" "[ -d src/InfoCardSystem.API ]"
    check_item "Core项目目录存在" "[ -d src/InfoCardSystem.Core ]"
    check_item "Infrastructure项目目录存在" "[ -d src/InfoCardSystem.Infrastructure ]"
    check_item "Web项目目录存在" "[ -d src/InfoCardSystem.Web ]"
    check_item "Mobile项目目录存在" "[ -d src/InfoCardSystem.Mobile ]"
    check_item "MiniProgram项目目录存在" "[ -d src/InfoCardSystem.MiniProgram ]"
    check_item "QuickApp项目目录存在" "[ -d src/InfoCardSystem.QuickApp ]"
    
    # 检查文档目录
    check_item "文档目录存在" "[ -d docs ]"
    check_item "安装文档存在" "[ -f docs/installation/README.md ]"
    check_item "部署文档存在" "[ -f docs/deployment/README.md ]"
    check_item "API文档存在" "[ -f docs/api/README.md ]"
    
    # 检查脚本目录
    check_item "脚本目录存在" "[ -d scripts ]"
    check_item "构建脚本存在" "[ -f scripts/build.sh ]"
    check_item "部署脚本存在" "[ -f scripts/deploy.sh ]"
    check_item "快速启动脚本存在" "[ -f scripts/quick-start.sh ]"
    
    log_success "项目结构验证完成"
}

# 验证项目文件
verify_project_files() {
    log_info "验证项目文件..."
    
    # 检查API项目文件
    check_item "API项目文件存在" "[ -f src/InfoCardSystem.API/InfoCardSystem.API.csproj ]"
    check_item "API Program.cs存在" "[ -f src/InfoCardSystem.API/Program.cs ]"
    check_item "API配置文件存在" "[ -f src/InfoCardSystem.API/appsettings.json ]"
    
    # 检查Core项目文件
    check_item "Core项目文件存在" "[ -f src/InfoCardSystem.Core/InfoCardSystem.Core.csproj ]"
    
    # 检查Infrastructure项目文件
    check_item "Infrastructure项目文件存在" "[ -f src/InfoCardSystem.Infrastructure/InfoCardSystem.Infrastructure.csproj ]"
    
    # 检查Web项目文件
    check_item "Web项目文件存在" "[ -f src/InfoCardSystem.Web/InfoCardSystem.Web.csproj ]"
    check_item "Web Program.cs存在" "[ -f src/InfoCardSystem.Web/Program.cs ]"
    
    # 检查Mobile项目文件
    check_item "Mobile项目文件存在" "[ -f src/InfoCardSystem.Mobile/InfoCardSystem.Mobile.csproj ]"
    
    # 检查小程序文件
    check_item "小程序配置文件存在" "[ -f src/InfoCardSystem.MiniProgram/app.json ]"
    check_item "小程序入口文件存在" "[ -f src/InfoCardSystem.MiniProgram/app.js ]"
    
    # 检查快应用文件
    check_item "快应用配置文件存在" "[ -f src/InfoCardSystem.QuickApp/manifest.json ]"
    check_item "快应用入口文件存在" "[ -f src/InfoCardSystem.QuickApp/app.ux ]"
    
    log_success "项目文件验证完成"
}

# 验证依赖和环境
verify_dependencies() {
    log_info "验证依赖和环境..."
    
    # 检查.NET SDK
    check_item ".NET SDK已安装" "command -v dotnet"
    if command -v dotnet > /dev/null 2>&1; then
        DOTNET_VERSION=$(dotnet --version)
        if [[ "$DOTNET_VERSION" =~ ^8\. ]]; then
            log_success "✓ .NET SDK版本: $DOTNET_VERSION"
        else
            log_warning "⚠ .NET SDK版本可能不兼容: $DOTNET_VERSION (推荐8.0+)"
        fi
    fi
    
    # 检查Node.js（可选）
    check_item "Node.js已安装" "command -v node" false
    
    # 检查Git
    check_item "Git已安装" "command -v git" false
    
    log_success "依赖验证完成"
}

# 验证代码质量
verify_code_quality() {
    log_info "验证代码质量..."
    
    # 检查解决方案是否可以还原
    check_item "NuGet包可以还原" "cd src && dotnet restore InfoCardSystem.sln"
    
    # 检查解决方案是否可以构建
    if [ "$FULL_VERIFY" = "true" ]; then
        check_item "解决方案可以构建" "cd src && dotnet build InfoCardSystem.sln --no-restore"
    fi
    
    # 检查代码格式
    check_item "代码格式检查" "cd src && dotnet format InfoCardSystem.sln --verify-no-changes --verbosity quiet" false
    
    log_success "代码质量验证完成"
}

# 验证配置文件
verify_configurations() {
    log_info "验证配置文件..."
    
    # 检查API配置
    if [ -f "src/InfoCardSystem.API/appsettings.json" ]; then
        check_item "API配置文件格式正确" "python3 -m json.tool src/InfoCardSystem.API/appsettings.json" false
    fi
    
    # 检查小程序配置
    if [ -f "src/InfoCardSystem.MiniProgram/app.json" ]; then
        check_item "小程序配置文件格式正确" "python3 -m json.tool src/InfoCardSystem.MiniProgram/app.json" false
    fi
    
    # 检查快应用配置
    if [ -f "src/InfoCardSystem.QuickApp/manifest.json" ]; then
        check_item "快应用配置文件格式正确" "python3 -m json.tool src/InfoCardSystem.QuickApp/manifest.json" false
    fi
    
    log_success "配置文件验证完成"
}

# 验证文档完整性
verify_documentation() {
    log_info "验证文档完整性..."
    
    # 检查主要文档文件
    local docs=(
        "README.md"
        "docs/README.md"
        "docs/installation/README.md"
        "docs/deployment/README.md"
        "docs/api/README.md"
        "src/InfoCardSystem.API/README.md"
        "src/InfoCardSystem.Web/README.md"
        "src/InfoCardSystem.Mobile/README.md"
        "src/InfoCardSystem.MiniProgram/README.md"
        "src/InfoCardSystem.QuickApp/README.md"
    )
    
    for doc in "${docs[@]}"; do
        check_item "文档存在: $doc" "[ -f $doc ]" false
    done
    
    # 检查文档内容长度（确保不是空文件）
    for doc in "${docs[@]}"; do
        if [ -f "$doc" ]; then
            check_item "文档有内容: $doc" "[ -s $doc ]" false
        fi
    done
    
    log_success "文档验证完成"
}

# 验证脚本权限
verify_scripts() {
    log_info "验证脚本权限..."
    
    local scripts=(
        "scripts/build.sh"
        "scripts/deploy.sh"
        "scripts/quick-start.sh"
        "scripts/verify-project.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            check_item "脚本可执行: $script" "[ -x $script ]"
            if [ ! -x "$script" ] && [ "$AUTO_FIX" = "true" ]; then
                chmod +x "$script"
                log_info "已修复脚本权限: $script"
            fi
        fi
    done
    
    log_success "脚本验证完成"
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local report_file="verification-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
InfoCard系统项目验证报告
========================

验证时间: $(date)
验证类型: $([ "$FULL_VERIFY" = "true" ] && echo "完整验证" || echo "快速验证")

验证结果统计:
- 总检查项: $TOTAL_CHECKS
- 通过检查: $PASSED_CHECKS
- 失败检查: $FAILED_CHECKS
- 警告检查: $WARNING_CHECKS

通过率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%

项目状态: $([ $FAILED_CHECKS -eq 0 ] && echo "✓ 验证通过" || echo "✗ 存在问题")

建议:
$([ $FAILED_CHECKS -gt 0 ] && echo "- 请修复失败的检查项" || echo "- 项目结构完整，可以正常使用")
$([ $WARNING_CHECKS -gt 0 ] && echo "- 建议处理警告项以获得更好的体验" || echo "")

详细日志请查看控制台输出。
EOF

    log_success "验证报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    echo "InfoCard系统项目验证脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --full      完整验证（包括构建测试）"
    echo "  --quick     快速验证（仅检查文件结构）"
    echo "  --fix       自动修复发现的问题"
    echo "  -h, --help  显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --quick          # 快速验证"
    echo "  $0 --full           # 完整验证"
    echo "  $0 --full --fix     # 完整验证并自动修复"
}

# 主函数
main() {
    log_info "开始验证InfoCard系统项目..."
    
    # 默认参数
    FULL_VERIFY=false
    QUICK_VERIFY=false
    AUTO_FIX=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --full)
                FULL_VERIFY=true
                shift
                ;;
            --quick)
                QUICK_VERIFY=true
                shift
                ;;
            --fix)
                AUTO_FIX=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查是否在项目根目录
    if [ ! -f "src/InfoCardSystem.sln" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行验证
    verify_project_structure
    verify_project_files
    verify_dependencies
    verify_configurations
    verify_documentation
    verify_scripts
    
    if [ "$FULL_VERIFY" = "true" ]; then
        verify_code_quality
    fi
    
    # 生成报告
    generate_report
    
    # 显示结果
    echo ""
    echo "=================================="
    echo "验证完成"
    echo "=================================="
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过: $PASSED_CHECKS"
    echo "失败: $FAILED_CHECKS"
    echo "警告: $WARNING_CHECKS"
    echo "通过率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        log_success "🎉 项目验证通过！项目结构完整，可以正常使用。"
        exit 0
    else
        log_error "❌ 项目验证失败！请修复上述问题后重新验证。"
        exit 1
    fi
}

# 执行主函数
main "$@"
