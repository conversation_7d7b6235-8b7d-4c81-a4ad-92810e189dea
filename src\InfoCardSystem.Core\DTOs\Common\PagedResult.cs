namespace InfoCardSystem.Core.DTOs.Common;

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据项
    /// </summary>
    public IEnumerable<T> Items { get; set; } = new List<T>();
    
    /// <summary>
    /// 分页信息
    /// </summary>
    public PaginationInfo Pagination { get; set; } = new();
}

/// <summary>
/// 分页信息
/// </summary>
public class PaginationInfo
{
    /// <summary>
    /// 当前页码
    /// </summary>
    public int CurrentPage { get; set; }
    
    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }
    
    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalItems { get; set; }
    
    /// <summary>
    /// 每页记录数
    /// </summary>
    public int ItemsPerPage { get; set; }
    
    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => CurrentPage > 1;
    
    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => CurrentPage < TotalPages;
}

/// <summary>
/// 分页请求参数
/// </summary>
public class PagedRequest
{
    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    public int Page { get; set; } = 1;
    
    /// <summary>
    /// 每页大小
    /// </summary>
    public int PageSize { get; set; } = 20;
    
    /// <summary>
    /// 排序字段
    /// </summary>
    public string? SortBy { get; set; }
    
    /// <summary>
    /// 排序方向（asc/desc）
    /// </summary>
    public string SortOrder { get; set; } = "desc";
    
    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? SearchKeyword { get; set; }
}
