using Microsoft.Extensions.Logging;

namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 首选项服务实现
/// </summary>
public class PreferencesService : IPreferencesService
{
    private readonly IPreferences _preferences;
    private readonly ILogger<PreferencesService> _logger;

    public PreferencesService(IPreferences preferences, ILogger<PreferencesService> logger)
    {
        _preferences = preferences;
        _logger = logger;
    }

    public string GetString(string key, string defaultValue = "")
    {
        try
        {
            return _preferences.Get(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取字符串首选项失败: {Key}", key);
            return defaultValue;
        }
    }

    public void SetString(string key, string value)
    {
        try
        {
            _preferences.Set(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置字符串首选项失败: {Key}", key);
        }
    }

    public bool GetBool(string key, bool defaultValue = false)
    {
        try
        {
            return _preferences.Get(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取布尔首选项失败: {Key}", key);
            return defaultValue;
        }
    }

    public void SetBool(string key, bool value)
    {
        try
        {
            _preferences.Set(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置布尔首选项失败: {Key}", key);
        }
    }

    public int GetInt(string key, int defaultValue = 0)
    {
        try
        {
            return _preferences.Get(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取整数首选项失败: {Key}", key);
            return defaultValue;
        }
    }

    public void SetInt(string key, int value)
    {
        try
        {
            _preferences.Set(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置整数首选项失败: {Key}", key);
        }
    }

    public double GetDouble(string key, double defaultValue = 0.0)
    {
        try
        {
            return _preferences.Get(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取双精度首选项失败: {Key}", key);
            return defaultValue;
        }
    }

    public void SetDouble(string key, double value)
    {
        try
        {
            _preferences.Set(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置双精度首选项失败: {Key}", key);
        }
    }

    public DateTime GetDateTime(string key, DateTime defaultValue)
    {
        try
        {
            return _preferences.Get(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日期时间首选项失败: {Key}", key);
            return defaultValue;
        }
    }

    public void SetDateTime(string key, DateTime value)
    {
        try
        {
            _preferences.Set(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置日期时间首选项失败: {Key}", key);
        }
    }

    public bool ContainsKey(string key)
    {
        try
        {
            return _preferences.ContainsKey(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查首选项键失败: {Key}", key);
            return false;
        }
    }

    public void Remove(string key)
    {
        try
        {
            _preferences.Remove(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除首选项失败: {Key}", key);
        }
    }

    public void Clear()
    {
        try
        {
            _preferences.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除所有首选项失败");
        }
    }
}
