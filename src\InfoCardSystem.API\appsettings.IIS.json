{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=infinitycircle;Uid=root;Pwd=************;CharSet=utf8mb4;"}, "Jwt": {"Key": "InfoCardSystem_JWT_Secret_Key_2024_Very_Long_And_Secure_Key_For_Production_Use", "Issuer": "InfoCardSystem", "Audience": "InfoCardSystem.Client", "ExpirationMinutes": 1440}, "App": {"BaseUrl": "http://localhost:8081"}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "Username": "", "Password": "", "FromEmail": "", "FromName": "InfoCard System"}, "FileUpload": {"UploadPath": "uploads", "MaxFileSize": 10485760, "MaxImageSize": 10485760, "MaxDocumentSize": 20971520, "MaxVideoSize": 104857600, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt", ".zip"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "InfoCardSystem": "Information"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "}}, "Performance": {"SlowRequestThresholdMs": 1000, "EnableDetailedMetrics": true, "EnableMemoryMonitoring": true, "MaxMemoryMB": 1024, "MinFreeSpaceGB": 1}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:8081"}}}}