namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 对话框服务接口
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示错误对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    Task ShowErrorAsync(string title, string message);

    /// <summary>
    /// 显示成功消息
    /// </summary>
    /// <param name="message">消息</param>
    Task ShowSuccessAsync(string message);

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    /// <returns>用户选择结果</returns>
    Task<bool> ShowConfirmAsync(string title, string message);

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="message">消息</param>
    /// <param name="placeholder">占位符</param>
    /// <returns>用户输入内容</returns>
    Task<string?> ShowPromptAsync(string title, string message, string placeholder = "");

    /// <summary>
    /// 显示加载对话框
    /// </summary>
    /// <param name="message">消息</param>
    Task ShowLoadingAsync(string message);

    /// <summary>
    /// 隐藏加载对话框
    /// </summary>
    Task HideLoadingAsync();

    /// <summary>
    /// 显示Toast消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="duration">持续时间(毫秒)</param>
    Task ShowToastAsync(string message, int duration = 3000);

    /// <summary>
    /// 显示操作表
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="cancel">取消按钮文本</param>
    /// <param name="destruction">销毁按钮文本</param>
    /// <param name="buttons">其他按钮</param>
    /// <returns>用户选择的按钮文本</returns>
    Task<string?> ShowActionSheetAsync(string title, string cancel, string? destruction, params string[] buttons);
}
