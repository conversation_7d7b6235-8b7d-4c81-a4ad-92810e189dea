namespace InfoCardSystem.Core.Entities;

/// <summary>
/// 用户群组实体
/// </summary>
public class UserGroup : BaseEntity
{
    /// <summary>
    /// 群组名称
    /// </summary>
    public string GroupName { get; set; } = string.Empty;
    
    /// <summary>
    /// 群组描述
    /// </summary>
    public string? GroupDescription { get; set; }
    
    /// <summary>
    /// 群组头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
    
    /// <summary>
    /// 创建者ID
    /// </summary>
    public int CreatorId { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 创建者
    /// </summary>
    public virtual AppUser Creator { get; set; } = null!;
    
    /// <summary>
    /// 群组成员
    /// </summary>
    public virtual ICollection<UserGroupMember> Members { get; set; } = new List<UserGroupMember>();
    
    /// <summary>
    /// 发送给该群组的资讯卡接收记录
    /// </summary>
    public virtual ICollection<UserInfoCardRecipient> InfoCardRecipients { get; set; } = new List<UserInfoCardRecipient>();
}
