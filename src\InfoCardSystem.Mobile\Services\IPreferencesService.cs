namespace InfoCardSystem.Mobile.Services;

/// <summary>
/// 首选项服务接口
/// </summary>
public interface IPreferencesService
{
    /// <summary>
    /// 获取字符串值
    /// </summary>
    string GetString(string key, string defaultValue = "");

    /// <summary>
    /// 设置字符串值
    /// </summary>
    void SetString(string key, string value);

    /// <summary>
    /// 获取布尔值
    /// </summary>
    bool GetBool(string key, bool defaultValue = false);

    /// <summary>
    /// 设置布尔值
    /// </summary>
    void SetBool(string key, bool value);

    /// <summary>
    /// 获取整数值
    /// </summary>
    int GetInt(string key, int defaultValue = 0);

    /// <summary>
    /// 设置整数值
    /// </summary>
    void SetInt(string key, int value);

    /// <summary>
    /// 获取双精度值
    /// </summary>
    double GetDouble(string key, double defaultValue = 0.0);

    /// <summary>
    /// 设置双精度值
    /// </summary>
    void SetDouble(string key, double value);

    /// <summary>
    /// 获取日期时间值
    /// </summary>
    DateTime GetDateTime(string key, DateTime defaultValue);

    /// <summary>
    /// 设置日期时间值
    /// </summary>
    void SetDateTime(string key, DateTime value);

    /// <summary>
    /// 检查键是否存在
    /// </summary>
    bool ContainsKey(string key);

    /// <summary>
    /// 移除键值对
    /// </summary>
    void Remove(string key);

    /// <summary>
    /// 清除所有首选项
    /// </summary>
    void Clear();
}
