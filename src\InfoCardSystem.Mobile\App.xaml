<?xml version="1.0" encoding="UTF-8" ?>
<Application x:Class="InfoCardSystem.Mobile.App"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:InfoCardSystem.Mobile"
             xmlns:converters="clr-namespace:InfoCardSystem.Mobile.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:CountToBoolConverter x:Key="CountToBoolConverter" />
            <converters:FavoriteTextConverter x:Key="FavoriteTextConverter" />
            <converters:FavoriteColorConverter x:Key="FavoriteColorConverter" />
            <converters:OnlineStatusColorConverter x:Key="OnlineStatusColorConverter" />

        </ResourceDictionary>
    </Application.Resources>
</Application>
