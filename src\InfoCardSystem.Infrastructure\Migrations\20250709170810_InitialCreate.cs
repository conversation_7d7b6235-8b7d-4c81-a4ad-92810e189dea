﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace InfoCardSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "app_users",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    CustomUserId = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Username = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Email = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Phone = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PasswordHash = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AvatarUrl = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Bio = table.Column<string>(type: "varchar(1000)", maxLength: 1000, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UserStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_app_users", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_attachments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    FileName = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OriginalFileName = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FilePath = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    MimeType = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UploaderId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_attachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_attachments_app_users_UploaderId",
                        column: x => x.UploaderId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_blacklist",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    BlockedUserId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_blacklist", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_blacklist_app_users_BlockedUserId",
                        column: x => x.BlockedUserId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_user_blacklist_app_users_UserId",
                        column: x => x.UserId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_friendships",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    FriendId = table.Column<int>(type: "int", nullable: false),
                    FriendAlias = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FriendshipStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_friendships", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_friendships_app_users_FriendId",
                        column: x => x.FriendId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_user_friendships_app_users_UserId",
                        column: x => x.UserId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_groups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    GroupName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    GroupDescription = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AvatarUrl = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_groups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_groups_app_users_CreatorId",
                        column: x => x.CreatorId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_infocards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    Title = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Content = table.Column<string>(type: "varchar(2000)", maxLength: 2000, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    InfoCardType = table.Column<int>(type: "int", nullable: false),
                    OriginalPublisherId = table.Column<int>(type: "int", nullable: false),
                    DirectPublisherId = table.Column<int>(type: "int", nullable: false),
                    ParentInfoCardId = table.Column<int>(type: "int", nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    AllowForward = table.Column<bool>(type: "tinyint(1)", nullable: false, defaultValue: true),
                    InfoCardStatus = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_infocards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_infocards_app_users_DirectPublisherId",
                        column: x => x.DirectPublisherId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_user_infocards_app_users_OriginalPublisherId",
                        column: x => x.OriginalPublisherId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_infocards_user_infocards_ParentInfoCardId",
                        column: x => x.ParentInfoCardId,
                        principalTable: "user_infocards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_group_members",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    GroupId = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    JoinedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_group_members", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_group_members_app_users_UserId",
                        column: x => x.UserId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_group_members_user_groups_GroupId",
                        column: x => x.GroupId,
                        principalTable: "user_groups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_infocard_attachments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    InfoCardId = table.Column<int>(type: "int", nullable: false),
                    AttachmentId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_infocard_attachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_infocard_attachments_user_attachments_AttachmentId",
                        column: x => x.AttachmentId,
                        principalTable: "user_attachments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_infocard_attachments_user_infocards_InfoCardId",
                        column: x => x.InfoCardId,
                        principalTable: "user_infocards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_infocard_favorites",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    InfoCardId = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_infocard_favorites", x => x.Id);
                    table.ForeignKey(
                        name: "FK_user_infocard_favorites_app_users_UserId",
                        column: x => x.UserId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_infocard_favorites_user_infocards_InfoCardId",
                        column: x => x.InfoCardId,
                        principalTable: "user_infocards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_infocard_recipients",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    InfoCardId = table.Column<int>(type: "int", nullable: false),
                    RecipientId = table.Column<int>(type: "int", nullable: false),
                    RecipientType = table.Column<int>(type: "int", nullable: false),
                    IsRead = table.Column<bool>(type: "tinyint(1)", nullable: false, defaultValue: false),
                    IsVisible = table.Column<bool>(type: "tinyint(1)", nullable: false, defaultValue: true),
                    ReceivedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_infocard_recipients", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InfoCardRecipients_Users",
                        column: x => x.RecipientId,
                        principalTable: "app_users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_infocard_recipients_user_groups_RecipientId",
                        column: x => x.RecipientId,
                        principalTable: "user_groups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_user_infocard_recipients_user_infocards_InfoCardId",
                        column: x => x.InfoCardId,
                        principalTable: "user_infocards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "idx_users_custom_user_id",
                table: "app_users",
                column: "CustomUserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_users_email",
                table: "app_users",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_users_phone",
                table: "app_users",
                column: "Phone",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_users_username",
                table: "app_users",
                column: "Username");

            migrationBuilder.CreateIndex(
                name: "idx_attachments_created",
                table: "user_attachments",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "idx_attachments_filename",
                table: "user_attachments",
                column: "FileName");

            migrationBuilder.CreateIndex(
                name: "idx_attachments_uploader",
                table: "user_attachments",
                column: "UploaderId");

            migrationBuilder.CreateIndex(
                name: "idx_blacklist_blocked_user",
                table: "user_blacklist",
                column: "BlockedUserId");

            migrationBuilder.CreateIndex(
                name: "idx_blacklist_user_blocked",
                table: "user_blacklist",
                columns: new[] { "UserId", "BlockedUserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_friendships_status",
                table: "user_friendships",
                column: "FriendshipStatus");

            migrationBuilder.CreateIndex(
                name: "idx_friendships_user_friend",
                table: "user_friendships",
                columns: new[] { "UserId", "FriendId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_user_friendships_FriendId",
                table: "user_friendships",
                column: "FriendId");

            migrationBuilder.CreateIndex(
                name: "idx_group_members_group_user",
                table: "user_group_members",
                columns: new[] { "GroupId", "UserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_group_members_user",
                table: "user_group_members",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "idx_groups_creator",
                table: "user_groups",
                column: "CreatorId");

            migrationBuilder.CreateIndex(
                name: "idx_groups_name",
                table: "user_groups",
                column: "GroupName");

            migrationBuilder.CreateIndex(
                name: "idx_infocard_attachments_attachment",
                table: "user_infocard_attachments",
                column: "AttachmentId");

            migrationBuilder.CreateIndex(
                name: "idx_infocard_attachments_infocard_attachment",
                table: "user_infocard_attachments",
                columns: new[] { "InfoCardId", "AttachmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_favorites_infocard_user",
                table: "user_infocard_favorites",
                columns: new[] { "InfoCardId", "UserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_favorites_user",
                table: "user_infocard_favorites",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "idx_recipients_infocard",
                table: "user_infocard_recipients",
                column: "InfoCardId");

            migrationBuilder.CreateIndex(
                name: "idx_recipients_recipient",
                table: "user_infocard_recipients",
                columns: new[] { "RecipientId", "RecipientType" });

            migrationBuilder.CreateIndex(
                name: "idx_recipients_visible",
                table: "user_infocard_recipients",
                columns: new[] { "RecipientId", "RecipientType", "IsVisible" });

            migrationBuilder.CreateIndex(
                name: "idx_infocards_created",
                table: "user_infocards",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "idx_infocards_direct_publisher",
                table: "user_infocards",
                column: "DirectPublisherId");

            migrationBuilder.CreateIndex(
                name: "idx_infocards_expires",
                table: "user_infocards",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "idx_infocards_original_publisher",
                table: "user_infocards",
                column: "OriginalPublisherId");

            migrationBuilder.CreateIndex(
                name: "idx_infocards_type_status",
                table: "user_infocards",
                columns: new[] { "InfoCardType", "InfoCardStatus" });

            migrationBuilder.CreateIndex(
                name: "IX_user_infocards_ParentInfoCardId",
                table: "user_infocards",
                column: "ParentInfoCardId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "user_blacklist");

            migrationBuilder.DropTable(
                name: "user_friendships");

            migrationBuilder.DropTable(
                name: "user_group_members");

            migrationBuilder.DropTable(
                name: "user_infocard_attachments");

            migrationBuilder.DropTable(
                name: "user_infocard_favorites");

            migrationBuilder.DropTable(
                name: "user_infocard_recipients");

            migrationBuilder.DropTable(
                name: "user_attachments");

            migrationBuilder.DropTable(
                name: "user_groups");

            migrationBuilder.DropTable(
                name: "user_infocards");

            migrationBuilder.DropTable(
                name: "app_users");
        }
    }
}
