using System.ComponentModel.DataAnnotations;

namespace InfoCardSystem.Shared.DTOs;

/// <summary>
/// 好友DTO
/// </summary>
public class FriendDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 好友信息
    /// </summary>
    public UserProfileDto Friend { get; set; } = null!;
    
    /// <summary>
    /// 好友别名
    /// </summary>
    public string? FriendAlias { get; set; }
    
    /// <summary>
    /// 好友关系状态
    /// </summary>
    public string FriendshipStatus { get; set; } = string.Empty;
    
    /// <summary>
    /// 建立好友关系时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 好友请求DTO
/// </summary>
public class FriendRequestDto
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 请求发送者信息
    /// </summary>
    public UserProfileDto Requester { get; set; } = null!;
    
    /// <summary>
    /// 请求状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 请求时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 发送好友请求
/// </summary>
public class SendFriendRequestDto
{
    /// <summary>
    /// 目标用户的手机号或用户名
    /// </summary>
    [Required(ErrorMessage = "目标用户标识不能为空")]
    public string TargetIdentifier { get; set; } = string.Empty;
    
    /// <summary>
    /// 请求消息
    /// </summary>
    [StringLength(200, ErrorMessage = "请求消息长度不能超过200个字符")]
    public string? Message { get; set; }
}
