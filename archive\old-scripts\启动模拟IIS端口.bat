@echo off
title InfoCard 模拟IIS端口服务 (9081/9082)
color 0C

echo ========================================
echo   InfoCard 模拟IIS端口服务启动器
echo   API: 9081端口 (模拟8081), Web: 9082端口 (模拟8082)
echo ========================================
echo.

echo [INFO] 停止现有服务...
taskkill /IM "dotnet.exe" /F >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo 步骤 1: 检查MySQL服务
echo ========================================
sc query MySQL80 | findstr "RUNNING" >nul
if %errorLevel% equ 0 (
    echo ✅ MySQL服务已运行
) else (
    echo ⚠️  MySQL服务未运行，正在启动...
    net start MySQL80 >nul 2>&1
    if %errorLevel% equ 0 (
        echo ✅ MySQL服务启动成功
    ) else (
        echo ❌ MySQL服务启动失败，请手动启动
    )
)

echo.
echo ========================================
echo 步骤 2: 构建项目
echo ========================================
echo 正在构建解决方案...
dotnet build InfoCardSystem.VS2022.sln -c Release --verbosity quiet
if %errorLevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
) else (
    echo ✅ 项目构建成功
)

echo.
echo ========================================
echo 步骤 3: 启动API服务 (端口9081)
echo ========================================
echo 正在后台启动API服务...
start "InfoCard API - 9081" /MIN cmd /c "cd /d %~dp0src\InfoCardSystem.API && dotnet run --environment Production --urls http://localhost:9081"
echo ✅ API服务启动中... (端口: 9081)

echo 等待API服务完全启动...
timeout /t 12 /nobreak >nul

echo.
echo ========================================
echo 步骤 4: 启动Web应用 (端口9082)
echo ========================================
echo 正在后台启动Web应用...
start "InfoCard Web - 9082" /MIN cmd /c "cd /d %~dp0src\InfoCardSystem.Web && dotnet run --environment Production --urls http://localhost:9082"
echo ✅ Web应用启动中... (端口: 9082)

echo 等待Web应用完全启动...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo 步骤 5: 验证服务状态
echo ========================================
echo 检查API服务 (9081)...
curl -s http://localhost:9081/health >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ API服务运行正常 (9081)
) else (
    echo ⚠️  API服务可能还在启动中 (9081)
)

echo 检查Web应用 (9082)...
curl -s -I http://localhost:9082 >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Web应用运行正常 (9082)
) else (
    echo ⚠️  Web应用可能还在启动中 (9082)
)

echo.
echo ========================================
echo 🎉 模拟IIS端口服务启动完成！
echo ========================================
echo.
echo 📱 应用访问地址 (模拟IIS端口):
echo    🔧 API服务:     http://localhost:9081 (模拟8081)
echo    🔧 API文档:     http://localhost:9081/swagger
echo    💚 健康检查:    http://localhost:9081/health
echo    🌐 Web应用:     http://localhost:9082 (模拟8082)
echo.
echo 🛠️  VS2022发布测试说明:
echo    1. 这些端口模拟了IIS的8081和8082端口
echo    2. 可以测试VS2022发布功能到C:\Webs\ICAPI和C:\Webs\ICWeb
echo    3. 实际IIS配置需要管理员权限修复
echo.
echo 📋 常用操作:
echo    1. 按任意键打开Web应用 (9082)
echo    2. 输入 'a' 打开API文档 (9081)
echo    3. 输入 'h' 查看健康检查
echo    4. 输入 'v' 打开VS2022
echo    5. 输入 'q' 退出
echo.

set /p choice="请选择操作 (1/a/h/v/q): "

if /i "%choice%"=="1" (
    echo 正在打开Web应用...
    start http://localhost:9082
) else if /i "%choice%"=="a" (
    echo 正在打开API文档...
    start http://localhost:9081/swagger
) else if /i "%choice%"=="h" (
    echo 正在打开健康检查...
    start http://localhost:9081/health
) else if /i "%choice%"=="v" (
    echo 正在打开VS2022...
    start InfoCardSystem.VS2022.sln
) else if /i "%choice%"=="q" (
    echo 退出启动器...
    exit /b 0
) else (
    echo 正在打开Web应用...
    start http://localhost:9082
)

echo.
echo 🎯 模拟IIS端口服务已就绪！
echo 💡 提示: 关闭此窗口不会停止服务，服务将继续在后台运行
echo 🔄 现在可以在VS2022中测试发布功能
echo.
pause
