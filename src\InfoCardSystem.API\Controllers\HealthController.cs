using InfoCardSystem.Core.DTOs.Common;
using InfoCardSystem.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace InfoCardSystem.API.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<HealthController> _logger;

    public HealthController(IUnitOfWork unitOfWork, ILogger<HealthController> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    /// <summary>
    /// 基础健康检查
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<object>>> GetHealth()
    {
        try
        {
            // 检查数据库连接
            var userCount = await _unitOfWork.Users.CountAsync();
            
            var healthInfo = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0",
                Database = new
                {
                    Status = "Connected",
                    UserCount = userCount
                },
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
            };

            _logger.LogInformation("Health check performed successfully");
            
            return Ok(ApiResponse<object>.SuccessResult(healthInfo, "系统运行正常"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            
            var errorInfo = new
            {
                Status = "Unhealthy",
                Timestamp = DateTime.UtcNow,
                Error = ex.Message
            };
            
            return StatusCode(500, ApiResponse<object>.ErrorResult("系统健康检查失败", "HEALTH_CHECK_FAILED", errorInfo));
        }
    }

    /// <summary>
    /// 数据库连接检查
    /// </summary>
    /// <returns>数据库状态</returns>
    [HttpGet("database")]
    public async Task<ActionResult<ApiResponse<object>>> GetDatabaseHealth()
    {
        try
        {
            var userCount = await _unitOfWork.Users.CountAsync();
            var friendshipCount = await _unitOfWork.Friendships.CountAsync();
            var infoCardCount = await _unitOfWork.InfoCards.CountAsync();
            
            var dbInfo = new
            {
                Status = "Connected",
                Statistics = new
                {
                    Users = userCount,
                    Friendships = friendshipCount,
                    InfoCards = infoCardCount
                },
                Timestamp = DateTime.UtcNow
            };

            return Ok(ApiResponse<object>.SuccessResult(dbInfo, "数据库连接正常"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database health check failed");
            return StatusCode(500, ApiResponse<object>.ErrorResult("数据库连接失败", "DATABASE_ERROR", ex.Message));
        }
    }
}
