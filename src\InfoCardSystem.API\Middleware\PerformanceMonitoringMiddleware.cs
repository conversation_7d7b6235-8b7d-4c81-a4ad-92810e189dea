using System.Diagnostics;

namespace InfoCardSystem.API.Middleware;

/// <summary>
/// 性能监控中间件
/// </summary>
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;
    private readonly long _slowRequestThresholdMs;

    public PerformanceMonitoringMiddleware(
        RequestDelegate next, 
        ILogger<PerformanceMonitoringMiddleware> logger,
        IConfiguration configuration)
    {
        _next = next;
        _logger = logger;
        _slowRequestThresholdMs = configuration.GetValue<long>("Performance:SlowRequestThresholdMs", 1000);
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestPath = context.Request.Path.Value;
        var requestMethod = context.Request.Method;
        
        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var elapsedMs = stopwatch.ElapsedMilliseconds;
            
            // 记录性能指标
            LogPerformanceMetrics(context, elapsedMs, requestPath, requestMethod);
            
            // 慢请求警告
            if (elapsedMs > _slowRequestThresholdMs)
            {
                _logger.LogWarning("慢请求检测: {Method} {Path} 耗时 {ElapsedMs}ms，超过阈值 {ThresholdMs}ms",
                    requestMethod, requestPath, elapsedMs, _slowRequestThresholdMs);
            }
        }
    }

    private void LogPerformanceMetrics(HttpContext context, long elapsedMs, string? requestPath, string requestMethod)
    {
        var statusCode = context.Response.StatusCode;
        var userId = context.User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["RequestId"] = context.TraceIdentifier,
            ["UserId"] = userId ?? "Anonymous",
            ["Method"] = requestMethod,
            ["Path"] = requestPath ?? "Unknown",
            ["StatusCode"] = statusCode,
            ["ElapsedMs"] = elapsedMs,
            ["Timestamp"] = DateTime.UtcNow
        });

        var logLevel = GetLogLevel(elapsedMs, statusCode);
        _logger.Log(logLevel, "请求性能: {Method} {Path} - {StatusCode} ({ElapsedMs}ms)",
            requestMethod, requestPath, statusCode, elapsedMs);
    }

    private LogLevel GetLogLevel(long elapsedMs, int statusCode)
    {
        // 错误状态码
        if (statusCode >= 500)
            return LogLevel.Error;
        
        if (statusCode >= 400)
            return LogLevel.Warning;
        
        // 基于响应时间
        if (elapsedMs > _slowRequestThresholdMs * 2)
            return LogLevel.Warning;
        
        if (elapsedMs > _slowRequestThresholdMs)
            return LogLevel.Information;
        
        return LogLevel.Debug;
    }
}

/// <summary>
/// 性能监控中间件扩展
/// </summary>
public static class PerformanceMonitoringMiddlewareExtensions
{
    public static IApplicationBuilder UsePerformanceMonitoring(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PerformanceMonitoringMiddleware>();
    }
}
