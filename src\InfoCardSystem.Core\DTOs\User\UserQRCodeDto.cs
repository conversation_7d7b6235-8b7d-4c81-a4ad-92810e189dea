namespace InfoCardSystem.Core.DTOs.User;

/// <summary>
/// 用户二维码DTO
/// </summary>
public class UserQRCodeDto
{
    /// <summary>
    /// 二维码图片URL
    /// </summary>
    public string QRCodeUrl { get; set; } = string.Empty;

    /// <summary>
    /// 二维码数据内容
    /// </summary>
    public string QRCodeData { get; set; } = string.Empty;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public UserProfileDto User { get; set; } = new();
}
