using System.Text.Json.Serialization;

namespace InfoCardSystem.Mobile.Models;

/// <summary>
/// 好友请求
/// </summary>
public class FriendRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// 请求者信息
    /// </summary>
    [JsonPropertyName("requester")]
    public UserInfo Requester { get; set; } = new();

    /// <summary>
    /// 接收者信息
    /// </summary>
    [JsonPropertyName("receiver")]
    public UserInfo Receiver { get; set; } = new();

    /// <summary>
    /// 请求消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 请求状态
    /// </summary>
    [JsonPropertyName("status")]
    public FriendRequestStatus Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    [JsonPropertyName("processedAt")]
    public DateTime? ProcessedAt { get; set; }
}

/// <summary>
/// 好友请求状态
/// </summary>
public enum FriendRequestStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 已接受
    /// </summary>
    Accepted = 1,

    /// <summary>
    /// 已拒绝
    /// </summary>
    Rejected = 2,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 3
}

/// <summary>
/// 好友信息
/// </summary>
public class FriendInfo
{
    /// <summary>
    /// 好友关系ID
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// 好友用户信息
    /// </summary>
    [JsonPropertyName("friend")]
    public UserInfo Friend { get; set; } = new();

    /// <summary>
    /// 好友别名
    /// </summary>
    [JsonPropertyName("alias")]
    public string? Alias { get; set; }

    /// <summary>
    /// 添加时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 是否被屏蔽
    /// </summary>
    [JsonPropertyName("isBlocked")]
    public bool IsBlocked { get; set; }

    /// <summary>
    /// 显示名称（别名优先，否则显示用户名）
    /// </summary>
    public string DisplayName => !string.IsNullOrEmpty(Alias) ? Alias : 
                                !string.IsNullOrEmpty(Friend.DisplayName) ? Friend.DisplayName : 
                                Friend.Username;

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl => Friend.AvatarUrl;
}

/// <summary>
/// 添加好友请求
/// </summary>
public class AddFriendRequest
{
    /// <summary>
    /// 目标用户ID
    /// </summary>
    [JsonPropertyName("targetUserId")]
    public int TargetUserId { get; set; }

    /// <summary>
    /// 请求消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// 处理好友请求
/// </summary>
public class ProcessFriendRequestRequest
{
    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("requestId")]
    public int RequestId { get; set; }

    /// <summary>
    /// 是否接受
    /// </summary>
    [JsonPropertyName("accept")]
    public bool Accept { get; set; }

    /// <summary>
    /// 好友别名（接受时可设置）
    /// </summary>
    [JsonPropertyName("alias")]
    public string? Alias { get; set; }
}

/// <summary>
/// 搜索用户请求
/// </summary>
public class SearchUsersRequest
{
    /// <summary>
    /// 搜索关键词
    /// </summary>
    [JsonPropertyName("keyword")]
    public string Keyword { get; set; } = string.Empty;

    /// <summary>
    /// 搜索类型
    /// </summary>
    [JsonPropertyName("searchType")]
    public UserSearchType SearchType { get; set; } = UserSearchType.All;

    /// <summary>
    /// 页码
    /// </summary>
    [JsonPropertyName("page")]
    public int Page { get; set; } = 1;

    /// <summary>
    /// 每页数量
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 20;
}

/// <summary>
/// 用户搜索类型
/// </summary>
public enum UserSearchType
{
    /// <summary>
    /// 全部
    /// </summary>
    All = 0,

    /// <summary>
    /// 用户名
    /// </summary>
    Username = 1,

    /// <summary>
    /// 邮箱
    /// </summary>
    Email = 2,

    /// <summary>
    /// 手机号
    /// </summary>
    Phone = 3,

    /// <summary>
    /// 自定义ID
    /// </summary>
    CustomId = 4
}
