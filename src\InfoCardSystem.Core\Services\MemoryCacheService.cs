using InfoCardSystem.Core.Interfaces;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace InfoCardSystem.Core.Services;

/// <summary>
/// 内存缓存服务实现
/// </summary>
public class MemoryCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<MemoryCacheService> _logger;
    private readonly ConcurrentDictionary<string, bool> _cacheKeys;
    private readonly TimeSpan _defaultExpiration;

    public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _cacheKeys = new ConcurrentDictionary<string, bool>();
        _defaultExpiration = TimeSpan.FromMinutes(30); // 默认30分钟过期
    }

    /// <summary>
    /// 获取缓存值
    /// </summary>
    public Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var cachedValue))
            {
                if (cachedValue is string jsonString)
                {
                    var value = JsonSerializer.Deserialize<T>(jsonString);
                    _logger.LogDebug("缓存命中: {Key}", key);
                    return Task.FromResult(value);
                }
                
                if (cachedValue is T directValue)
                {
                    _logger.LogDebug("缓存命中: {Key}", key);
                    return Task.FromResult<T?>(directValue);
                }
            }

            _logger.LogDebug("缓存未命中: {Key}", key);
            return Task.FromResult<T?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存失败: {Key}", key);
            return Task.FromResult<T?>(null);
        }
    }

    /// <summary>
    /// 设置缓存值
    /// </summary>
    public Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration ?? _defaultExpiration,
                Priority = CacheItemPriority.Normal
            };

            // 注册移除回调
            options.RegisterPostEvictionCallback((evictedKey, evictedValue, reason, state) =>
            {
                _cacheKeys.TryRemove(evictedKey.ToString() ?? string.Empty, out _);
                _logger.LogDebug("缓存移除: {Key}, 原因: {Reason}", evictedKey, reason);
            });

            // 序列化复杂对象
            object cacheValue = typeof(T) == typeof(string) ? (object)value : JsonSerializer.Serialize(value);
            
            _memoryCache.Set(key, cacheValue, options);
            _cacheKeys.TryAdd(key, true);
            
            _logger.LogDebug("缓存设置: {Key}, 过期时间: {Expiration}", key, expiration ?? _defaultExpiration);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置缓存失败: {Key}", key);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 移除缓存
    /// </summary>
    public Task<bool> RemoveAsync(string key)
    {
        try
        {
            _memoryCache.Remove(key);
            _cacheKeys.TryRemove(key, out _);
            
            _logger.LogDebug("缓存移除: {Key}", key);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除缓存失败: {Key}", key);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 移除匹配模式的缓存
    /// </summary>
    public Task<int> RemoveByPatternAsync(string pattern)
    {
        try
        {
            var regex = new Regex(pattern, RegexOptions.IgnoreCase);
            var keysToRemove = _cacheKeys.Keys.Where(key => regex.IsMatch(key)).ToList();
            
            var removedCount = 0;
            foreach (var key in keysToRemove)
            {
                _memoryCache.Remove(key);
                _cacheKeys.TryRemove(key, out _);
                removedCount++;
            }
            
            _logger.LogDebug("按模式移除缓存: {Pattern}, 移除数量: {Count}", pattern, removedCount);
            return Task.FromResult(removedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按模式移除缓存失败: {Pattern}", pattern);
            return Task.FromResult(0);
        }
    }

    /// <summary>
    /// 检查缓存是否存在
    /// </summary>
    public Task<bool> ExistsAsync(string key)
    {
        var exists = _cacheKeys.ContainsKey(key);
        return Task.FromResult(exists);
    }

    /// <summary>
    /// 获取或设置缓存
    /// </summary>
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiration = null) where T : class
    {
        var cachedValue = await GetAsync<T>(key);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        var value = await factory();
        if (value != null)
        {
            await SetAsync(key, value, expiration);
        }

        return value;
    }
}
